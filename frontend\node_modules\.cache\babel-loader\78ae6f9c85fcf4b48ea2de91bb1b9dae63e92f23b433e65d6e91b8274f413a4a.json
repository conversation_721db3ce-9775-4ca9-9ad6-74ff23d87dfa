{"ast": null, "code": "/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M5 7 3 5\",\n  key: \"1yys58\"\n}], [\"path\", {\n  d: \"M9 6V3\",\n  key: \"1ptz9u\"\n}], [\"path\", {\n  d: \"m13 7 2-2\",\n  key: \"1w3vmq\"\n}], [\"circle\", {\n  cx: \"9\",\n  cy: \"13\",\n  r: \"3\",\n  key: \"1mma13\"\n}], [\"path\", {\n  d: \"M11.83 12H20a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-4a2 2 0 0 1 2-2h2.17\",\n  key: \"2frwzc\"\n}], [\"path\", {\n  d: \"M16 16h2\",\n  key: \"dnq2od\"\n}]];\nconst Projector = createLucideIcon(\"projector\", __iconNode);\nexport { __iconNode, Projector as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "r", "Projector", "createLucideIcon"], "sources": ["D:\\techvritti\\Collegemanagement\\frontend\\node_modules\\lucide-react\\src\\icons\\projector.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 7 3 5', key: '1yys58' }],\n  ['path', { d: 'M9 6V3', key: '1ptz9u' }],\n  ['path', { d: 'm13 7 2-2', key: '1w3vmq' }],\n  ['circle', { cx: '9', cy: '13', r: '3', key: '1mma13' }],\n  [\n    'path',\n    {\n      d: 'M11.83 12H20a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-4a2 2 0 0 1 2-2h2.17',\n      key: '2frwzc',\n    },\n  ],\n  ['path', { d: 'M16 16h2', key: 'dnq2od' }],\n];\n\n/**\n * @component @name Projector\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSA3IDMgNSIgLz4KICA8cGF0aCBkPSJNOSA2VjMiIC8+CiAgPHBhdGggZD0ibTEzIDcgMi0yIiAvPgogIDxjaXJjbGUgY3g9IjkiIGN5PSIxMyIgcj0iMyIgLz4KICA8cGF0aCBkPSJNMTEuODMgMTJIMjBhMiAyIDAgMCAxIDIgMnY0YTIgMiAwIDAgMS0yIDJINGEyIDIgMCAwIDEtMi0ydi00YTIgMiAwIDAgMSAyLTJoMi4xNyIgLz4KICA8cGF0aCBkPSJNMTYgMTZoMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/projector\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Projector = createLucideIcon('projector', __iconNode);\n\nexport default Projector;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAYC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAUC,GAAA,EAAK;AAAA,CAAU,GACvC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAaC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,UAAU;EAAEC,EAAA,EAAI;EAAKC,EAAA,EAAI;EAAMC,CAAA,EAAG;EAAKH,GAAA,EAAK;AAAA,CAAU,GACvD,CACE,QACA;EACED,CAAA,EAAG;EACHC,GAAA,EAAK;AAAA,EAET,EACA,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAYC,GAAA,EAAK;AAAA,CAAU,EAC3C;AAaA,MAAMI,SAAA,GAAYC,gBAAA,CAAiB,aAAaP,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m2 8 2 2-2 2 2 2-2 2\",\n  key: \"sv1b1\"\n}], [\"path\", {\n  d: \"m22 8-2 2 2 2-2 2 2 2\",\n  key: \"101i4y\"\n}], [\"path\", {\n  d: \"M8 8v10c0 .55.45 1 1 1h6c.55 0 1-.45 1-1v-2\",\n  key: \"1hbad5\"\n}], [\"path\", {\n  d: \"M16 10.34V6c0-.55-.45-1-1-1h-4.34\",\n  key: \"1x5tf0\"\n}], [\"line\", {\n  x1: \"2\",\n  x2: \"22\",\n  y1: \"2\",\n  y2: \"22\",\n  key: \"a6p6uj\"\n}]];\nconst VibrateOff = createLucideIcon(\"vibrate-off\", __iconNode);\nexport { __iconNode, VibrateOff as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "x1", "x2", "y1", "y2", "VibrateOff", "createLucideIcon"], "sources": ["D:\\techvritti\\Collegemanagement\\frontend\\node_modules\\lucide-react\\src\\icons\\vibrate-off.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm2 8 2 2-2 2 2 2-2 2', key: 'sv1b1' }],\n  ['path', { d: 'm22 8-2 2 2 2-2 2 2 2', key: '101i4y' }],\n  ['path', { d: 'M8 8v10c0 .55.45 1 1 1h6c.55 0 1-.45 1-1v-2', key: '1hbad5' }],\n  ['path', { d: 'M16 10.34V6c0-.55-.45-1-1-1h-4.34', key: '1x5tf0' }],\n  ['line', { x1: '2', x2: '22', y1: '2', y2: '22', key: 'a6p6uj' }],\n];\n\n/**\n * @component @name VibrateOff\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMiA4IDIgMi0yIDIgMiAyLTIgMiIgLz4KICA8cGF0aCBkPSJtMjIgOC0yIDIgMiAyLTIgMiAyIDIiIC8+CiAgPHBhdGggZD0iTTggOHYxMGMwIC41NS40NSAxIDEgMWg2Yy41NSAwIDEtLjQ1IDEtMXYtMiIgLz4KICA8cGF0aCBkPSJNMTYgMTAuMzRWNmMwLS41NS0uNDUtMS0xLTFoLTQuMzQiIC8+CiAgPGxpbmUgeDE9IjIiIHgyPSIyMiIgeTE9IjIiIHkyPSIyMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/vibrate-off\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst VibrateOff = createLucideIcon('vibrate-off', __iconNode);\n\nexport default VibrateOff;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAwBC,GAAA,EAAK;AAAA,CAAS,GACpD,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAyBC,GAAA,EAAK;AAAA,CAAU,GACtD,CAAC,QAAQ;EAAED,CAAA,EAAG;EAA+CC,GAAA,EAAK;AAAA,CAAU,GAC5E,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAqCC,GAAA,EAAK;AAAA,CAAU,GAClE,CAAC,QAAQ;EAAEC,EAAA,EAAI;EAAKC,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAKC,EAAA,EAAI;EAAMJ,GAAA,EAAK;AAAA,CAAU,EAClE;AAaA,MAAMK,UAAA,GAAaC,gBAAA,CAAiB,eAAeR,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
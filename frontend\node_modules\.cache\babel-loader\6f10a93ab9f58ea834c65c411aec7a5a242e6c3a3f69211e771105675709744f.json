{"ast": null, "code": "/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M11.636 6A13 13 0 0 0 19.4 3.2 1 1 0 0 1 21 4v11.344\",\n  key: \"bycexp\"\n}], [\"path\", {\n  d: \"M14.378 14.357A13 13 0 0 0 11 14H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h1\",\n  key: \"1t17s6\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"M6 14a12 12 0 0 0 2.4 7.2 2 2 0 0 0 3.2-2.4A8 8 0 0 1 10 14\",\n  key: \"1853fq\"\n}], [\"path\", {\n  d: \"M8 8v6\",\n  key: \"aieo6v\"\n}]];\nconst MegaphoneOff = createLucideIcon(\"megaphone-off\", __iconNode);\nexport { __iconNode, MegaphoneOff as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "MegaphoneOff", "createLucideIcon"], "sources": ["D:\\techvritti\\Collegemanagement\\frontend\\node_modules\\lucide-react\\src\\icons\\megaphone-off.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M11.636 6A13 13 0 0 0 19.4 3.2 1 1 0 0 1 21 4v11.344', key: 'bycexp' }],\n  [\n    'path',\n    { d: 'M14.378 14.357A13 13 0 0 0 11 14H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h1', key: '1t17s6' },\n  ],\n  ['path', { d: 'm2 2 20 20', key: '1ooewy' }],\n  ['path', { d: 'M6 14a12 12 0 0 0 2.4 7.2 2 2 0 0 0 3.2-2.4A8 8 0 0 1 10 14', key: '1853fq' }],\n  ['path', { d: 'M8 8v6', key: 'aieo6v' }],\n];\n\n/**\n * @component @name MegaphoneOff\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTEuNjM2IDZBMTMgMTMgMCAwIDAgMTkuNCAzLjIgMSAxIDAgMCAxIDIxIDR2MTEuMzQ0IiAvPgogIDxwYXRoIGQ9Ik0xNC4zNzggMTQuMzU3QTEzIDEzIDAgMCAwIDExIDE0SDVhMiAyIDAgMCAxLTItMlY4YTIgMiAwIDAgMSAyLTJoMSIgLz4KICA8cGF0aCBkPSJtMiAyIDIwIDIwIiAvPgogIDxwYXRoIGQ9Ik02IDE0YTEyIDEyIDAgMCAwIDIuNCA3LjIgMiAyIDAgMCAwIDMuMi0yLjRBOCA4IDAgMCAxIDEwIDE0IiAvPgogIDxwYXRoIGQ9Ik04IDh2NiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/megaphone-off\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MegaphoneOff = createLucideIcon('megaphone-off', __iconNode);\n\nexport default MegaphoneOff;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAwDC,GAAA,EAAK;AAAA,CAAU,GACrF,CACE,QACA;EAAED,CAAA,EAAG;EAAsEC,GAAA,EAAK;AAAA,EAClF,EACA,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAcC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAA+DC,GAAA,EAAK;AAAA,CAAU,GAC5F,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAUC,GAAA,EAAK;AAAA,CAAU,EACzC;AAaA,MAAMC,YAAA,GAAeC,gBAAA,CAAiB,iBAAiBJ,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
import React, { useState, useEffect } from "react";
import {
  FiMenu,
  FiX,
  FiHome,
  FiBook,
  FiCalendar,
  FiUser,
  FiAward,
  FiLogOut,
  FiCheckCircle,
} from "react-icons/fi";
import { useNavigate, Link, useLocation } from "react-router-dom";

const StudentLayout = ({ children }) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const navigate = useNavigate();
  const location = useLocation();

  const menuItems = [
    { icon: <FiHome size={20} />, label: "Dashboard", path: "/student" },
    { icon: <FiBook size={20} />, label: "My Courses", path: "/student/courses" },
    { icon: <FiCalendar size={20} />, label: "Schedule", path: "/student/schedule" },
    { icon: <FiCheckCircle size={20} />, label: "Attendance", path: "/student/attendance" },
    { icon: <FiAward size={20} />, label: "Certificate", path: "/student/certificate" },
    { icon: <FiUser size={20} />, label: "Profile", path: "/student/profile" },
  ];

  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
      if (!mobile) {
        setIsSidebarOpen(true);
      } else {
        setIsSidebarOpen(false);
      }
    };
    window.addEventListener("resize", handleResize);
    handleResize();
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const handleLogout = () => {
    localStorage.clear();
    navigate("/login");
    if (isMobile) setIsSidebarOpen(false);
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isMobile && isSidebarOpen && !event.target.closest("aside")) {
        setIsSidebarOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [isMobile, isSidebarOpen]);

  return (
    <div className="flex min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Mobile Overlay */}
      {isMobile && isSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-70 z-40"
          onClick={() => setIsSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <aside
        className={`fixed top-0 left-0 bottom-0 w-64 bg-gradient-to-b from-gray-900 to-gray-800 shadow-2xl z-50 transform transition-all duration-300 overflow-y-auto
        ${isSidebarOpen ? "translate-x-0" : "-translate-x-64"} md:translate-x-0`}
      >
        {/* Sidebar Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700/50">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-[#29354d] rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">M</span>
            </div>
            <div className="text-white">
              <h3 className="font-bold text-lg">Medini</h3>
              <p className="text-xs text-gray-300">Student Portal</p>
            </div>
          </div>
          <button
            className="md:hidden text-gray-300 hover:text-white hover:bg-white/10 p-2 rounded-lg transition-all duration-200"
            onClick={() => setIsSidebarOpen(false)}
          >
            <FiX size={20} />
          </button>
        </div>

        {/* Menu Items */}
        <nav className="p-4">
          <ul className="space-y-2">
            {menuItems.map((item) => (
              <li key={item.path}>
                <Link
                  to={item.path}
                  onClick={() => isMobile && setIsSidebarOpen(false)}
                  className={`flex items-center px-4 py-3 rounded-xl transition-all duration-200 group
                    ${location.pathname === item.path
                    ? "bg-[#29354d] text-white shadow-lg transform scale-105"
                      : "text-gray-300 hover:bg-white/10 hover:text-white hover:transform hover:scale-105"
                    }`}
                >
                  <span className="mr-3 transition-transform group-hover:scale-110">{item.icon}</span>
                  <span className="font-medium">{item.label}</span>
                </Link>
              </li>
            ))}
          </ul>
        </nav>

        {/* Logout Button */}
        <div className="absolute bottom-0 left-0 right-0 border-t border-gray-700/50 p-4">
          <button
            onClick={handleLogout}
            className="flex items-center w-full px-4 py-3 rounded-xl text-gray-300 hover:bg-red-600/20 hover:text-red-400 transition-all duration-200 group"
          >
            <FiLogOut className="mr-3 transition-transform group-hover:scale-110" />
            <span className="font-medium">Logout</span>
          </button>
        </div>
      </aside>

      {/* Main Content Area */}
      <main
        className={`flex-1 transition-all duration-300 ${isMobile ? "ml-0" : "ml-64"} min-h-screen flex flex-col`}
      >
        {/* Top Bar for Mobile */}
        <div className="md:hidden bg-white shadow-sm border-b px-4 py-3 flex items-center justify-between sticky top-0 z-30">
          <button
            onClick={() => setIsSidebarOpen(true)}
            className="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors"
          >
            <FiMenu size={20} className="text-gray-700" />
          </button>
          <h1 className="font-semibold text-gray-800">Student Portal</h1>
          <div className="w-8"></div>
        </div>

        {/* Content Container - Centered */}
        <div className="flex-1 flex items-center justify-center p-4 md:p-6 lg:p-8">
          <div className="w-full max-w-7xl">
            {children}
          </div>
        </div>
      </main>

      {/* Floating Mobile Menu Button */}
      {isMobile && !isSidebarOpen && (
        <button
          className="fixed bottom-6 right-6 bg-blue-600 text-white p-4 rounded-full shadow-2xl z-40 hover:bg-blue-700 transition-all duration-200 hover:scale-110"
          onClick={() => setIsSidebarOpen(true)}
        >
          <FiMenu size={24} />
        </button>
      )}
    </div>
  );
};

export default StudentLayout;
{"ast": null, "code": "/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 2h4\",\n  key: \"n1abiw\"\n}], [\"path\", {\n  d: \"M12 14v-4\",\n  key: \"1evpnu\"\n}], [\"path\", {\n  d: \"M4 13a8 8 0 0 1 8-7 8 8 0 1 1-5.3 14L4 17.6\",\n  key: \"1ts96g\"\n}], [\"path\", {\n  d: \"M9 17H4v5\",\n  key: \"8t5av\"\n}]];\nconst TimerReset = createLucideIcon(\"timer-reset\", __iconNode);\nexport { __iconNode, TimerReset as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "<PERSON>r<PERSON><PERSON><PERSON>", "createLucideIcon"], "sources": ["D:\\techvritti\\Collegemanagement\\frontend\\node_modules\\lucide-react\\src\\icons\\timer-reset.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M10 2h4', key: 'n1abiw' }],\n  ['path', { d: 'M12 14v-4', key: '1evpnu' }],\n  ['path', { d: 'M4 13a8 8 0 0 1 8-7 8 8 0 1 1-5.3 14L4 17.6', key: '1ts96g' }],\n  ['path', { d: 'M9 17H4v5', key: '8t5av' }],\n];\n\n/**\n * @component @name TimerReset\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMmg0IiAvPgogIDxwYXRoIGQ9Ik0xMiAxNHYtNCIgLz4KICA8cGF0aCBkPSJNNCAxM2E4IDggMCAwIDEgOC03IDggOCAwIDEgMS01LjMgMTRMNCAxNy42IiAvPgogIDxwYXRoIGQ9Ik05IDE3SDR2NSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/timer-reset\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TimerReset = createLucideIcon('timer-reset', __iconNode);\n\nexport default TimerReset;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAWC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAaC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAA+CC,GAAA,EAAK;AAAA,CAAU,GAC5E,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAaC,GAAA,EAAK;AAAA,CAAS,EAC3C;AAaA,MAAMC,UAAA,GAAaC,gBAAA,CAAiB,eAAeJ,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10.029 4.285A2 2 0 0 0 7 6v12a2 2 0 0 0 3.029 1.715l9.997-5.998a2 2 0 0 0 .003-3.432z\",\n  key: \"1ystz2\"\n}], [\"path\", {\n  d: \"M3 4v16\",\n  key: \"1ph11n\"\n}]];\nconst StepForward = createLucideIcon(\"step-forward\", __iconNode);\nexport { __iconNode, StepForward as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "StepForward", "createLucideIcon"], "sources": ["D:\\techvritti\\Collegemanagement\\frontend\\node_modules\\lucide-react\\src\\icons\\step-forward.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M10.029 4.285A2 2 0 0 0 7 6v12a2 2 0 0 0 3.029 1.715l9.997-5.998a2 2 0 0 0 .003-3.432z',\n      key: '1ystz2',\n    },\n  ],\n  ['path', { d: 'M3 4v16', key: '1ph11n' }],\n];\n\n/**\n * @component @name StepForward\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAuMDI5IDQuMjg1QTIgMiAwIDAgMCA3IDZ2MTJhMiAyIDAgMCAwIDMuMDI5IDEuNzE1bDkuOTk3LTUuOTk4YTIgMiAwIDAgMCAuMDAzLTMuNDMyeiIgLz4KICA8cGF0aCBkPSJNMyA0djE2IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/step-forward\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst StepForward = createLucideIcon('step-forward', __iconNode);\n\nexport default StepForward;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CACE,QACA;EACEC,CAAA,EAAG;EACHC,GAAA,EAAK;AAAA,EAET,EACA,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAWC,GAAA,EAAK;AAAA,CAAU,EAC1C;AAaA,MAAMC,WAAA,GAAcC,gBAAA,CAAiB,gBAAgBJ,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
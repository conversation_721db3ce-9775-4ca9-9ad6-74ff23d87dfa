{"ast": null, "code": "/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m15 15 6 6m-6-6v4.8m0-4.8h4.8\",\n  key: \"17vawe\"\n}], [\"path\", {\n  d: \"M9 19.8V15m0 0H4.2M9 15l-6 6\",\n  key: \"chjx8e\"\n}], [\"path\", {\n  d: \"M15 4.2V9m0 0h4.8M15 9l6-6\",\n  key: \"lav6yq\"\n}], [\"path\", {\n  d: \"M9 4.2V9m0 0H4.2M9 9 3 3\",\n  key: \"1pxi2q\"\n}]];\nconst Shrink = createLucideIcon(\"shrink\", __iconNode);\nexport { __iconNode, Shrink as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Shrink", "createLucideIcon"], "sources": ["D:\\techvritti\\Collegemanagement\\frontend\\node_modules\\lucide-react\\src\\icons\\shrink.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm15 15 6 6m-6-6v4.8m0-4.8h4.8', key: '17vawe' }],\n  ['path', { d: 'M9 19.8V15m0 0H4.2M9 15l-6 6', key: 'chjx8e' }],\n  ['path', { d: 'M15 4.2V9m0 0h4.8M15 9l6-6', key: 'lav6yq' }],\n  ['path', { d: 'M9 4.2V9m0 0H4.2M9 9 3 3', key: '1pxi2q' }],\n];\n\n/**\n * @component @name Shrink\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTUgMTUgNiA2bS02LTZ2NC44bTAtNC44aDQuOCIgLz4KICA8cGF0aCBkPSJNOSAxOS44VjE1bTAgMEg0LjJNOSAxNWwtNiA2IiAvPgogIDxwYXRoIGQ9Ik0xNSA0LjJWOW0wIDBoNC44TTE1IDlsNi02IiAvPgogIDxwYXRoIGQ9Ik05IDQuMlY5bTAgMEg0LjJNOSA5IDMgMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/shrink\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Shrink = createLucideIcon('shrink', __iconNode);\n\nexport default Shrink;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAiCC,GAAA,EAAK;AAAA,CAAU,GAC9D,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAgCC,GAAA,EAAK;AAAA,CAAU,GAC7D,CAAC,QAAQ;EAAED,CAAA,EAAG;EAA8BC,GAAA,EAAK;AAAA,CAAU,GAC3D,CAAC,QAAQ;EAAED,CAAA,EAAG;EAA4BC,GAAA,EAAK;AAAA,CAAU,EAC3D;AAaA,MAAMC,MAAA,GAASC,gBAAA,CAAiB,UAAUJ,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
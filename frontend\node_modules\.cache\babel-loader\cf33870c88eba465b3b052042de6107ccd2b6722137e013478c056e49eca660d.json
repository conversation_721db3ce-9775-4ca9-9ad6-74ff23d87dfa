{"ast": null, "code": "var _jsxFileName = \"D:\\\\techvritti\\\\Collegemanagement\\\\frontend\\\\src\\\\Screens\\\\Student\\\\AttendancePage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Calendar, Users, BookOpen, GraduationCap, Filter, Download, TrendingUp, Clock, CheckCircle, XCircle, AlertCircle, Info } from 'lucide-react';\nimport 'react-calendar/dist/Calendar.css';\nimport CalendarView from 'react-calendar';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AttendancePage = () => {\n  _s();\n  const [activeView, setActiveView] = useState('overall');\n  const [selectedBatch, setSelectedBatch] = useState('all');\n  const [selectedCourse, setSelectedCourse] = useState('all');\n  const [selectedSubject, setSelectedSubject] = useState('all');\n  const [dateRange, setDateRange] = useState('thisMonth');\n  const [selectedDate, setSelectedDate] = useState(new Date());\n  const [attendanceData] = useState({\n    overall: {\n      present: 85,\n      absent: 15,\n      total: 100\n    },\n    subjects: [{\n      name: 'Mathematics',\n      present: 18,\n      total: 20,\n      percentage: 90\n    }, {\n      name: 'Physics',\n      present: 16,\n      total: 18,\n      percentage: 89\n    }, {\n      name: 'Chemistry',\n      present: 14,\n      total: 16,\n      percentage: 88\n    }, {\n      name: 'Computer Science',\n      present: 19,\n      total: 22,\n      percentage: 86\n    }, {\n      name: 'English',\n      present: 15,\n      total: 18,\n      percentage: 83\n    }],\n    batches: [{\n      name: 'Batch A',\n      students: 45,\n      avgAttendance: 87\n    }, {\n      name: 'Batch B',\n      students: 42,\n      avgAttendance: 85\n    }, {\n      name: 'Batch C',\n      students: 48,\n      avgAttendance: 89\n    }],\n    courses: [{\n      name: 'Engineering',\n      students: 135,\n      avgAttendance: 87\n    }, {\n      name: 'Science',\n      students: 98,\n      avgAttendance: 85\n    }, {\n      name: 'Commerce',\n      students: 76,\n      avgAttendance: 91\n    }],\n    recentActivity: [{\n      date: '2024-03-15',\n      subject: 'Mathematics',\n      status: 'present',\n      time: '09:00 AM'\n    }, {\n      date: '2024-03-15',\n      subject: 'Physics',\n      status: 'present',\n      time: '10:30 AM'\n    }, {\n      date: '2024-03-14',\n      subject: 'Chemistry',\n      status: 'absent',\n      time: '02:00 PM'\n    }, {\n      date: '2024-03-14',\n      subject: 'Computer Science',\n      status: 'present',\n      time: '03:30 PM'\n    }, {\n      date: '2024-03-13',\n      subject: 'English',\n      status: 'present',\n      time: '11:00 AM'\n    }],\n    // Enhanced daily attendance records for calendar view\n    dailyRecords: [\n    // January 2024 - Engineering Course\n    {\n      date: '2024-01-15',\n      subject: 'Mathematics',\n      status: 'Present',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-15',\n      subject: 'Physics',\n      status: 'Present',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-16',\n      subject: 'Chemistry',\n      status: 'Absent',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-16',\n      subject: 'Mathematics',\n      status: 'Present',\n      course: 'Engineering',\n      batch: 'Batch B',\n      type: 'class'\n    }, {\n      date: '2024-01-17',\n      subject: 'English',\n      status: 'Present',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-18',\n      subject: 'Computer Science',\n      status: 'Absent',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-19',\n      subject: 'Physics',\n      status: 'Present',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-20',\n      subject: 'Holiday',\n      status: 'Holiday',\n      course: 'all',\n      batch: 'all',\n      type: 'holiday'\n    }, {\n      date: '2024-01-21',\n      subject: 'Holiday',\n      status: 'Holiday',\n      course: 'all',\n      batch: 'all',\n      type: 'holiday'\n    }, {\n      date: '2024-01-22',\n      subject: 'Mathematics',\n      status: 'Present',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-23',\n      subject: 'Chemistry',\n      status: 'Present',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-24',\n      subject: 'English',\n      status: 'Absent',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-25',\n      subject: 'Computer Science',\n      status: 'Present',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-26',\n      subject: 'Physics',\n      status: 'Present',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-29',\n      subject: 'Mathematics',\n      status: 'Present',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-30',\n      subject: 'Chemistry',\n      status: 'Absent',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-31',\n      subject: 'English',\n      status: 'Present',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    },\n    // Science course records\n    {\n      date: '2024-01-15',\n      subject: 'Biology',\n      status: 'Present',\n      course: 'Science',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-16',\n      subject: 'Chemistry',\n      status: 'Present',\n      course: 'Science',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-17',\n      subject: 'Physics',\n      status: 'Absent',\n      course: 'Science',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-18',\n      subject: 'Mathematics',\n      status: 'Present',\n      course: 'Science',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-19',\n      subject: 'Biology',\n      status: 'Present',\n      course: 'Science',\n      batch: 'Batch A',\n      type: 'class'\n    },\n    // Commerce course records\n    {\n      date: '2024-01-15',\n      subject: 'Accounting',\n      status: 'Present',\n      course: 'Commerce',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-16',\n      subject: 'Economics',\n      status: 'Present',\n      course: 'Commerce',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-17',\n      subject: 'Business Studies',\n      status: 'Present',\n      course: 'Commerce',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-18',\n      subject: 'Accounting',\n      status: 'Absent',\n      course: 'Commerce',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-19',\n      subject: 'Economics',\n      status: 'Present',\n      course: 'Commerce',\n      batch: 'Batch A',\n      type: 'class'\n    }]\n  });\n  const getStatusIcon = status => {\n    switch (status) {\n      case 'present':\n        return /*#__PURE__*/_jsxDEV(CheckCircle, {\n          className: \"w-4 h-4 text-green-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 36\n        }, this);\n      case 'absent':\n        return /*#__PURE__*/_jsxDEV(XCircle, {\n          className: \"w-4 h-4 text-red-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 35\n        }, this);\n      case 'late':\n        return /*#__PURE__*/_jsxDEV(AlertCircle, {\n          className: \"w-4 h-4 text-yellow-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 33\n        }, this);\n      default:\n        return null;\n    }\n  };\n  const getAttendanceColor = percentage => {\n    if (percentage >= 90) return 'text-green-600 bg-green-50 border-green-200';\n    if (percentage >= 75) return 'text-yellow-600 bg-yellow-50 border-yellow-200';\n    return 'text-red-600 bg-red-50 border-red-200';\n  };\n  const StatCard = ({\n    title,\n    value,\n    subtitle,\n    icon: Icon,\n    trend\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-gradient-to-br from-white via-blue-50 to-blue-100 rounded-xl p-6 shadow-md border border-blue-100 hover:shadow-lg transition-shadow\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 bg-blue-600 rounded-lg\",\n          children: /*#__PURE__*/_jsxDEV(Icon, {\n            className: \"w-5 h-5 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-blue-700 font-semibold\",\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 17\n      }, this), trend && /*#__PURE__*/_jsxDEV(TrendingUp, {\n        className: \"w-4 h-4 text-green-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 27\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-1\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-3xl font-extrabold text-blue-900\",\n        children: value\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-blue-600\",\n        children: subtitle\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 95,\n    columnNumber: 9\n  }, this);\n  const ProgressBar = ({\n    percentage,\n    label,\n    total,\n    present\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-2\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-sm font-semibold text-gray-700\",\n        children: label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: `text-xs px-2 py-1 rounded-full border ${getAttendanceColor(percentage)}`,\n        children: [percentage, \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-full bg-gray-200 rounded-full h-2\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `h-2 rounded-full transition-all duration-300 ${percentage >= 90 ? 'bg-green-500' : percentage >= 75 ? 'bg-yellow-500' : 'bg-red-500'}`,\n        style: {\n          width: `${percentage}%`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between text-xs text-gray-500\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: [\"Present: \", present]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: [\"Total: \", total]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 113,\n    columnNumber: 9\n  }, this);\n\n  // Auto-switch to calendar view when filters are selected\n  useEffect(() => {\n    if (selectedCourse !== 'all' || selectedBatch !== 'all' || selectedSubject !== 'all') {\n      setActiveView('calendar');\n    }\n  }, [selectedCourse, selectedBatch, selectedSubject]);\n\n  // Filter attendance data based on selected filters\n  const getFilteredAttendanceData = () => {\n    return attendanceData.dailyRecords.filter(record => {\n      const courseMatch = selectedCourse === 'all' || record.course.toLowerCase() === selectedCourse.toLowerCase();\n      const batchMatch = selectedBatch === 'all' || record.batch.toLowerCase() === selectedBatch.toLowerCase();\n      const subjectMatch = selectedSubject === 'all' || record.subject.toLowerCase() === selectedSubject.toLowerCase();\n      return courseMatch && batchMatch && subjectMatch;\n    });\n  };\n\n  // Get attendance status for a specific date\n  const getAttendanceForDate = date => {\n    const dateStr = date.toISOString().split('T')[0];\n    const filteredData = getFilteredAttendanceData();\n    const dayRecords = filteredData.filter(record => record.date === dateStr);\n    if (dayRecords.length === 0) return null;\n\n    // Check if it's a holiday\n    if (dayRecords.some(record => record.type === 'holiday')) {\n      return {\n        status: 'holiday',\n        records: dayRecords\n      };\n    }\n\n    // Calculate overall status for the day\n    const presentCount = dayRecords.filter(record => record.status === 'Present').length;\n    const absentCount = dayRecords.filter(record => record.status === 'Absent').length;\n    if (presentCount > 0 && absentCount === 0) return {\n      status: 'present',\n      records: dayRecords\n    };\n    if (absentCount > 0 && presentCount === 0) return {\n      status: 'absent',\n      records: dayRecords\n    };\n    if (presentCount > 0 && absentCount > 0) return {\n      status: 'mixed',\n      records: dayRecords\n    };\n    return {\n      status: 'unknown',\n      records: dayRecords\n    };\n  };\n\n  // Custom tile content for calendar (small dots)\n  const getTileContent = ({\n    date,\n    view\n  }) => {\n    if (view !== 'month') return null;\n    const attendance = getAttendanceForDate(date);\n    if (!attendance) return null;\n    const today = new Date();\n    const isFuture = date > today;\n    if (isFuture) return null;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center mt-1\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `w-2 h-2 rounded-full ${attendance.status === 'present' ? 'bg-green-500' : attendance.status === 'absent' ? 'bg-red-500' : attendance.status === 'holiday' ? 'bg-gray-400' : attendance.status === 'mixed' ? 'bg-yellow-500' : 'bg-blue-400'}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 13\n    }, this);\n  };\n\n  // Custom tile class name for calendar styling\n  const getTileClassName = ({\n    date,\n    view\n  }) => {\n    if (view !== 'month') return null;\n    const attendance = getAttendanceForDate(date);\n    const today = new Date();\n    const isFuture = date > today;\n    if (isFuture) {\n      return 'future-date';\n    }\n    if (!attendance) return 'no-class';\n    switch (attendance.status) {\n      case 'present':\n        return 'present-day';\n      case 'absent':\n        return 'absent-day';\n      case 'holiday':\n        return 'holiday-day';\n      case 'mixed':\n        return 'mixed-day';\n      default:\n        return 'no-class';\n    }\n  };\n  const handleDateChange = date => {\n    setSelectedDate(date);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-tr from-blue-50 via-white to-pink-50 p-6\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto space-y-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl p-8 shadow-lg border border-blue-100\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-4xl font-extrabold text-blue-800 leading-tight\",\n              children: \"Attendance Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-blue-600 mt-2 text-lg\",\n              children: \"Monitor attendance by batch, course, and subject.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"flex items-center gap-2 px-5 py-2 bg-blue-700 text-white rounded-xl shadow hover:bg-blue-800 transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(Download, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Export\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"flex items-center gap-2 px-5 py-2 border border-blue-300 text-blue-700 rounded-xl hover:bg-blue-50 transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(Filter, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Filter\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl p-2 shadow-sm border border-blue-100\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap gap-2 justify-center\",\n          children: [{\n            id: 'overall',\n            label: 'Overall',\n            icon: TrendingUp\n          }, {\n            id: 'subjects',\n            label: 'By Subject',\n            icon: BookOpen\n          }, {\n            id: 'batches',\n            label: 'By Batch',\n            icon: Users\n          }, {\n            id: 'courses',\n            label: 'By Course',\n            icon: GraduationCap\n          }, {\n            id: 'calendar',\n            label: 'Calendar View',\n            icon: Calendar\n          }].map(({\n            id,\n            label,\n            icon: Icon\n          }) => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setActiveView(id),\n            className: `flex items-center gap-2 px-5 py-2 rounded-xl font-medium text-base transition-all\n                  ${activeView === id ? 'bg-blue-700 text-white shadow' : 'text-blue-700 hover:bg-blue-100'}`,\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 33\n            }, this)]\n          }, id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 29\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl p-6 shadow-md border border-blue-100\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-semibold text-blue-700 mb-2\",\n              children: \"Date Range\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: dateRange,\n              onChange: e => setDateRange(e.target.value),\n              className: \"w-full px-3 py-2 border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"today\",\n                children: \"Today\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"thisWeek\",\n                children: \"This Week\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"thisMonth\",\n                children: \"This Month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"lastMonth\",\n                children: \"Last Month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"thisYear\",\n                children: \"This Year\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-semibold text-blue-700 mb-2\",\n              children: \"Batch\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedBatch,\n              onChange: e => {\n                setSelectedBatch(e.target.value);\n                // Add smooth transition to calendar view\n                if (e.target.value !== 'all') {\n                  setTimeout(() => setActiveView('calendar'), 300);\n                }\n              },\n              className: \"w-full px-3 py-2 border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: \"All Batches\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Batch A\",\n                children: \"Batch A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Batch B\",\n                children: \"Batch B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Batch C\",\n                children: \"Batch C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-semibold text-blue-700 mb-2\",\n              children: \"Course\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedCourse,\n              onChange: e => {\n                setSelectedCourse(e.target.value);\n                // Add smooth transition to calendar view\n                if (e.target.value !== 'all') {\n                  setTimeout(() => setActiveView('calendar'), 300);\n                }\n              },\n              className: \"w-full px-3 py-2 border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: \"All Courses\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Engineering\",\n                children: \"Engineering\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Science\",\n                children: \"Science\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Commerce\",\n                children: \"Commerce\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-semibold text-blue-700 mb-2\",\n              children: \"Subject\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedSubject,\n              onChange: e => {\n                setSelectedSubject(e.target.value);\n                // Add smooth transition to calendar view\n                if (e.target.value !== 'all') {\n                  setTimeout(() => setActiveView('calendar'), 300);\n                }\n              },\n              className: \"w-full px-3 py-2 border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: \"All Subjects\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Mathematics\",\n                children: \"Mathematics\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Physics\",\n                children: \"Physics\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Chemistry\",\n                children: \"Chemistry\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Computer Science\",\n                children: \"Computer Science\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"English\",\n                children: \"English\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Biology\",\n                children: \"Biology\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Accounting\",\n                children: \"Accounting\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Economics\",\n                children: \"Economics\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Business Studies\",\n                children: \"Business Studies\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Overall Attendance\",\n          value: \"85%\",\n          subtitle: \"This month average\",\n          icon: TrendingUp,\n          trend: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Total Classes\",\n          value: \"124\",\n          subtitle: \"Classes conducted\",\n          icon: Calendar\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Present Days\",\n          value: \"105\",\n          subtitle: \"Out of 124 classes\",\n          icon: CheckCircle\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 xl:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"xl:col-span-2 space-y-8\",\n          children: [activeView === 'overall' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-2xl p-8 shadow-md border border-blue-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-blue-900 mb-6\",\n              children: \"Overall Attendance Summary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center p-8 bg-gradient-to-tr from-blue-50 via-blue-100 to-white rounded-xl shadow\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-5xl font-extrabold text-blue-700 mb-2 drop-shadow\",\n                  children: \"85%\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 396,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-blue-800 font-semibold\",\n                  children: \"Overall Attendance\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-5\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-xl shadow-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                      className: \"w-8 h-8 text-green-600\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 402,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"font-semibold text-green-900\",\n                        children: \"Present\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 404,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-green-700\",\n                        children: \"Days attended\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 405,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 403,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 401,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-extrabold text-green-600\",\n                    children: \"105\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 408,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between p-4 bg-red-50 border border-red-200 rounded-xl shadow-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [/*#__PURE__*/_jsxDEV(XCircle, {\n                      className: \"w-8 h-8 text-red-600\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 412,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"font-semibold text-red-900\",\n                        children: \"Absent\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 414,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-red-700\",\n                        children: \"Days missed\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 415,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 413,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 411,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-extrabold text-red-600\",\n                    children: \"19\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 418,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 29\n          }, this), activeView === 'subjects' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-2xl p-8 shadow-md border border-blue-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-blue-900 mb-6\",\n              children: \"Subject-wise Attendance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-6\",\n              children: attendanceData.subjects.map((subject, index) => /*#__PURE__*/_jsxDEV(ProgressBar, {\n                percentage: subject.percentage,\n                label: subject.name,\n                total: subject.total,\n                present: subject.present\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 41\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 29\n          }, this), activeView === 'batches' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-2xl p-8 shadow-md border border-blue-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-blue-900 mb-6\",\n              children: \"Batch-wise Attendance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n              children: attendanceData.batches.map((batch, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6 bg-blue-50 rounded-xl border border-blue-200 shadow hover:scale-105 transition-transform\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-semibold text-blue-900\",\n                    children: batch.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 449,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(Users, {\n                    className: \"w-5 h-5 text-blue-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-bold text-blue-900\",\n                    children: [batch.avgAttendance, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 453,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-blue-700\",\n                    children: [batch.students, \" students\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 454,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full bg-blue-200 rounded-full h-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"h-2 bg-blue-600 rounded-full transition-all duration-300\",\n                      style: {\n                        width: `${batch.avgAttendance}%`\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 456,\n                      columnNumber: 53\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 455,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 452,\n                  columnNumber: 45\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 41\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 29\n          }, this), activeView === 'courses' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-2xl p-8 shadow-md border border-blue-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-blue-900 mb-6\",\n              children: \"Course-wise Attendance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-6\",\n              children: attendanceData.courses.map((course, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between p-5 border border-blue-200 rounded-xl bg-blue-50 hover:bg-blue-100 transition-colors shadow\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-blue-700 rounded-lg\",\n                    children: /*#__PURE__*/_jsxDEV(GraduationCap, {\n                      className: \"w-6 h-6 text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 476,\n                      columnNumber: 53\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 475,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-semibold text-blue-900\",\n                      children: course.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 479,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-blue-700\",\n                      children: [course.students, \" students enrolled\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 480,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 478,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 474,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `text-lg font-bold px-4 py-1 rounded-full border ${getAttendanceColor(course.avgAttendance)}`,\n                    children: [course.avgAttendance, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 484,\n                    columnNumber: 49\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 483,\n                  columnNumber: 45\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 473,\n                columnNumber: 41\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 29\n          }, this), activeView === 'calendar' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-8\",\n            children: [(selectedCourse !== 'all' || selectedBatch !== 'all' || selectedSubject !== 'all') && (() => {\n              const filteredData = getFilteredAttendanceData();\n              const classRecords = filteredData.filter(record => record.type === 'class');\n              const presentCount = classRecords.filter(record => record.status === 'Present').length;\n              const absentCount = classRecords.filter(record => record.status === 'Absent').length;\n              const totalClasses = classRecords.length;\n              const attendancePercentage = totalClasses > 0 ? Math.round(presentCount / totalClasses * 100) : 0;\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-br from-indigo-50 via-blue-50 to-purple-50 rounded-3xl p-8 shadow-xl border border-indigo-100 backdrop-blur-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"text-2xl font-bold text-indigo-900 mb-2\",\n                      children: \"Filtered Attendance Summary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 509,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex flex-wrap gap-2 text-sm\",\n                      children: [selectedCourse !== 'all' && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"px-3 py-1 bg-indigo-100 text-indigo-700 rounded-full border border-indigo-200\",\n                        children: [\"Course: \", selectedCourse]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 512,\n                        columnNumber: 61\n                      }, this), selectedBatch !== 'all' && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"px-3 py-1 bg-blue-100 text-blue-700 rounded-full border border-blue-200\",\n                        children: [\"Batch: \", selectedBatch]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 517,\n                        columnNumber: 61\n                      }, this), selectedSubject !== 'all' && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"px-3 py-1 bg-purple-100 text-purple-700 rounded-full border border-purple-200\",\n                        children: [\"Subject: \", selectedSubject]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 522,\n                        columnNumber: 61\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 510,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 508,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"hidden md:block\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg\",\n                      children: /*#__PURE__*/_jsxDEV(TrendingUp, {\n                        className: \"w-8 h-8 text-white\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 530,\n                        columnNumber: 57\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 529,\n                      columnNumber: 53\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 528,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 507,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"group relative overflow-hidden bg-gradient-to-br from-white to-indigo-50 rounded-2xl p-6 shadow-lg border border-indigo-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-indigo-400 to-indigo-600 rounded-bl-3xl opacity-10\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 537,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"relative\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-between mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"p-2 bg-indigo-100 rounded-xl\",\n                          children: /*#__PURE__*/_jsxDEV(TrendingUp, {\n                            className: \"w-5 h-5 text-indigo-600\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 541,\n                            columnNumber: 65\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 540,\n                          columnNumber: 61\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `text-xs px-2 py-1 rounded-full ${attendancePercentage >= 90 ? 'bg-green-100 text-green-700' : attendancePercentage >= 75 ? 'bg-yellow-100 text-yellow-700' : 'bg-red-100 text-red-700'}`,\n                          children: attendancePercentage >= 90 ? 'Excellent' : attendancePercentage >= 75 ? 'Good' : 'Needs Improvement'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 543,\n                          columnNumber: 61\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 539,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-3xl font-bold text-indigo-700 mb-1\",\n                        children: [attendancePercentage, \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 551,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-indigo-600 font-medium\",\n                        children: \"Attendance Rate\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 552,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 538,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 536,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"group relative overflow-hidden bg-gradient-to-br from-white to-green-50 rounded-2xl p-6 shadow-lg border border-green-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-bl-3xl opacity-10\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 557,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"relative\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-between mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"p-2 bg-green-100 rounded-xl\",\n                          children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                            className: \"w-5 h-5 text-green-600\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 561,\n                            columnNumber: 65\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 560,\n                          columnNumber: 61\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-xs px-2 py-1 bg-green-100 text-green-700 rounded-full\",\n                          children: [totalClasses > 0 ? Math.round(presentCount / totalClasses * 100) : 0, \"%\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 563,\n                          columnNumber: 61\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 559,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-3xl font-bold text-green-700 mb-1\",\n                        children: presentCount\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 567,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-green-600 font-medium\",\n                        children: \"Present Days\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 568,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 558,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 556,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"group relative overflow-hidden bg-gradient-to-br from-white to-red-50 rounded-2xl p-6 shadow-lg border border-red-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-red-400 to-red-600 rounded-bl-3xl opacity-10\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 573,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"relative\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-between mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"p-2 bg-red-100 rounded-xl\",\n                          children: /*#__PURE__*/_jsxDEV(XCircle, {\n                            className: \"w-5 h-5 text-red-600\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 577,\n                            columnNumber: 65\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 576,\n                          columnNumber: 61\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-xs px-2 py-1 bg-red-100 text-red-700 rounded-full\",\n                          children: [totalClasses > 0 ? Math.round(absentCount / totalClasses * 100) : 0, \"%\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 579,\n                          columnNumber: 61\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 575,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-3xl font-bold text-red-700 mb-1\",\n                        children: absentCount\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 583,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-red-600 font-medium\",\n                        children: \"Absent Days\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 584,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 574,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 572,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"group relative overflow-hidden bg-gradient-to-br from-white to-gray-50 rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-gray-400 to-gray-600 rounded-bl-3xl opacity-10\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 589,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"relative\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-between mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"p-2 bg-gray-100 rounded-xl\",\n                          children: /*#__PURE__*/_jsxDEV(Calendar, {\n                            className: \"w-5 h-5 text-gray-600\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 593,\n                            columnNumber: 65\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 592,\n                          columnNumber: 61\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded-full\",\n                          children: \"100%\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 595,\n                          columnNumber: 61\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 591,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-3xl font-bold text-gray-700 mb-1\",\n                        children: totalClasses\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 599,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-gray-600 font-medium\",\n                        children: \"Total Classes\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 600,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 590,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 588,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 506,\n                columnNumber: 41\n              }, this);\n            })(), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-white via-blue-50/30 to-indigo-50/50 rounded-3xl p-8 shadow-2xl border border-blue-100/50 backdrop-blur-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl shadow-lg\",\n                    children: /*#__PURE__*/_jsxDEV(Calendar, {\n                      className: \"w-7 h-7 text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 613,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 612,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-2xl font-bold text-blue-900\",\n                      children: \"Interactive Calendar\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 616,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-blue-600 text-sm mt-1\",\n                      children: selectedCourse === 'all' && selectedBatch === 'all' && selectedSubject === 'all' ? 'Showing all attendance records' : 'Filtered attendance view'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 617,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 615,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 611,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-4 mt-4 lg:mt-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-lg font-bold text-blue-700\",\n                      children: new Date().toLocaleDateString('en-US', {\n                        month: 'short'\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 628,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-blue-600\",\n                      children: \"Current Month\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 629,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 627,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-px h-8 bg-blue-200\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 631,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-lg font-bold text-blue-700\",\n                      children: selectedDate.getDate()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 633,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-blue-600\",\n                      children: \"Selected\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 634,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 632,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 626,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 610,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-8 p-6 bg-gradient-to-r from-slate-50 to-blue-50 rounded-2xl border border-slate-200 shadow-inner\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-lg font-bold text-slate-700 flex items-center gap-2\",\n                    children: [/*#__PURE__*/_jsxDEV(Info, {\n                      className: \"w-5 h-5 text-blue-500\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 643,\n                      columnNumber: 49\n                    }, this), \"Calendar Legend\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 642,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-slate-500 bg-white px-3 py-1 rounded-full border\",\n                    children: \"Click dates for details\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 646,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 641,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-2 lg:grid-cols-4 gap-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-3 p-3 bg-white rounded-xl shadow-sm border border-green-100 hover:shadow-md transition-shadow\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-6 h-6 bg-gradient-to-br from-green-200 to-green-300 border-2 border-green-400 rounded-lg shadow-sm\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 652,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-semibold text-green-700\",\n                        children: \"Present\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 654,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-xs text-green-600\",\n                        children: \"Attended class\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 655,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 653,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 651,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-3 p-3 bg-white rounded-xl shadow-sm border border-red-100 hover:shadow-md transition-shadow\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-6 h-6 bg-gradient-to-br from-red-200 to-red-300 border-2 border-red-400 rounded-lg shadow-sm\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 659,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-semibold text-red-700\",\n                        children: \"Absent\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 661,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-xs text-red-600\",\n                        children: \"Missed class\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 662,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 660,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 658,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-3 p-3 bg-white rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-6 h-6 bg-gradient-to-br from-gray-200 to-gray-300 border-2 border-gray-400 rounded-lg shadow-sm\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 666,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-semibold text-gray-700\",\n                        children: \"Holiday\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 668,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-xs text-gray-600\",\n                        children: \"No classes\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 669,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 667,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 665,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-3 p-3 bg-white rounded-xl shadow-sm border border-yellow-100 hover:shadow-md transition-shadow\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-6 h-6 bg-gradient-to-br from-yellow-200 to-yellow-300 border-2 border-yellow-400 rounded-lg shadow-sm\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 673,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-semibold text-yellow-700\",\n                        children: \"Mixed\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 675,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-xs text-yellow-600\",\n                        children: \"Partial attendance\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 676,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 674,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 672,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 650,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 640,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"calendar-container relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-br from-blue-100/20 to-indigo-100/20 rounded-2xl -z-10\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 684,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white rounded-2xl shadow-xl border border-blue-100 overflow-hidden\",\n                  children: /*#__PURE__*/_jsxDEV(CalendarView, {\n                    value: selectedDate,\n                    onChange: handleDateChange,\n                    tileContent: getTileContent,\n                    tileClassName: getTileClassName,\n                    className: \"enhanced-calendar w-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 686,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 685,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-4 flex items-center justify-center gap-4 text-sm text-blue-600\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 698,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Use navigation arrows to browse months\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 699,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 697,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 696,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 683,\n                columnNumber: 37\n              }, this), selectedDate && (() => {\n                const attendance = getAttendanceForDate(selectedDate);\n                const today = new Date();\n                const isToday = selectedDate.toDateString() === today.toDateString();\n                const isFuture = selectedDate > today;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-8 space-y-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl p-6 text-white shadow-xl\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-between\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                          className: \"text-2xl font-bold mb-1\",\n                          children: selectedDate.toLocaleDateString('en-US', {\n                            weekday: 'long',\n                            year: 'numeric',\n                            month: 'long',\n                            day: 'numeric'\n                          })\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 717,\n                          columnNumber: 61\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center gap-2 text-indigo-100\",\n                          children: [isToday && /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"px-2 py-1 bg-white/20 rounded-full text-xs font-medium\",\n                            children: \"Today\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 727,\n                            columnNumber: 69\n                          }, this), isFuture && /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"px-2 py-1 bg-white/20 rounded-full text-xs font-medium\",\n                            children: \"Future Date\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 732,\n                            columnNumber: 69\n                          }, this), attendance && /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: `px-2 py-1 rounded-full text-xs font-medium ${attendance.status === 'present' ? 'bg-green-400/30 text-green-100' : attendance.status === 'absent' ? 'bg-red-400/30 text-red-100' : attendance.status === 'holiday' ? 'bg-gray-400/30 text-gray-100' : 'bg-yellow-400/30 text-yellow-100'}`,\n                            children: attendance.status === 'present' ? 'Present Day' : attendance.status === 'absent' ? 'Absent Day' : attendance.status === 'holiday' ? 'Holiday' : 'Mixed Attendance'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 737,\n                            columnNumber: 69\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 725,\n                          columnNumber: 61\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 716,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-right\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-3xl font-bold\",\n                          children: selectedDate.getDate()\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 752,\n                          columnNumber: 61\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-sm text-indigo-200\",\n                          children: selectedDate.toLocaleDateString('en-US', {\n                            month: 'short'\n                          })\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 753,\n                          columnNumber: 61\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 751,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 715,\n                      columnNumber: 53\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 714,\n                    columnNumber: 49\n                  }, this), attendance && attendance.records.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white rounded-2xl p-6 shadow-lg border border-gray-100\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-between mb-6\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                        className: \"text-lg font-bold text-gray-900\",\n                        children: \"Class Details\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 764,\n                        columnNumber: 61\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [attendance.records.length, \" \", attendance.records.length === 1 ? 'class' : 'classes']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 765,\n                        columnNumber: 61\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 763,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"space-y-3\",\n                      children: attendance.records.map((record, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `group relative overflow-hidden rounded-xl border-2 transition-all duration-300 hover:shadow-lg ${record.status === 'Present' ? 'border-green-200 bg-gradient-to-r from-green-50 to-green-100/50 hover:border-green-300' : record.status === 'Absent' ? 'border-red-200 bg-gradient-to-r from-red-50 to-red-100/50 hover:border-red-300' : 'border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100/50 hover:border-gray-300'}`,\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"p-4\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center gap-4\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: `p-3 rounded-xl shadow-sm ${record.status === 'Present' ? 'bg-green-100' : record.status === 'Absent' ? 'bg-red-100' : 'bg-gray-100'}`,\n                                children: getStatusIcon(record.status.toLowerCase())\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 779,\n                                columnNumber: 81\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                                  className: \"font-bold text-gray-900 text-lg\",\n                                  children: record.subject\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 787,\n                                  columnNumber: 85\n                                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"flex items-center gap-2 text-sm text-gray-600 mt-1\",\n                                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                    className: \"px-2 py-1 bg-white/70 rounded-lg border\",\n                                    children: record.course\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 789,\n                                    columnNumber: 89\n                                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                    className: \"px-2 py-1 bg-white/70 rounded-lg border\",\n                                    children: record.batch\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 792,\n                                    columnNumber: 89\n                                  }, this), record.type === 'holiday' && /*#__PURE__*/_jsxDEV(\"span\", {\n                                    className: \"px-2 py-1 bg-blue-100 text-blue-700 rounded-lg border border-blue-200\",\n                                    children: \"Holiday\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 796,\n                                    columnNumber: 93\n                                  }, this)]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 788,\n                                  columnNumber: 85\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 786,\n                                columnNumber: 81\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 778,\n                              columnNumber: 77\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-right\",\n                              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: `inline-flex items-center px-4 py-2 rounded-xl font-semibold text-sm border-2 ${record.status === 'Present' ? 'bg-green-100 text-green-800 border-green-300' : record.status === 'Absent' ? 'bg-red-100 text-red-800 border-red-300' : 'bg-gray-100 text-gray-800 border-gray-300'}`,\n                                children: record.status\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 804,\n                                columnNumber: 81\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 803,\n                              columnNumber: 77\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 777,\n                            columnNumber: 73\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 776,\n                          columnNumber: 69\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `absolute top-0 right-0 w-1 h-full ${record.status === 'Present' ? 'bg-gradient-to-b from-green-400 to-green-600' : record.status === 'Absent' ? 'bg-gradient-to-b from-red-400 to-red-600' : 'bg-gradient-to-b from-gray-400 to-gray-600'}`\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 815,\n                          columnNumber: 69\n                        }, this)]\n                      }, index, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 771,\n                        columnNumber: 65\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 769,\n                      columnNumber: 57\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 762,\n                    columnNumber: 53\n                  }, this) : isFuture ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8 text-center border border-blue-100\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-16 h-16 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-2xl mx-auto mb-4 flex items-center justify-center\",\n                      children: /*#__PURE__*/_jsxDEV(Calendar, {\n                        className: \"w-8 h-8 text-white\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 827,\n                        columnNumber: 61\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 826,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"text-lg font-bold text-blue-900 mb-2\",\n                      children: \"Future Date\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 829,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-blue-600\",\n                      children: \"No attendance data available for future dates.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 830,\n                      columnNumber: 57\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 825,\n                    columnNumber: 53\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gradient-to-r from-gray-50 to-slate-50 rounded-2xl p-8 text-center border border-gray-200\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-16 h-16 bg-gradient-to-br from-gray-400 to-slate-500 rounded-2xl mx-auto mb-4 flex items-center justify-center\",\n                      children: /*#__PURE__*/_jsxDEV(Calendar, {\n                        className: \"w-8 h-8 text-white\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 835,\n                        columnNumber: 61\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 834,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"text-lg font-bold text-gray-700 mb-2\",\n                      children: \"No Classes\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 837,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600\",\n                      children: \"No classes were scheduled for this date.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 838,\n                      columnNumber: 57\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 833,\n                    columnNumber: 53\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 712,\n                  columnNumber: 45\n                }, this);\n              })()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 608,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 495,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl p-8 shadow-md border border-blue-100\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-bold text-blue-900 mb-6 flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(Clock, {\n              className: \"w-5 h-5 text-blue-700\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 852,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Recent Activity\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 853,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 851,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-5\",\n            children: attendanceData.recentActivity.map((activity, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-3 p-4 bg-blue-50 rounded-xl border hover:bg-blue-100 transition-colors\",\n              children: [getStatusIcon(activity.status), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold text-blue-900\",\n                  children: activity.subject\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 860,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-blue-700\",\n                  children: [activity.date, \" \\u2022 \", activity.time]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 861,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 859,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `text-xs px-3 py-1 rounded-full capitalize border\n                    ${activity.status === 'present' ? 'bg-green-100 text-green-800 border-green-300' : activity.status === 'absent' ? 'bg-red-100 text-red-800 border-red-300' : 'bg-yellow-100 text-yellow-800 border-yellow-300'}`,\n                children: activity.status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 863,\n                columnNumber: 37\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 857,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 855,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 850,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 233,\n    columnNumber: 9\n  }, this);\n};\n_s(AttendancePage, \"U/CV1TzuG0fvLkcqq9qrhG4KLhg=\");\n_c = AttendancePage;\nexport default AttendancePage;\n\n// Custom CSS styles for enhanced calendar\nconst calendarStyles = `\n.enhanced-calendar {\n    width: 100%;\n    border: none;\n    font-family: inherit;\n}\n\n.enhanced-calendar .react-calendar__tile {\n    position: relative;\n    padding: 0.75em 0.5em;\n    background: white;\n    border: 1px solid #e5e7eb;\n    transition: all 0.2s ease;\n}\n\n.enhanced-calendar .react-calendar__tile:hover {\n    background-color: #f3f4f6;\n}\n\n.enhanced-calendar .react-calendar__tile--active {\n    background: #3b82f6 !important;\n    color: white;\n}\n\n.enhanced-calendar .react-calendar__tile.present-day {\n    background-color: #dcfce7;\n    border-color: #16a34a;\n    color: #15803d;\n}\n\n.enhanced-calendar .react-calendar__tile.absent-day {\n    background-color: #fecaca;\n    border-color: #dc2626;\n    color: #b91c1c;\n}\n\n.enhanced-calendar .react-calendar__tile.holiday-day {\n    background-color: #f3f4f6;\n    border-color: #6b7280;\n    color: #4b5563;\n}\n\n.enhanced-calendar .react-calendar__tile.mixed-day {\n    background-color: #fef3c7;\n    border-color: #d97706;\n    color: #92400e;\n}\n\n.enhanced-calendar .react-calendar__tile.future-date {\n    background-color: #f9fafb;\n    color: #9ca3af;\n}\n\n.enhanced-calendar .react-calendar__tile.no-class {\n    background-color: #ffffff;\n    color: #6b7280;\n}\n\n.enhanced-calendar .react-calendar__navigation button {\n    background: #3b82f6;\n    color: white;\n    border: none;\n    padding: 0.5rem 1rem;\n    border-radius: 0.5rem;\n    font-weight: 500;\n}\n\n.enhanced-calendar .react-calendar__navigation button:hover {\n    background: #2563eb;\n}\n\n.enhanced-calendar .react-calendar__navigation button:disabled {\n    background: #9ca3af;\n}\n\n.enhanced-calendar .react-calendar__month-view__weekdays {\n    background: #f8fafc;\n    font-weight: 600;\n    color: #475569;\n}\n\n.enhanced-calendar .react-calendar__month-view__weekdays__weekday {\n    padding: 0.5rem;\n    text-align: center;\n}\n`;\n\n// Inject styles into the document\nif (typeof document !== 'undefined') {\n  const existingStyle = document.getElementById('calendar-styles');\n  if (!existingStyle) {\n    const styleElement = document.createElement('style');\n    styleElement.id = 'calendar-styles';\n    styleElement.textContent = calendarStyles;\n    document.head.appendChild(styleElement);\n  }\n}\nvar _c;\n$RefreshReg$(_c, \"AttendancePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Calendar", "Users", "BookOpen", "GraduationCap", "Filter", "Download", "TrendingUp", "Clock", "CheckCircle", "XCircle", "AlertCircle", "Info", "CalendarView", "jsxDEV", "_jsxDEV", "AttendancePage", "_s", "activeView", "setActiveView", "selected<PERSON><PERSON>", "setSelectedBatch", "selectedCourse", "setSelectedCourse", "selectedSubject", "setSelectedSubject", "date<PERSON><PERSON><PERSON>", "setDateRange", "selectedDate", "setSelectedDate", "Date", "attendanceData", "overall", "present", "absent", "total", "subjects", "name", "percentage", "batches", "students", "avgAttendance", "courses", "recentActivity", "date", "subject", "status", "time", "dailyRecords", "course", "batch", "type", "getStatusIcon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getAttendanceColor", "StatCard", "title", "value", "subtitle", "icon", "Icon", "trend", "children", "ProgressBar", "label", "style", "width", "getFilteredAttendanceData", "filter", "record", "courseMatch", "toLowerCase", "batchMatch", "subjectMatch", "getAttendanceForDate", "dateStr", "toISOString", "split", "filteredData", "dayRecords", "length", "some", "records", "presentCount", "absentCount", "getTileContent", "view", "attendance", "today", "isFuture", "getTileClassName", "handleDateChange", "id", "map", "onClick", "onChange", "e", "target", "setTimeout", "index", "classRecords", "totalClasses", "attendancePercentage", "Math", "round", "toLocaleDateString", "month", "getDate", "tileContent", "tileClassName", "isToday", "toDateString", "weekday", "year", "day", "activity", "_c", "calendarStyles", "document", "existingStyle", "getElementById", "styleElement", "createElement", "textContent", "head", "append<PERSON><PERSON><PERSON>", "$RefreshReg$"], "sources": ["D:/techvritti/Collegemanagement/frontend/src/Screens/Student/AttendancePage.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport {\r\n    Calendar, Users, BookOpen, GraduationCap, Filter, Download,\r\n    TrendingUp, Clock, CheckCircle, XCircle, AlertCircle, Info\r\n} from 'lucide-react';\r\nimport 'react-calendar/dist/Calendar.css';\r\nimport CalendarView from 'react-calendar';\r\n\r\nconst AttendancePage = () => {\r\n    const [activeView, setActiveView] = useState('overall');\r\n    const [selectedBatch, setSelectedBatch] = useState('all');\r\n    const [selectedCourse, setSelectedCourse] = useState('all');\r\n    const [selectedSubject, setSelectedSubject] = useState('all');\r\n    const [dateRange, setDateRange] = useState('thisMonth');\r\n    const [selectedDate, setSelectedDate] = useState(new Date());\r\n    const [attendanceData] = useState({\r\n        overall: { present: 85, absent: 15, total: 100 },\r\n        subjects: [\r\n            { name: 'Mathematics', present: 18, total: 20, percentage: 90 },\r\n            { name: 'Physics', present: 16, total: 18, percentage: 89 },\r\n            { name: 'Chemistry', present: 14, total: 16, percentage: 88 },\r\n            { name: 'Computer Science', present: 19, total: 22, percentage: 86 },\r\n            { name: 'English', present: 15, total: 18, percentage: 83 }\r\n        ],\r\n        batches: [\r\n            { name: 'Batch A', students: 45, avgAttendance: 87 },\r\n            { name: 'Batch B', students: 42, avgAttendance: 85 },\r\n            { name: 'Batch C', students: 48, avgAttendance: 89 }\r\n        ],\r\n        courses: [\r\n            { name: 'Engineering', students: 135, avgAttendance: 87 },\r\n            { name: 'Science', students: 98, avgAttendance: 85 },\r\n            { name: 'Commerce', students: 76, avgAttendance: 91 }\r\n        ],\r\n        recentActivity: [\r\n            { date: '2024-03-15', subject: 'Mathematics', status: 'present', time: '09:00 AM' },\r\n            { date: '2024-03-15', subject: 'Physics', status: 'present', time: '10:30 AM' },\r\n            { date: '2024-03-14', subject: 'Chemistry', status: 'absent', time: '02:00 PM' },\r\n            { date: '2024-03-14', subject: 'Computer Science', status: 'present', time: '03:30 PM' },\r\n            { date: '2024-03-13', subject: 'English', status: 'present', time: '11:00 AM' }\r\n        ],\r\n        // Enhanced daily attendance records for calendar view\r\n        dailyRecords: [\r\n            // January 2024 - Engineering Course\r\n            { date: '2024-01-15', subject: 'Mathematics', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-15', subject: 'Physics', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-16', subject: 'Chemistry', status: 'Absent', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-16', subject: 'Mathematics', status: 'Present', course: 'Engineering', batch: 'Batch B', type: 'class' },\r\n            { date: '2024-01-17', subject: 'English', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-18', subject: 'Computer Science', status: 'Absent', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-19', subject: 'Physics', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-20', subject: 'Holiday', status: 'Holiday', course: 'all', batch: 'all', type: 'holiday' },\r\n            { date: '2024-01-21', subject: 'Holiday', status: 'Holiday', course: 'all', batch: 'all', type: 'holiday' },\r\n            { date: '2024-01-22', subject: 'Mathematics', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-23', subject: 'Chemistry', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-24', subject: 'English', status: 'Absent', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-25', subject: 'Computer Science', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-26', subject: 'Physics', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-29', subject: 'Mathematics', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-30', subject: 'Chemistry', status: 'Absent', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-31', subject: 'English', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n\r\n            // Science course records\r\n            { date: '2024-01-15', subject: 'Biology', status: 'Present', course: 'Science', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-16', subject: 'Chemistry', status: 'Present', course: 'Science', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-17', subject: 'Physics', status: 'Absent', course: 'Science', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-18', subject: 'Mathematics', status: 'Present', course: 'Science', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-19', subject: 'Biology', status: 'Present', course: 'Science', batch: 'Batch A', type: 'class' },\r\n\r\n            // Commerce course records\r\n            { date: '2024-01-15', subject: 'Accounting', status: 'Present', course: 'Commerce', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-16', subject: 'Economics', status: 'Present', course: 'Commerce', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-17', subject: 'Business Studies', status: 'Present', course: 'Commerce', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-18', subject: 'Accounting', status: 'Absent', course: 'Commerce', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-19', subject: 'Economics', status: 'Present', course: 'Commerce', batch: 'Batch A', type: 'class' },\r\n        ]\r\n    });\r\n\r\n    const getStatusIcon = (status) => {\r\n        switch (status) {\r\n            case 'present': return <CheckCircle className=\"w-4 h-4 text-green-500\" />;\r\n            case 'absent': return <XCircle className=\"w-4 h-4 text-red-500\" />;\r\n            case 'late': return <AlertCircle className=\"w-4 h-4 text-yellow-500\" />;\r\n            default: return null;\r\n        }\r\n    };\r\n\r\n    const getAttendanceColor = (percentage) => {\r\n        if (percentage >= 90) return 'text-green-600 bg-green-50 border-green-200';\r\n        if (percentage >= 75) return 'text-yellow-600 bg-yellow-50 border-yellow-200';\r\n        return 'text-red-600 bg-red-50 border-red-200';\r\n    };\r\n\r\n    const StatCard = ({ title, value, subtitle, icon: Icon, trend }) => (\r\n        <div className=\"bg-gradient-to-br from-white via-blue-50 to-blue-100 rounded-xl p-6 shadow-md border border-blue-100 hover:shadow-lg transition-shadow\">\r\n            <div className=\"flex items-center justify-between mb-4\">\r\n                <div className=\"flex items-center space-x-3\">\r\n                    <div className=\"p-2 bg-blue-600 rounded-lg\">\r\n                        <Icon className=\"w-5 h-5 text-white\" />\r\n                    </div>\r\n                    <h3 className=\"text-blue-700 font-semibold\">{title}</h3>\r\n                </div>\r\n                {trend && <TrendingUp className=\"w-4 h-4 text-green-500\" />}\r\n            </div>\r\n            <div className=\"space-y-1\">\r\n                <p className=\"text-3xl font-extrabold text-blue-900\">{value}</p>\r\n                <p className=\"text-sm text-blue-600\">{subtitle}</p>\r\n            </div>\r\n        </div>\r\n    );\r\n\r\n    const ProgressBar = ({ percentage, label, total, present }) => (\r\n        <div className=\"space-y-2\">\r\n            <div className=\"flex justify-between items-center\">\r\n                <span className=\"text-sm font-semibold text-gray-700\">{label}</span>\r\n                <span className={`text-xs px-2 py-1 rounded-full border ${getAttendanceColor(percentage)}`}>\r\n                    {percentage}%\r\n                </span>\r\n            </div>\r\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\r\n                <div\r\n                    className={`h-2 rounded-full transition-all duration-300 ${percentage >= 90 ? 'bg-green-500' : percentage >= 75 ? 'bg-yellow-500' : 'bg-red-500'}`}\r\n                    style={{ width: `${percentage}%` }}\r\n                ></div>\r\n            </div>\r\n            <div className=\"flex justify-between text-xs text-gray-500\">\r\n                <span>Present: {present}</span>\r\n                <span>Total: {total}</span>\r\n            </div>\r\n        </div>\r\n    );\r\n\r\n    // Auto-switch to calendar view when filters are selected\r\n    useEffect(() => {\r\n        if (selectedCourse !== 'all' || selectedBatch !== 'all' || selectedSubject !== 'all') {\r\n            setActiveView('calendar');\r\n        }\r\n    }, [selectedCourse, selectedBatch, selectedSubject]);\r\n\r\n    // Filter attendance data based on selected filters\r\n    const getFilteredAttendanceData = () => {\r\n        return attendanceData.dailyRecords.filter(record => {\r\n            const courseMatch = selectedCourse === 'all' || record.course.toLowerCase() === selectedCourse.toLowerCase();\r\n            const batchMatch = selectedBatch === 'all' || record.batch.toLowerCase() === selectedBatch.toLowerCase();\r\n            const subjectMatch = selectedSubject === 'all' || record.subject.toLowerCase() === selectedSubject.toLowerCase();\r\n\r\n            return courseMatch && batchMatch && subjectMatch;\r\n        });\r\n    };\r\n\r\n    // Get attendance status for a specific date\r\n    const getAttendanceForDate = (date) => {\r\n        const dateStr = date.toISOString().split('T')[0];\r\n        const filteredData = getFilteredAttendanceData();\r\n        const dayRecords = filteredData.filter(record => record.date === dateStr);\r\n\r\n        if (dayRecords.length === 0) return null;\r\n\r\n        // Check if it's a holiday\r\n        if (dayRecords.some(record => record.type === 'holiday')) {\r\n            return { status: 'holiday', records: dayRecords };\r\n        }\r\n\r\n        // Calculate overall status for the day\r\n        const presentCount = dayRecords.filter(record => record.status === 'Present').length;\r\n        const absentCount = dayRecords.filter(record => record.status === 'Absent').length;\r\n\r\n        if (presentCount > 0 && absentCount === 0) return { status: 'present', records: dayRecords };\r\n        if (absentCount > 0 && presentCount === 0) return { status: 'absent', records: dayRecords };\r\n        if (presentCount > 0 && absentCount > 0) return { status: 'mixed', records: dayRecords };\r\n\r\n        return { status: 'unknown', records: dayRecords };\r\n    };\r\n\r\n    // Custom tile content for calendar (small dots)\r\n    const getTileContent = ({ date, view }) => {\r\n        if (view !== 'month') return null;\r\n\r\n        const attendance = getAttendanceForDate(date);\r\n        if (!attendance) return null;\r\n\r\n        const today = new Date();\r\n        const isFuture = date > today;\r\n\r\n        if (isFuture) return null;\r\n\r\n        return (\r\n            <div className=\"flex justify-center mt-1\">\r\n                <div className={`w-2 h-2 rounded-full ${\r\n                    attendance.status === 'present' ? 'bg-green-500' :\r\n                    attendance.status === 'absent' ? 'bg-red-500' :\r\n                    attendance.status === 'holiday' ? 'bg-gray-400' :\r\n                    attendance.status === 'mixed' ? 'bg-yellow-500' :\r\n                    'bg-blue-400'\r\n                }`} />\r\n            </div>\r\n        );\r\n    };\r\n\r\n    // Custom tile class name for calendar styling\r\n    const getTileClassName = ({ date, view }) => {\r\n        if (view !== 'month') return null;\r\n\r\n        const attendance = getAttendanceForDate(date);\r\n        const today = new Date();\r\n        const isFuture = date > today;\r\n\r\n        if (isFuture) {\r\n            return 'future-date';\r\n        }\r\n\r\n        if (!attendance) return 'no-class';\r\n\r\n        switch (attendance.status) {\r\n            case 'present':\r\n                return 'present-day';\r\n            case 'absent':\r\n                return 'absent-day';\r\n            case 'holiday':\r\n                return 'holiday-day';\r\n            case 'mixed':\r\n                return 'mixed-day';\r\n            default:\r\n                return 'no-class';\r\n        }\r\n    };\r\n\r\n    const handleDateChange = (date) => {\r\n        setSelectedDate(date);\r\n    };\r\n\r\n    return (\r\n        <div className=\"min-h-screen bg-gradient-to-tr from-blue-50 via-white to-pink-50 p-6\">\r\n            <div className=\"max-w-7xl mx-auto space-y-8\">\r\n                {/* Header */}\r\n                <div className=\"bg-white rounded-2xl p-8 shadow-lg border border-blue-100\">\r\n                    <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6\">\r\n                        <div>\r\n                            <h1 className=\"text-4xl font-extrabold text-blue-800 leading-tight\">Attendance Dashboard</h1>\r\n                            <p className=\"text-blue-600 mt-2 text-lg\">Monitor attendance by batch, course, and subject.</p>\r\n                        </div>\r\n                        <div className=\"flex items-center gap-3\">\r\n                            <button className=\"flex items-center gap-2 px-5 py-2 bg-blue-700 text-white rounded-xl shadow hover:bg-blue-800 transition-colors\">\r\n                                <Download className=\"w-4 h-4\" />\r\n                                <span>Export</span>\r\n                            </button>\r\n                            <button className=\"flex items-center gap-2 px-5 py-2 border border-blue-300 text-blue-700 rounded-xl hover:bg-blue-50 transition-colors\">\r\n                                <Filter className=\"w-4 h-4\" />\r\n                                <span>Filter</span>\r\n                            </button>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* View Toggle */}\r\n                <div className=\"bg-white rounded-2xl p-2 shadow-sm border border-blue-100\">\r\n                    <div className=\"flex flex-wrap gap-2 justify-center\">\r\n                        {[\r\n                            { id: 'overall', label: 'Overall', icon: TrendingUp },\r\n                            { id: 'subjects', label: 'By Subject', icon: BookOpen },\r\n                            { id: 'batches', label: 'By Batch', icon: Users },\r\n                            { id: 'courses', label: 'By Course', icon: GraduationCap },\r\n                            { id: 'calendar', label: 'Calendar View', icon: Calendar }\r\n                        ].map(({ id, label, icon: Icon }) => (\r\n                            <button\r\n                                key={id}\r\n                                onClick={() => setActiveView(id)}\r\n                                className={`flex items-center gap-2 px-5 py-2 rounded-xl font-medium text-base transition-all\r\n                  ${activeView === id\r\n                                        ? 'bg-blue-700 text-white shadow'\r\n                                        : 'text-blue-700 hover:bg-blue-100'\r\n                                    }`}\r\n                            >\r\n                                <Icon className=\"w-4 h-4\" />\r\n                                <span>{label}</span>\r\n                            </button>\r\n                        ))}\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Filters */}\r\n                <div className=\"bg-white rounded-2xl p-6 shadow-md border border-blue-100\">\r\n                    <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\r\n                        <div>\r\n                            <label className=\"block text-sm font-semibold text-blue-700 mb-2\">Date Range</label>\r\n                            <select\r\n                                value={dateRange}\r\n                                onChange={(e) => setDateRange(e.target.value)}\r\n                                className=\"w-full px-3 py-2 border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                            >\r\n                                <option value=\"today\">Today</option>\r\n                                <option value=\"thisWeek\">This Week</option>\r\n                                <option value=\"thisMonth\">This Month</option>\r\n                                <option value=\"lastMonth\">Last Month</option>\r\n                                <option value=\"thisYear\">This Year</option>\r\n                            </select>\r\n                        </div>\r\n                        <div>\r\n                            <label className=\"block text-sm font-semibold text-blue-700 mb-2\">Batch</label>\r\n                            <select\r\n                                value={selectedBatch}\r\n                                onChange={(e) => {\r\n                                    setSelectedBatch(e.target.value);\r\n                                    // Add smooth transition to calendar view\r\n                                    if (e.target.value !== 'all') {\r\n                                        setTimeout(() => setActiveView('calendar'), 300);\r\n                                    }\r\n                                }}\r\n                                className=\"w-full px-3 py-2 border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\r\n                            >\r\n                                <option value=\"all\">All Batches</option>\r\n                                <option value=\"Batch A\">Batch A</option>\r\n                                <option value=\"Batch B\">Batch B</option>\r\n                                <option value=\"Batch C\">Batch C</option>\r\n                            </select>\r\n                        </div>\r\n                        <div>\r\n                            <label className=\"block text-sm font-semibold text-blue-700 mb-2\">Course</label>\r\n                            <select\r\n                                value={selectedCourse}\r\n                                onChange={(e) => {\r\n                                    setSelectedCourse(e.target.value);\r\n                                    // Add smooth transition to calendar view\r\n                                    if (e.target.value !== 'all') {\r\n                                        setTimeout(() => setActiveView('calendar'), 300);\r\n                                    }\r\n                                }}\r\n                                className=\"w-full px-3 py-2 border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\r\n                            >\r\n                                <option value=\"all\">All Courses</option>\r\n                                <option value=\"Engineering\">Engineering</option>\r\n                                <option value=\"Science\">Science</option>\r\n                                <option value=\"Commerce\">Commerce</option>\r\n                            </select>\r\n                        </div>\r\n                        <div>\r\n                            <label className=\"block text-sm font-semibold text-blue-700 mb-2\">Subject</label>\r\n                            <select\r\n                                value={selectedSubject}\r\n                                onChange={(e) => {\r\n                                    setSelectedSubject(e.target.value);\r\n                                    // Add smooth transition to calendar view\r\n                                    if (e.target.value !== 'all') {\r\n                                        setTimeout(() => setActiveView('calendar'), 300);\r\n                                    }\r\n                                }}\r\n                                className=\"w-full px-3 py-2 border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\r\n                            >\r\n                                <option value=\"all\">All Subjects</option>\r\n                                <option value=\"Mathematics\">Mathematics</option>\r\n                                <option value=\"Physics\">Physics</option>\r\n                                <option value=\"Chemistry\">Chemistry</option>\r\n                                <option value=\"Computer Science\">Computer Science</option>\r\n                                <option value=\"English\">English</option>\r\n                                <option value=\"Biology\">Biology</option>\r\n                                <option value=\"Accounting\">Accounting</option>\r\n                                <option value=\"Economics\">Economics</option>\r\n                                <option value=\"Business Studies\">Business Studies</option>\r\n                            </select>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Stats Cards */}\r\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\r\n                    <StatCard\r\n                        title=\"Overall Attendance\"\r\n                        value=\"85%\"\r\n                        subtitle=\"This month average\"\r\n                        icon={TrendingUp}\r\n                        trend={true}\r\n                    />\r\n                    <StatCard\r\n                        title=\"Total Classes\"\r\n                        value=\"124\"\r\n                        subtitle=\"Classes conducted\"\r\n                        icon={Calendar}\r\n                    />\r\n                    <StatCard\r\n                        title=\"Present Days\"\r\n                        value=\"105\"\r\n                        subtitle=\"Out of 124 classes\"\r\n                        icon={CheckCircle}\r\n                    />\r\n                </div>\r\n\r\n                {/* Main Content */}\r\n                <div className=\"grid grid-cols-1 xl:grid-cols-3 gap-8\">\r\n                    {/* Left Column - Main Data */}\r\n                    <div className=\"xl:col-span-2 space-y-8\">\r\n                        {activeView === 'overall' && (\r\n                            <div className=\"bg-white rounded-2xl p-8 shadow-md border border-blue-100\">\r\n                                <h3 className=\"text-xl font-bold text-blue-900 mb-6\">Overall Attendance Summary</h3>\r\n                                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\r\n                                    <div className=\"text-center p-8 bg-gradient-to-tr from-blue-50 via-blue-100 to-white rounded-xl shadow\">\r\n                                        <div className=\"text-5xl font-extrabold text-blue-700 mb-2 drop-shadow\">85%</div>\r\n                                        <div className=\"text-blue-800 font-semibold\">Overall Attendance</div>\r\n                                    </div>\r\n                                    <div className=\"space-y-5\">\r\n                                        <div className=\"flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-xl shadow-sm\">\r\n                                            <div className=\"flex items-center gap-3\">\r\n                                                <CheckCircle className=\"w-8 h-8 text-green-600\" />\r\n                                                <div>\r\n                                                    <div className=\"font-semibold text-green-900\">Present</div>\r\n                                                    <div className=\"text-sm text-green-700\">Days attended</div>\r\n                                                </div>\r\n                                            </div>\r\n                                            <div className=\"text-2xl font-extrabold text-green-600\">105</div>\r\n                                        </div>\r\n                                        <div className=\"flex items-center justify-between p-4 bg-red-50 border border-red-200 rounded-xl shadow-sm\">\r\n                                            <div className=\"flex items-center gap-3\">\r\n                                                <XCircle className=\"w-8 h-8 text-red-600\" />\r\n                                                <div>\r\n                                                    <div className=\"font-semibold text-red-900\">Absent</div>\r\n                                                    <div className=\"text-sm text-red-700\">Days missed</div>\r\n                                                </div>\r\n                                            </div>\r\n                                            <div className=\"text-2xl font-extrabold text-red-600\">19</div>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        )}\r\n\r\n                        {activeView === 'subjects' && (\r\n                            <div className=\"bg-white rounded-2xl p-8 shadow-md border border-blue-100\">\r\n                                <h3 className=\"text-xl font-bold text-blue-900 mb-6\">Subject-wise Attendance</h3>\r\n                                <div className=\"space-y-6\">\r\n                                    {attendanceData.subjects.map((subject, index) => (\r\n                                        <ProgressBar\r\n                                            key={index}\r\n                                            percentage={subject.percentage}\r\n                                            label={subject.name}\r\n                                            total={subject.total}\r\n                                            present={subject.present}\r\n                                        />\r\n                                    ))}\r\n                                </div>\r\n                            </div>\r\n                        )}\r\n\r\n                        {activeView === 'batches' && (\r\n                            <div className=\"bg-white rounded-2xl p-8 shadow-md border border-blue-100\">\r\n                                <h3 className=\"text-xl font-bold text-blue-900 mb-6\">Batch-wise Attendance</h3>\r\n                                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\r\n                                    {attendanceData.batches.map((batch, index) => (\r\n                                        <div key={index} className=\"p-6 bg-blue-50 rounded-xl border border-blue-200 shadow hover:scale-105 transition-transform\">\r\n                                            <div className=\"flex items-center justify-between mb-3\">\r\n                                                <h4 className=\"font-semibold text-blue-900\">{batch.name}</h4>\r\n                                                <Users className=\"w-5 h-5 text-blue-400\" />\r\n                                            </div>\r\n                                            <div className=\"space-y-2\">\r\n                                                <div className=\"text-2xl font-bold text-blue-900\">{batch.avgAttendance}%</div>\r\n                                                <div className=\"text-sm text-blue-700\">{batch.students} students</div>\r\n                                                <div className=\"w-full bg-blue-200 rounded-full h-2\">\r\n                                                    <div\r\n                                                        className=\"h-2 bg-blue-600 rounded-full transition-all duration-300\"\r\n                                                        style={{ width: `${batch.avgAttendance}%` }}\r\n                                                    ></div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                    ))}\r\n                                </div>\r\n                            </div>\r\n                        )}\r\n\r\n                        {activeView === 'courses' && (\r\n                            <div className=\"bg-white rounded-2xl p-8 shadow-md border border-blue-100\">\r\n                                <h3 className=\"text-xl font-bold text-blue-900 mb-6\">Course-wise Attendance</h3>\r\n                                <div className=\"space-y-6\">\r\n                                    {attendanceData.courses.map((course, index) => (\r\n                                        <div key={index} className=\"flex items-center justify-between p-5 border border-blue-200 rounded-xl bg-blue-50 hover:bg-blue-100 transition-colors shadow\">\r\n                                            <div className=\"flex items-center gap-4\">\r\n                                                <div className=\"p-3 bg-blue-700 rounded-lg\">\r\n                                                    <GraduationCap className=\"w-6 h-6 text-white\" />\r\n                                                </div>\r\n                                                <div>\r\n                                                    <h4 className=\"font-semibold text-blue-900\">{course.name}</h4>\r\n                                                    <p className=\"text-sm text-blue-700\">{course.students} students enrolled</p>\r\n                                                </div>\r\n                                            </div>\r\n                                            <div>\r\n                                                <div className={`text-lg font-bold px-4 py-1 rounded-full border ${getAttendanceColor(course.avgAttendance)}`}>\r\n                                                    {course.avgAttendance}%\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                    ))}\r\n                                </div>\r\n                            </div>\r\n                        )}\r\n\r\n                        {activeView === 'calendar' && (\r\n                            <div className=\"space-y-8\">\r\n                                {/* Enhanced Filtered Statistics Summary */}\r\n                                {(selectedCourse !== 'all' || selectedBatch !== 'all' || selectedSubject !== 'all') && (() => {\r\n                                    const filteredData = getFilteredAttendanceData();\r\n                                    const classRecords = filteredData.filter(record => record.type === 'class');\r\n                                    const presentCount = classRecords.filter(record => record.status === 'Present').length;\r\n                                    const absentCount = classRecords.filter(record => record.status === 'Absent').length;\r\n                                    const totalClasses = classRecords.length;\r\n                                    const attendancePercentage = totalClasses > 0 ? Math.round((presentCount / totalClasses) * 100) : 0;\r\n\r\n                                    return (\r\n                                        <div className=\"bg-gradient-to-br from-indigo-50 via-blue-50 to-purple-50 rounded-3xl p-8 shadow-xl border border-indigo-100 backdrop-blur-sm\">\r\n                                            <div className=\"flex items-center justify-between mb-6\">\r\n                                                <div>\r\n                                                    <h4 className=\"text-2xl font-bold text-indigo-900 mb-2\">Filtered Attendance Summary</h4>\r\n                                                    <div className=\"flex flex-wrap gap-2 text-sm\">\r\n                                                        {selectedCourse !== 'all' && (\r\n                                                            <span className=\"px-3 py-1 bg-indigo-100 text-indigo-700 rounded-full border border-indigo-200\">\r\n                                                                Course: {selectedCourse}\r\n                                                            </span>\r\n                                                        )}\r\n                                                        {selectedBatch !== 'all' && (\r\n                                                            <span className=\"px-3 py-1 bg-blue-100 text-blue-700 rounded-full border border-blue-200\">\r\n                                                                Batch: {selectedBatch}\r\n                                                            </span>\r\n                                                        )}\r\n                                                        {selectedSubject !== 'all' && (\r\n                                                            <span className=\"px-3 py-1 bg-purple-100 text-purple-700 rounded-full border border-purple-200\">\r\n                                                                Subject: {selectedSubject}\r\n                                                            </span>\r\n                                                        )}\r\n                                                    </div>\r\n                                                </div>\r\n                                                <div className=\"hidden md:block\">\r\n                                                    <div className=\"w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg\">\r\n                                                        <TrendingUp className=\"w-8 h-8 text-white\" />\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n\r\n                                            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\r\n                                                <div className=\"group relative overflow-hidden bg-gradient-to-br from-white to-indigo-50 rounded-2xl p-6 shadow-lg border border-indigo-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1\">\r\n                                                    <div className=\"absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-indigo-400 to-indigo-600 rounded-bl-3xl opacity-10\"></div>\r\n                                                    <div className=\"relative\">\r\n                                                        <div className=\"flex items-center justify-between mb-3\">\r\n                                                            <div className=\"p-2 bg-indigo-100 rounded-xl\">\r\n                                                                <TrendingUp className=\"w-5 h-5 text-indigo-600\" />\r\n                                                            </div>\r\n                                                            <div className={`text-xs px-2 py-1 rounded-full ${\r\n                                                                attendancePercentage >= 90 ? 'bg-green-100 text-green-700' :\r\n                                                                attendancePercentage >= 75 ? 'bg-yellow-100 text-yellow-700' :\r\n                                                                'bg-red-100 text-red-700'\r\n                                                            }`}>\r\n                                                                {attendancePercentage >= 90 ? 'Excellent' : attendancePercentage >= 75 ? 'Good' : 'Needs Improvement'}\r\n                                                            </div>\r\n                                                        </div>\r\n                                                        <div className=\"text-3xl font-bold text-indigo-700 mb-1\">{attendancePercentage}%</div>\r\n                                                        <div className=\"text-sm text-indigo-600 font-medium\">Attendance Rate</div>\r\n                                                    </div>\r\n                                                </div>\r\n\r\n                                                <div className=\"group relative overflow-hidden bg-gradient-to-br from-white to-green-50 rounded-2xl p-6 shadow-lg border border-green-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1\">\r\n                                                    <div className=\"absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-bl-3xl opacity-10\"></div>\r\n                                                    <div className=\"relative\">\r\n                                                        <div className=\"flex items-center justify-between mb-3\">\r\n                                                            <div className=\"p-2 bg-green-100 rounded-xl\">\r\n                                                                <CheckCircle className=\"w-5 h-5 text-green-600\" />\r\n                                                            </div>\r\n                                                            <div className=\"text-xs px-2 py-1 bg-green-100 text-green-700 rounded-full\">\r\n                                                                {totalClasses > 0 ? Math.round((presentCount / totalClasses) * 100) : 0}%\r\n                                                            </div>\r\n                                                        </div>\r\n                                                        <div className=\"text-3xl font-bold text-green-700 mb-1\">{presentCount}</div>\r\n                                                        <div className=\"text-sm text-green-600 font-medium\">Present Days</div>\r\n                                                    </div>\r\n                                                </div>\r\n\r\n                                                <div className=\"group relative overflow-hidden bg-gradient-to-br from-white to-red-50 rounded-2xl p-6 shadow-lg border border-red-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1\">\r\n                                                    <div className=\"absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-red-400 to-red-600 rounded-bl-3xl opacity-10\"></div>\r\n                                                    <div className=\"relative\">\r\n                                                        <div className=\"flex items-center justify-between mb-3\">\r\n                                                            <div className=\"p-2 bg-red-100 rounded-xl\">\r\n                                                                <XCircle className=\"w-5 h-5 text-red-600\" />\r\n                                                            </div>\r\n                                                            <div className=\"text-xs px-2 py-1 bg-red-100 text-red-700 rounded-full\">\r\n                                                                {totalClasses > 0 ? Math.round((absentCount / totalClasses) * 100) : 0}%\r\n                                                            </div>\r\n                                                        </div>\r\n                                                        <div className=\"text-3xl font-bold text-red-700 mb-1\">{absentCount}</div>\r\n                                                        <div className=\"text-sm text-red-600 font-medium\">Absent Days</div>\r\n                                                    </div>\r\n                                                </div>\r\n\r\n                                                <div className=\"group relative overflow-hidden bg-gradient-to-br from-white to-gray-50 rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1\">\r\n                                                    <div className=\"absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-gray-400 to-gray-600 rounded-bl-3xl opacity-10\"></div>\r\n                                                    <div className=\"relative\">\r\n                                                        <div className=\"flex items-center justify-between mb-3\">\r\n                                                            <div className=\"p-2 bg-gray-100 rounded-xl\">\r\n                                                                <Calendar className=\"w-5 h-5 text-gray-600\" />\r\n                                                            </div>\r\n                                                            <div className=\"text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded-full\">\r\n                                                                100%\r\n                                                            </div>\r\n                                                        </div>\r\n                                                        <div className=\"text-3xl font-bold text-gray-700 mb-1\">{totalClasses}</div>\r\n                                                        <div className=\"text-sm text-gray-600 font-medium\">Total Classes</div>\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                    );\r\n                                })()}\r\n\r\n                                <div className=\"bg-gradient-to-br from-white via-blue-50/30 to-indigo-50/50 rounded-3xl p-8 shadow-2xl border border-blue-100/50 backdrop-blur-sm\">\r\n                                    {/* Header Section */}\r\n                                    <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8\">\r\n                                        <div className=\"flex items-center gap-4\">\r\n                                            <div className=\"p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl shadow-lg\">\r\n                                                <Calendar className=\"w-7 h-7 text-white\" />\r\n                                            </div>\r\n                                            <div>\r\n                                                <h3 className=\"text-2xl font-bold text-blue-900\">Interactive Calendar</h3>\r\n                                                <p className=\"text-blue-600 text-sm mt-1\">\r\n                                                    {(selectedCourse === 'all' && selectedBatch === 'all' && selectedSubject === 'all')\r\n                                                        ? 'Showing all attendance records'\r\n                                                        : 'Filtered attendance view'}\r\n                                                </p>\r\n                                            </div>\r\n                                        </div>\r\n\r\n                                        {/* Quick Stats */}\r\n                                        <div className=\"flex items-center gap-4 mt-4 lg:mt-0\">\r\n                                            <div className=\"text-center\">\r\n                                                <div className=\"text-lg font-bold text-blue-700\">{new Date().toLocaleDateString('en-US', { month: 'short' })}</div>\r\n                                                <div className=\"text-xs text-blue-600\">Current Month</div>\r\n                                            </div>\r\n                                            <div className=\"w-px h-8 bg-blue-200\"></div>\r\n                                            <div className=\"text-center\">\r\n                                                <div className=\"text-lg font-bold text-blue-700\">{selectedDate.getDate()}</div>\r\n                                                <div className=\"text-xs text-blue-600\">Selected</div>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                    {/* Enhanced Calendar Legend */}\r\n                                    <div className=\"mb-8 p-6 bg-gradient-to-r from-slate-50 to-blue-50 rounded-2xl border border-slate-200 shadow-inner\">\r\n                                        <div className=\"flex items-center justify-between mb-4\">\r\n                                            <h4 className=\"text-lg font-bold text-slate-700 flex items-center gap-2\">\r\n                                                <Info className=\"w-5 h-5 text-blue-500\" />\r\n                                                Calendar Legend\r\n                                            </h4>\r\n                                            <div className=\"text-xs text-slate-500 bg-white px-3 py-1 rounded-full border\">\r\n                                                Click dates for details\r\n                                            </div>\r\n                                        </div>\r\n                                        <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4\">\r\n                                            <div className=\"flex items-center gap-3 p-3 bg-white rounded-xl shadow-sm border border-green-100 hover:shadow-md transition-shadow\">\r\n                                                <div className=\"w-6 h-6 bg-gradient-to-br from-green-200 to-green-300 border-2 border-green-400 rounded-lg shadow-sm\"></div>\r\n                                                <div>\r\n                                                    <span className=\"font-semibold text-green-700\">Present</span>\r\n                                                    <div className=\"text-xs text-green-600\">Attended class</div>\r\n                                                </div>\r\n                                            </div>\r\n                                            <div className=\"flex items-center gap-3 p-3 bg-white rounded-xl shadow-sm border border-red-100 hover:shadow-md transition-shadow\">\r\n                                                <div className=\"w-6 h-6 bg-gradient-to-br from-red-200 to-red-300 border-2 border-red-400 rounded-lg shadow-sm\"></div>\r\n                                                <div>\r\n                                                    <span className=\"font-semibold text-red-700\">Absent</span>\r\n                                                    <div className=\"text-xs text-red-600\">Missed class</div>\r\n                                                </div>\r\n                                            </div>\r\n                                            <div className=\"flex items-center gap-3 p-3 bg-white rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow\">\r\n                                                <div className=\"w-6 h-6 bg-gradient-to-br from-gray-200 to-gray-300 border-2 border-gray-400 rounded-lg shadow-sm\"></div>\r\n                                                <div>\r\n                                                    <span className=\"font-semibold text-gray-700\">Holiday</span>\r\n                                                    <div className=\"text-xs text-gray-600\">No classes</div>\r\n                                                </div>\r\n                                            </div>\r\n                                            <div className=\"flex items-center gap-3 p-3 bg-white rounded-xl shadow-sm border border-yellow-100 hover:shadow-md transition-shadow\">\r\n                                                <div className=\"w-6 h-6 bg-gradient-to-br from-yellow-200 to-yellow-300 border-2 border-yellow-400 rounded-lg shadow-sm\"></div>\r\n                                                <div>\r\n                                                    <span className=\"font-semibold text-yellow-700\">Mixed</span>\r\n                                                    <div className=\"text-xs text-yellow-600\">Partial attendance</div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                    {/* Enhanced Calendar Container */}\r\n                                    <div className=\"calendar-container relative\">\r\n                                        <div className=\"absolute inset-0 bg-gradient-to-br from-blue-100/20 to-indigo-100/20 rounded-2xl -z-10\"></div>\r\n                                        <div className=\"bg-white rounded-2xl shadow-xl border border-blue-100 overflow-hidden\">\r\n                                            <CalendarView\r\n                                                value={selectedDate}\r\n                                                onChange={handleDateChange}\r\n                                                tileContent={getTileContent}\r\n                                                tileClassName={getTileClassName}\r\n                                                className=\"enhanced-calendar w-full\"\r\n                                            />\r\n                                        </div>\r\n\r\n                                        {/* Calendar Navigation Helper */}\r\n                                        <div className=\"mt-4 flex items-center justify-center gap-4 text-sm text-blue-600\">\r\n                                            <div className=\"flex items-center gap-2\">\r\n                                                <div className=\"w-2 h-2 bg-blue-400 rounded-full\"></div>\r\n                                                <span>Use navigation arrows to browse months</span>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                    {/* Enhanced Selected Date Details */}\r\n                                    {selectedDate && (() => {\r\n                                        const attendance = getAttendanceForDate(selectedDate);\r\n                                        const today = new Date();\r\n                                        const isToday = selectedDate.toDateString() === today.toDateString();\r\n                                        const isFuture = selectedDate > today;\r\n\r\n                                        return (\r\n                                            <div className=\"mt-8 space-y-4\">\r\n                                                {/* Date Header */}\r\n                                                <div className=\"bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl p-6 text-white shadow-xl\">\r\n                                                    <div className=\"flex items-center justify-between\">\r\n                                                        <div>\r\n                                                            <h4 className=\"text-2xl font-bold mb-1\">\r\n                                                                {selectedDate.toLocaleDateString('en-US', {\r\n                                                                    weekday: 'long',\r\n                                                                    year: 'numeric',\r\n                                                                    month: 'long',\r\n                                                                    day: 'numeric'\r\n                                                                })}\r\n                                                            </h4>\r\n                                                            <div className=\"flex items-center gap-2 text-indigo-100\">\r\n                                                                {isToday && (\r\n                                                                    <span className=\"px-2 py-1 bg-white/20 rounded-full text-xs font-medium\">\r\n                                                                        Today\r\n                                                                    </span>\r\n                                                                )}\r\n                                                                {isFuture && (\r\n                                                                    <span className=\"px-2 py-1 bg-white/20 rounded-full text-xs font-medium\">\r\n                                                                        Future Date\r\n                                                                    </span>\r\n                                                                )}\r\n                                                                {attendance && (\r\n                                                                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${\r\n                                                                        attendance.status === 'present' ? 'bg-green-400/30 text-green-100' :\r\n                                                                        attendance.status === 'absent' ? 'bg-red-400/30 text-red-100' :\r\n                                                                        attendance.status === 'holiday' ? 'bg-gray-400/30 text-gray-100' :\r\n                                                                        'bg-yellow-400/30 text-yellow-100'\r\n                                                                    }`}>\r\n                                                                        {attendance.status === 'present' ? 'Present Day' :\r\n                                                                         attendance.status === 'absent' ? 'Absent Day' :\r\n                                                                         attendance.status === 'holiday' ? 'Holiday' :\r\n                                                                         'Mixed Attendance'}\r\n                                                                    </span>\r\n                                                                )}\r\n                                                            </div>\r\n                                                        </div>\r\n                                                        <div className=\"text-right\">\r\n                                                            <div className=\"text-3xl font-bold\">{selectedDate.getDate()}</div>\r\n                                                            <div className=\"text-sm text-indigo-200\">\r\n                                                                {selectedDate.toLocaleDateString('en-US', { month: 'short' })}\r\n                                                            </div>\r\n                                                        </div>\r\n                                                    </div>\r\n                                                </div>\r\n\r\n                                                {/* Attendance Details */}\r\n                                                {attendance && attendance.records.length > 0 ? (\r\n                                                    <div className=\"bg-white rounded-2xl p-6 shadow-lg border border-gray-100\">\r\n                                                        <div className=\"flex items-center justify-between mb-6\">\r\n                                                            <h5 className=\"text-lg font-bold text-gray-900\">Class Details</h5>\r\n                                                            <div className=\"text-sm text-gray-500\">\r\n                                                                {attendance.records.length} {attendance.records.length === 1 ? 'class' : 'classes'}\r\n                                                            </div>\r\n                                                        </div>\r\n                                                        <div className=\"space-y-3\">\r\n                                                            {attendance.records.map((record, index) => (\r\n                                                                <div key={index} className={`group relative overflow-hidden rounded-xl border-2 transition-all duration-300 hover:shadow-lg ${\r\n                                                                    record.status === 'Present' ? 'border-green-200 bg-gradient-to-r from-green-50 to-green-100/50 hover:border-green-300' :\r\n                                                                    record.status === 'Absent' ? 'border-red-200 bg-gradient-to-r from-red-50 to-red-100/50 hover:border-red-300' :\r\n                                                                    'border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100/50 hover:border-gray-300'\r\n                                                                }`}>\r\n                                                                    <div className=\"p-4\">\r\n                                                                        <div className=\"flex items-center justify-between\">\r\n                                                                            <div className=\"flex items-center gap-4\">\r\n                                                                                <div className={`p-3 rounded-xl shadow-sm ${\r\n                                                                                    record.status === 'Present' ? 'bg-green-100' :\r\n                                                                                    record.status === 'Absent' ? 'bg-red-100' :\r\n                                                                                    'bg-gray-100'\r\n                                                                                }`}>\r\n                                                                                    {getStatusIcon(record.status.toLowerCase())}\r\n                                                                                </div>\r\n                                                                                <div>\r\n                                                                                    <h6 className=\"font-bold text-gray-900 text-lg\">{record.subject}</h6>\r\n                                                                                    <div className=\"flex items-center gap-2 text-sm text-gray-600 mt-1\">\r\n                                                                                        <span className=\"px-2 py-1 bg-white/70 rounded-lg border\">\r\n                                                                                            {record.course}\r\n                                                                                        </span>\r\n                                                                                        <span className=\"px-2 py-1 bg-white/70 rounded-lg border\">\r\n                                                                                            {record.batch}\r\n                                                                                        </span>\r\n                                                                                        {record.type === 'holiday' && (\r\n                                                                                            <span className=\"px-2 py-1 bg-blue-100 text-blue-700 rounded-lg border border-blue-200\">\r\n                                                                                                Holiday\r\n                                                                                            </span>\r\n                                                                                        )}\r\n                                                                                    </div>\r\n                                                                                </div>\r\n                                                                            </div>\r\n                                                                            <div className=\"text-right\">\r\n                                                                                <span className={`inline-flex items-center px-4 py-2 rounded-xl font-semibold text-sm border-2 ${\r\n                                                                                    record.status === 'Present' ? 'bg-green-100 text-green-800 border-green-300' :\r\n                                                                                    record.status === 'Absent' ? 'bg-red-100 text-red-800 border-red-300' :\r\n                                                                                    'bg-gray-100 text-gray-800 border-gray-300'\r\n                                                                                }`}>\r\n                                                                                    {record.status}\r\n                                                                                </span>\r\n                                                                            </div>\r\n                                                                        </div>\r\n                                                                    </div>\r\n                                                                    {/* Decorative gradient overlay */}\r\n                                                                    <div className={`absolute top-0 right-0 w-1 h-full ${\r\n                                                                        record.status === 'Present' ? 'bg-gradient-to-b from-green-400 to-green-600' :\r\n                                                                        record.status === 'Absent' ? 'bg-gradient-to-b from-red-400 to-red-600' :\r\n                                                                        'bg-gradient-to-b from-gray-400 to-gray-600'\r\n                                                                    }`}></div>\r\n                                                                </div>\r\n                                                            ))}\r\n                                                        </div>\r\n                                                    </div>\r\n                                                ) : isFuture ? (\r\n                                                    <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8 text-center border border-blue-100\">\r\n                                                        <div className=\"w-16 h-16 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-2xl mx-auto mb-4 flex items-center justify-center\">\r\n                                                            <Calendar className=\"w-8 h-8 text-white\" />\r\n                                                        </div>\r\n                                                        <h5 className=\"text-lg font-bold text-blue-900 mb-2\">Future Date</h5>\r\n                                                        <p className=\"text-blue-600\">No attendance data available for future dates.</p>\r\n                                                    </div>\r\n                                                ) : (\r\n                                                    <div className=\"bg-gradient-to-r from-gray-50 to-slate-50 rounded-2xl p-8 text-center border border-gray-200\">\r\n                                                        <div className=\"w-16 h-16 bg-gradient-to-br from-gray-400 to-slate-500 rounded-2xl mx-auto mb-4 flex items-center justify-center\">\r\n                                                            <Calendar className=\"w-8 h-8 text-white\" />\r\n                                                        </div>\r\n                                                        <h5 className=\"text-lg font-bold text-gray-700 mb-2\">No Classes</h5>\r\n                                                        <p className=\"text-gray-600\">No classes were scheduled for this date.</p>\r\n                                                    </div>\r\n                                                )}\r\n                                            </div>\r\n                                        );\r\n                                    })()}\r\n                                </div>\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n\r\n                    {/* Right Column - Recent Activity */}\r\n                    <div className=\"bg-white rounded-2xl p-8 shadow-md border border-blue-100\">\r\n                        <h3 className=\"text-xl font-bold text-blue-900 mb-6 flex items-center gap-2\">\r\n                            <Clock className=\"w-5 h-5 text-blue-700\" />\r\n                            <span>Recent Activity</span>\r\n                        </h3>\r\n                        <div className=\"space-y-5\">\r\n                            {attendanceData.recentActivity.map((activity, index) => (\r\n                                <div key={index} className=\"flex items-center gap-3 p-4 bg-blue-50 rounded-xl border hover:bg-blue-100 transition-colors\">\r\n                                    {getStatusIcon(activity.status)}\r\n                                    <div className=\"flex-1\">\r\n                                        <div className=\"font-semibold text-blue-900\">{activity.subject}</div>\r\n                                        <div className=\"text-sm text-blue-700\">{activity.date} • {activity.time}</div>\r\n                                    </div>\r\n                                    <div className={`text-xs px-3 py-1 rounded-full capitalize border\r\n                    ${activity.status === 'present' ? 'bg-green-100 text-green-800 border-green-300' :\r\n                                            activity.status === 'absent' ? 'bg-red-100 text-red-800 border-red-300' :\r\n                                                'bg-yellow-100 text-yellow-800 border-yellow-300'\r\n                                        }`}>\r\n                                        {activity.status}\r\n                                    </div>\r\n                                </div>\r\n                            ))}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default AttendancePage;\r\n\r\n// Custom CSS styles for enhanced calendar\r\nconst calendarStyles = `\r\n.enhanced-calendar {\r\n    width: 100%;\r\n    border: none;\r\n    font-family: inherit;\r\n}\r\n\r\n.enhanced-calendar .react-calendar__tile {\r\n    position: relative;\r\n    padding: 0.75em 0.5em;\r\n    background: white;\r\n    border: 1px solid #e5e7eb;\r\n    transition: all 0.2s ease;\r\n}\r\n\r\n.enhanced-calendar .react-calendar__tile:hover {\r\n    background-color: #f3f4f6;\r\n}\r\n\r\n.enhanced-calendar .react-calendar__tile--active {\r\n    background: #3b82f6 !important;\r\n    color: white;\r\n}\r\n\r\n.enhanced-calendar .react-calendar__tile.present-day {\r\n    background-color: #dcfce7;\r\n    border-color: #16a34a;\r\n    color: #15803d;\r\n}\r\n\r\n.enhanced-calendar .react-calendar__tile.absent-day {\r\n    background-color: #fecaca;\r\n    border-color: #dc2626;\r\n    color: #b91c1c;\r\n}\r\n\r\n.enhanced-calendar .react-calendar__tile.holiday-day {\r\n    background-color: #f3f4f6;\r\n    border-color: #6b7280;\r\n    color: #4b5563;\r\n}\r\n\r\n.enhanced-calendar .react-calendar__tile.mixed-day {\r\n    background-color: #fef3c7;\r\n    border-color: #d97706;\r\n    color: #92400e;\r\n}\r\n\r\n.enhanced-calendar .react-calendar__tile.future-date {\r\n    background-color: #f9fafb;\r\n    color: #9ca3af;\r\n}\r\n\r\n.enhanced-calendar .react-calendar__tile.no-class {\r\n    background-color: #ffffff;\r\n    color: #6b7280;\r\n}\r\n\r\n.enhanced-calendar .react-calendar__navigation button {\r\n    background: #3b82f6;\r\n    color: white;\r\n    border: none;\r\n    padding: 0.5rem 1rem;\r\n    border-radius: 0.5rem;\r\n    font-weight: 500;\r\n}\r\n\r\n.enhanced-calendar .react-calendar__navigation button:hover {\r\n    background: #2563eb;\r\n}\r\n\r\n.enhanced-calendar .react-calendar__navigation button:disabled {\r\n    background: #9ca3af;\r\n}\r\n\r\n.enhanced-calendar .react-calendar__month-view__weekdays {\r\n    background: #f8fafc;\r\n    font-weight: 600;\r\n    color: #475569;\r\n}\r\n\r\n.enhanced-calendar .react-calendar__month-view__weekdays__weekday {\r\n    padding: 0.5rem;\r\n    text-align: center;\r\n}\r\n`;\r\n\r\n// Inject styles into the document\r\nif (typeof document !== 'undefined') {\r\n    const existingStyle = document.getElementById('calendar-styles');\r\n    if (!existingStyle) {\r\n        const styleElement = document.createElement('style');\r\n        styleElement.id = 'calendar-styles';\r\n        styleElement.textContent = calendarStyles;\r\n        document.head.appendChild(styleElement);\r\n    }\r\n}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACIC,QAAQ,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,MAAM,EAAEC,QAAQ,EAC1DC,UAAU,EAAEC,KAAK,EAAEC,WAAW,EAAEC,OAAO,EAAEC,WAAW,EAAEC,IAAI,QACvD,cAAc;AACrB,OAAO,kCAAkC;AACzC,OAAOC,YAAY,MAAM,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,SAAS,CAAC;EACvD,MAAM,CAACqB,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACuB,cAAc,EAAEC,iBAAiB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACyB,eAAe,EAAEC,kBAAkB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,WAAW,CAAC;EACvD,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC,IAAI+B,IAAI,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACC,cAAc,CAAC,GAAGhC,QAAQ,CAAC;IAC9BiC,OAAO,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAEC,MAAM,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAI,CAAC;IAChDC,QAAQ,EAAE,CACN;MAAEC,IAAI,EAAE,aAAa;MAAEJ,OAAO,EAAE,EAAE;MAAEE,KAAK,EAAE,EAAE;MAAEG,UAAU,EAAE;IAAG,CAAC,EAC/D;MAAED,IAAI,EAAE,SAAS;MAAEJ,OAAO,EAAE,EAAE;MAAEE,KAAK,EAAE,EAAE;MAAEG,UAAU,EAAE;IAAG,CAAC,EAC3D;MAAED,IAAI,EAAE,WAAW;MAAEJ,OAAO,EAAE,EAAE;MAAEE,KAAK,EAAE,EAAE;MAAEG,UAAU,EAAE;IAAG,CAAC,EAC7D;MAAED,IAAI,EAAE,kBAAkB;MAAEJ,OAAO,EAAE,EAAE;MAAEE,KAAK,EAAE,EAAE;MAAEG,UAAU,EAAE;IAAG,CAAC,EACpE;MAAED,IAAI,EAAE,SAAS;MAAEJ,OAAO,EAAE,EAAE;MAAEE,KAAK,EAAE,EAAE;MAAEG,UAAU,EAAE;IAAG,CAAC,CAC9D;IACDC,OAAO,EAAE,CACL;MAAEF,IAAI,EAAE,SAAS;MAAEG,QAAQ,EAAE,EAAE;MAAEC,aAAa,EAAE;IAAG,CAAC,EACpD;MAAEJ,IAAI,EAAE,SAAS;MAAEG,QAAQ,EAAE,EAAE;MAAEC,aAAa,EAAE;IAAG,CAAC,EACpD;MAAEJ,IAAI,EAAE,SAAS;MAAEG,QAAQ,EAAE,EAAE;MAAEC,aAAa,EAAE;IAAG,CAAC,CACvD;IACDC,OAAO,EAAE,CACL;MAAEL,IAAI,EAAE,aAAa;MAAEG,QAAQ,EAAE,GAAG;MAAEC,aAAa,EAAE;IAAG,CAAC,EACzD;MAAEJ,IAAI,EAAE,SAAS;MAAEG,QAAQ,EAAE,EAAE;MAAEC,aAAa,EAAE;IAAG,CAAC,EACpD;MAAEJ,IAAI,EAAE,UAAU;MAAEG,QAAQ,EAAE,EAAE;MAAEC,aAAa,EAAE;IAAG,CAAC,CACxD;IACDE,cAAc,EAAE,CACZ;MAAEC,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,aAAa;MAAEC,MAAM,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAW,CAAC,EACnF;MAAEH,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAW,CAAC,EAC/E;MAAEH,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,WAAW;MAAEC,MAAM,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAW,CAAC,EAChF;MAAEH,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,kBAAkB;MAAEC,MAAM,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAW,CAAC,EACxF;MAAEH,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAW,CAAC,CAClF;IACD;IACAC,YAAY,EAAE;IACV;IACA;MAAEJ,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,aAAa;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACzH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACrH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,WAAW;MAAEC,MAAM,EAAE,QAAQ;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACtH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,aAAa;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACzH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACrH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,kBAAkB;MAAEC,MAAM,EAAE,QAAQ;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EAC7H;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACrH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,KAAK;MAAEC,KAAK,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAU,CAAC,EAC3G;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,KAAK;MAAEC,KAAK,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAU,CAAC,EAC3G;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,aAAa;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACzH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,WAAW;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACvH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,QAAQ;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACpH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,kBAAkB;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EAC9H;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACrH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,aAAa;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACzH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,WAAW;MAAEC,MAAM,EAAE,QAAQ;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACtH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC;IAErH;IACA;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,SAAS;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACjH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,WAAW;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,SAAS;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACnH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,QAAQ;MAAEG,MAAM,EAAE,SAAS;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EAChH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,aAAa;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,SAAS;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACrH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,SAAS;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC;IAEjH;IACA;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,YAAY;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,UAAU;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACrH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,WAAW;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,UAAU;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACpH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,kBAAkB;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,UAAU;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EAC3H;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,YAAY;MAAEC,MAAM,EAAE,QAAQ;MAAEG,MAAM,EAAE,UAAU;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACpH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,WAAW;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,UAAU;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC;EAE5H,CAAC,CAAC;EAEF,MAAMC,aAAa,GAAIN,MAAM,IAAK;IAC9B,QAAQA,MAAM;MACV,KAAK,SAAS;QAAE,oBAAO/B,OAAA,CAACN,WAAW;UAAC4C,SAAS,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzE,KAAK,QAAQ;QAAE,oBAAO1C,OAAA,CAACL,OAAO;UAAC2C,SAAS,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAClE,KAAK,MAAM;QAAE,oBAAO1C,OAAA,CAACJ,WAAW;UAAC0C,SAAS,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvE;QAAS,OAAO,IAAI;IACxB;EACJ,CAAC;EAED,MAAMC,kBAAkB,GAAIpB,UAAU,IAAK;IACvC,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO,6CAA6C;IAC1E,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO,gDAAgD;IAC7E,OAAO,uCAAuC;EAClD,CAAC;EAED,MAAMqB,QAAQ,GAAGA,CAAC;IAAEC,KAAK;IAAEC,KAAK;IAAEC,QAAQ;IAAEC,IAAI,EAAEC,IAAI;IAAEC;EAAM,CAAC,kBAC3DlD,OAAA;IAAKsC,SAAS,EAAC,wIAAwI;IAAAa,QAAA,gBACnJnD,OAAA;MAAKsC,SAAS,EAAC,wCAAwC;MAAAa,QAAA,gBACnDnD,OAAA;QAAKsC,SAAS,EAAC,6BAA6B;QAAAa,QAAA,gBACxCnD,OAAA;UAAKsC,SAAS,EAAC,4BAA4B;UAAAa,QAAA,eACvCnD,OAAA,CAACiD,IAAI;YAACX,SAAS,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eACN1C,OAAA;UAAIsC,SAAS,EAAC,6BAA6B;UAAAa,QAAA,EAAEN;QAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,EACLQ,KAAK,iBAAIlD,OAAA,CAACR,UAAU;QAAC8C,SAAS,EAAC;MAAwB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CAAC,eACN1C,OAAA;MAAKsC,SAAS,EAAC,WAAW;MAAAa,QAAA,gBACtBnD,OAAA;QAAGsC,SAAS,EAAC,uCAAuC;QAAAa,QAAA,EAAEL;MAAK;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChE1C,OAAA;QAAGsC,SAAS,EAAC,uBAAuB;QAAAa,QAAA,EAAEJ;MAAQ;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACR;EAED,MAAMU,WAAW,GAAGA,CAAC;IAAE7B,UAAU;IAAE8B,KAAK;IAAEjC,KAAK;IAAEF;EAAQ,CAAC,kBACtDlB,OAAA;IAAKsC,SAAS,EAAC,WAAW;IAAAa,QAAA,gBACtBnD,OAAA;MAAKsC,SAAS,EAAC,mCAAmC;MAAAa,QAAA,gBAC9CnD,OAAA;QAAMsC,SAAS,EAAC,qCAAqC;QAAAa,QAAA,EAAEE;MAAK;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACpE1C,OAAA;QAAMsC,SAAS,EAAG,yCAAwCK,kBAAkB,CAACpB,UAAU,CAAE,EAAE;QAAA4B,QAAA,GACtF5B,UAAU,EAAC,GAChB;MAAA;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eACN1C,OAAA;MAAKsC,SAAS,EAAC,qCAAqC;MAAAa,QAAA,eAChDnD,OAAA;QACIsC,SAAS,EAAG,gDAA+Cf,UAAU,IAAI,EAAE,GAAG,cAAc,GAAGA,UAAU,IAAI,EAAE,GAAG,eAAe,GAAG,YAAa,EAAE;QACnJ+B,KAAK,EAAE;UAAEC,KAAK,EAAG,GAAEhC,UAAW;QAAG;MAAE;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eACN1C,OAAA;MAAKsC,SAAS,EAAC,4CAA4C;MAAAa,QAAA,gBACvDnD,OAAA;QAAAmD,QAAA,GAAM,WAAS,EAACjC,OAAO;MAAA;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC/B1C,OAAA;QAAAmD,QAAA,GAAM,SAAO,EAAC/B,KAAK;MAAA;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACR;;EAED;EACAzD,SAAS,CAAC,MAAM;IACZ,IAAIsB,cAAc,KAAK,KAAK,IAAIF,aAAa,KAAK,KAAK,IAAII,eAAe,KAAK,KAAK,EAAE;MAClFL,aAAa,CAAC,UAAU,CAAC;IAC7B;EACJ,CAAC,EAAE,CAACG,cAAc,EAAEF,aAAa,EAAEI,eAAe,CAAC,CAAC;;EAEpD;EACA,MAAM+C,yBAAyB,GAAGA,CAAA,KAAM;IACpC,OAAOxC,cAAc,CAACiB,YAAY,CAACwB,MAAM,CAACC,MAAM,IAAI;MAChD,MAAMC,WAAW,GAAGpD,cAAc,KAAK,KAAK,IAAImD,MAAM,CAACxB,MAAM,CAAC0B,WAAW,CAAC,CAAC,KAAKrD,cAAc,CAACqD,WAAW,CAAC,CAAC;MAC5G,MAAMC,UAAU,GAAGxD,aAAa,KAAK,KAAK,IAAIqD,MAAM,CAACvB,KAAK,CAACyB,WAAW,CAAC,CAAC,KAAKvD,aAAa,CAACuD,WAAW,CAAC,CAAC;MACxG,MAAME,YAAY,GAAGrD,eAAe,KAAK,KAAK,IAAIiD,MAAM,CAAC5B,OAAO,CAAC8B,WAAW,CAAC,CAAC,KAAKnD,eAAe,CAACmD,WAAW,CAAC,CAAC;MAEhH,OAAOD,WAAW,IAAIE,UAAU,IAAIC,YAAY;IACpD,CAAC,CAAC;EACN,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAIlC,IAAI,IAAK;IACnC,MAAMmC,OAAO,GAAGnC,IAAI,CAACoC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAChD,MAAMC,YAAY,GAAGX,yBAAyB,CAAC,CAAC;IAChD,MAAMY,UAAU,GAAGD,YAAY,CAACV,MAAM,CAACC,MAAM,IAAIA,MAAM,CAAC7B,IAAI,KAAKmC,OAAO,CAAC;IAEzE,IAAII,UAAU,CAACC,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;;IAExC;IACA,IAAID,UAAU,CAACE,IAAI,CAACZ,MAAM,IAAIA,MAAM,CAACtB,IAAI,KAAK,SAAS,CAAC,EAAE;MACtD,OAAO;QAAEL,MAAM,EAAE,SAAS;QAAEwC,OAAO,EAAEH;MAAW,CAAC;IACrD;;IAEA;IACA,MAAMI,YAAY,GAAGJ,UAAU,CAACX,MAAM,CAACC,MAAM,IAAIA,MAAM,CAAC3B,MAAM,KAAK,SAAS,CAAC,CAACsC,MAAM;IACpF,MAAMI,WAAW,GAAGL,UAAU,CAACX,MAAM,CAACC,MAAM,IAAIA,MAAM,CAAC3B,MAAM,KAAK,QAAQ,CAAC,CAACsC,MAAM;IAElF,IAAIG,YAAY,GAAG,CAAC,IAAIC,WAAW,KAAK,CAAC,EAAE,OAAO;MAAE1C,MAAM,EAAE,SAAS;MAAEwC,OAAO,EAAEH;IAAW,CAAC;IAC5F,IAAIK,WAAW,GAAG,CAAC,IAAID,YAAY,KAAK,CAAC,EAAE,OAAO;MAAEzC,MAAM,EAAE,QAAQ;MAAEwC,OAAO,EAAEH;IAAW,CAAC;IAC3F,IAAII,YAAY,GAAG,CAAC,IAAIC,WAAW,GAAG,CAAC,EAAE,OAAO;MAAE1C,MAAM,EAAE,OAAO;MAAEwC,OAAO,EAAEH;IAAW,CAAC;IAExF,OAAO;MAAErC,MAAM,EAAE,SAAS;MAAEwC,OAAO,EAAEH;IAAW,CAAC;EACrD,CAAC;;EAED;EACA,MAAMM,cAAc,GAAGA,CAAC;IAAE7C,IAAI;IAAE8C;EAAK,CAAC,KAAK;IACvC,IAAIA,IAAI,KAAK,OAAO,EAAE,OAAO,IAAI;IAEjC,MAAMC,UAAU,GAAGb,oBAAoB,CAAClC,IAAI,CAAC;IAC7C,IAAI,CAAC+C,UAAU,EAAE,OAAO,IAAI;IAE5B,MAAMC,KAAK,GAAG,IAAI9D,IAAI,CAAC,CAAC;IACxB,MAAM+D,QAAQ,GAAGjD,IAAI,GAAGgD,KAAK;IAE7B,IAAIC,QAAQ,EAAE,OAAO,IAAI;IAEzB,oBACI9E,OAAA;MAAKsC,SAAS,EAAC,0BAA0B;MAAAa,QAAA,eACrCnD,OAAA;QAAKsC,SAAS,EAAG,wBACbsC,UAAU,CAAC7C,MAAM,KAAK,SAAS,GAAG,cAAc,GAChD6C,UAAU,CAAC7C,MAAM,KAAK,QAAQ,GAAG,YAAY,GAC7C6C,UAAU,CAAC7C,MAAM,KAAK,SAAS,GAAG,aAAa,GAC/C6C,UAAU,CAAC7C,MAAM,KAAK,OAAO,GAAG,eAAe,GAC/C,aACH;MAAE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd,CAAC;;EAED;EACA,MAAMqC,gBAAgB,GAAGA,CAAC;IAAElD,IAAI;IAAE8C;EAAK,CAAC,KAAK;IACzC,IAAIA,IAAI,KAAK,OAAO,EAAE,OAAO,IAAI;IAEjC,MAAMC,UAAU,GAAGb,oBAAoB,CAAClC,IAAI,CAAC;IAC7C,MAAMgD,KAAK,GAAG,IAAI9D,IAAI,CAAC,CAAC;IACxB,MAAM+D,QAAQ,GAAGjD,IAAI,GAAGgD,KAAK;IAE7B,IAAIC,QAAQ,EAAE;MACV,OAAO,aAAa;IACxB;IAEA,IAAI,CAACF,UAAU,EAAE,OAAO,UAAU;IAElC,QAAQA,UAAU,CAAC7C,MAAM;MACrB,KAAK,SAAS;QACV,OAAO,aAAa;MACxB,KAAK,QAAQ;QACT,OAAO,YAAY;MACvB,KAAK,SAAS;QACV,OAAO,aAAa;MACxB,KAAK,OAAO;QACR,OAAO,WAAW;MACtB;QACI,OAAO,UAAU;IACzB;EACJ,CAAC;EAED,MAAMiD,gBAAgB,GAAInD,IAAI,IAAK;IAC/Bf,eAAe,CAACe,IAAI,CAAC;EACzB,CAAC;EAED,oBACI7B,OAAA;IAAKsC,SAAS,EAAC,sEAAsE;IAAAa,QAAA,eACjFnD,OAAA;MAAKsC,SAAS,EAAC,6BAA6B;MAAAa,QAAA,gBAExCnD,OAAA;QAAKsC,SAAS,EAAC,2DAA2D;QAAAa,QAAA,eACtEnD,OAAA;UAAKsC,SAAS,EAAC,oEAAoE;UAAAa,QAAA,gBAC/EnD,OAAA;YAAAmD,QAAA,gBACInD,OAAA;cAAIsC,SAAS,EAAC,qDAAqD;cAAAa,QAAA,EAAC;YAAoB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7F1C,OAAA;cAAGsC,SAAS,EAAC,4BAA4B;cAAAa,QAAA,EAAC;YAAiD;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9F,CAAC,eACN1C,OAAA;YAAKsC,SAAS,EAAC,yBAAyB;YAAAa,QAAA,gBACpCnD,OAAA;cAAQsC,SAAS,EAAC,gHAAgH;cAAAa,QAAA,gBAC9HnD,OAAA,CAACT,QAAQ;gBAAC+C,SAAS,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChC1C,OAAA;gBAAAmD,QAAA,EAAM;cAAM;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACT1C,OAAA;cAAQsC,SAAS,EAAC,sHAAsH;cAAAa,QAAA,gBACpInD,OAAA,CAACV,MAAM;gBAACgD,SAAS,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9B1C,OAAA;gBAAAmD,QAAA,EAAM;cAAM;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGN1C,OAAA;QAAKsC,SAAS,EAAC,2DAA2D;QAAAa,QAAA,eACtEnD,OAAA;UAAKsC,SAAS,EAAC,qCAAqC;UAAAa,QAAA,EAC/C,CACG;YAAE8B,EAAE,EAAE,SAAS;YAAE5B,KAAK,EAAE,SAAS;YAAEL,IAAI,EAAExD;UAAW,CAAC,EACrD;YAAEyF,EAAE,EAAE,UAAU;YAAE5B,KAAK,EAAE,YAAY;YAAEL,IAAI,EAAE5D;UAAS,CAAC,EACvD;YAAE6F,EAAE,EAAE,SAAS;YAAE5B,KAAK,EAAE,UAAU;YAAEL,IAAI,EAAE7D;UAAM,CAAC,EACjD;YAAE8F,EAAE,EAAE,SAAS;YAAE5B,KAAK,EAAE,WAAW;YAAEL,IAAI,EAAE3D;UAAc,CAAC,EAC1D;YAAE4F,EAAE,EAAE,UAAU;YAAE5B,KAAK,EAAE,eAAe;YAAEL,IAAI,EAAE9D;UAAS,CAAC,CAC7D,CAACgG,GAAG,CAAC,CAAC;YAAED,EAAE;YAAE5B,KAAK;YAAEL,IAAI,EAAEC;UAAK,CAAC,kBAC5BjD,OAAA;YAEImF,OAAO,EAAEA,CAAA,KAAM/E,aAAa,CAAC6E,EAAE,CAAE;YACjC3C,SAAS,EAAG;AAC5C,oBAAoBnC,UAAU,KAAK8E,EAAE,GACK,+BAA+B,GAC/B,iCACL,EAAE;YAAA9B,QAAA,gBAEPnD,OAAA,CAACiD,IAAI;cAACX,SAAS,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5B1C,OAAA;cAAAmD,QAAA,EAAOE;YAAK;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GATfuC,EAAE;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUH,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGN1C,OAAA;QAAKsC,SAAS,EAAC,2DAA2D;QAAAa,QAAA,eACtEnD,OAAA;UAAKsC,SAAS,EAAC,uCAAuC;UAAAa,QAAA,gBAClDnD,OAAA;YAAAmD,QAAA,gBACInD,OAAA;cAAOsC,SAAS,EAAC,gDAAgD;cAAAa,QAAA,EAAC;YAAU;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpF1C,OAAA;cACI8C,KAAK,EAAEnC,SAAU;cACjByE,QAAQ,EAAGC,CAAC,IAAKzE,YAAY,CAACyE,CAAC,CAACC,MAAM,CAACxC,KAAK,CAAE;cAC9CR,SAAS,EAAC,8GAA8G;cAAAa,QAAA,gBAExHnD,OAAA;gBAAQ8C,KAAK,EAAC,OAAO;gBAAAK,QAAA,EAAC;cAAK;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpC1C,OAAA;gBAAQ8C,KAAK,EAAC,UAAU;gBAAAK,QAAA,EAAC;cAAS;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC3C1C,OAAA;gBAAQ8C,KAAK,EAAC,WAAW;gBAAAK,QAAA,EAAC;cAAU;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC7C1C,OAAA;gBAAQ8C,KAAK,EAAC,WAAW;gBAAAK,QAAA,EAAC;cAAU;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC7C1C,OAAA;gBAAQ8C,KAAK,EAAC,UAAU;gBAAAK,QAAA,EAAC;cAAS;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACN1C,OAAA;YAAAmD,QAAA,gBACInD,OAAA;cAAOsC,SAAS,EAAC,gDAAgD;cAAAa,QAAA,EAAC;YAAK;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/E1C,OAAA;cACI8C,KAAK,EAAEzC,aAAc;cACrB+E,QAAQ,EAAGC,CAAC,IAAK;gBACb/E,gBAAgB,CAAC+E,CAAC,CAACC,MAAM,CAACxC,KAAK,CAAC;gBAChC;gBACA,IAAIuC,CAAC,CAACC,MAAM,CAACxC,KAAK,KAAK,KAAK,EAAE;kBAC1ByC,UAAU,CAAC,MAAMnF,aAAa,CAAC,UAAU,CAAC,EAAE,GAAG,CAAC;gBACpD;cACJ,CAAE;cACFkC,SAAS,EAAC,0IAA0I;cAAAa,QAAA,gBAEpJnD,OAAA;gBAAQ8C,KAAK,EAAC,KAAK;gBAAAK,QAAA,EAAC;cAAW;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC1C,OAAA;gBAAQ8C,KAAK,EAAC,SAAS;gBAAAK,QAAA,EAAC;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC1C,OAAA;gBAAQ8C,KAAK,EAAC,SAAS;gBAAAK,QAAA,EAAC;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC1C,OAAA;gBAAQ8C,KAAK,EAAC,SAAS;gBAAAK,QAAA,EAAC;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACN1C,OAAA;YAAAmD,QAAA,gBACInD,OAAA;cAAOsC,SAAS,EAAC,gDAAgD;cAAAa,QAAA,EAAC;YAAM;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChF1C,OAAA;cACI8C,KAAK,EAAEvC,cAAe;cACtB6E,QAAQ,EAAGC,CAAC,IAAK;gBACb7E,iBAAiB,CAAC6E,CAAC,CAACC,MAAM,CAACxC,KAAK,CAAC;gBACjC;gBACA,IAAIuC,CAAC,CAACC,MAAM,CAACxC,KAAK,KAAK,KAAK,EAAE;kBAC1ByC,UAAU,CAAC,MAAMnF,aAAa,CAAC,UAAU,CAAC,EAAE,GAAG,CAAC;gBACpD;cACJ,CAAE;cACFkC,SAAS,EAAC,0IAA0I;cAAAa,QAAA,gBAEpJnD,OAAA;gBAAQ8C,KAAK,EAAC,KAAK;gBAAAK,QAAA,EAAC;cAAW;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC1C,OAAA;gBAAQ8C,KAAK,EAAC,aAAa;gBAAAK,QAAA,EAAC;cAAW;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChD1C,OAAA;gBAAQ8C,KAAK,EAAC,SAAS;gBAAAK,QAAA,EAAC;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC1C,OAAA;gBAAQ8C,KAAK,EAAC,UAAU;gBAAAK,QAAA,EAAC;cAAQ;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACN1C,OAAA;YAAAmD,QAAA,gBACInD,OAAA;cAAOsC,SAAS,EAAC,gDAAgD;cAAAa,QAAA,EAAC;YAAO;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjF1C,OAAA;cACI8C,KAAK,EAAErC,eAAgB;cACvB2E,QAAQ,EAAGC,CAAC,IAAK;gBACb3E,kBAAkB,CAAC2E,CAAC,CAACC,MAAM,CAACxC,KAAK,CAAC;gBAClC;gBACA,IAAIuC,CAAC,CAACC,MAAM,CAACxC,KAAK,KAAK,KAAK,EAAE;kBAC1ByC,UAAU,CAAC,MAAMnF,aAAa,CAAC,UAAU,CAAC,EAAE,GAAG,CAAC;gBACpD;cACJ,CAAE;cACFkC,SAAS,EAAC,0IAA0I;cAAAa,QAAA,gBAEpJnD,OAAA;gBAAQ8C,KAAK,EAAC,KAAK;gBAAAK,QAAA,EAAC;cAAY;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACzC1C,OAAA;gBAAQ8C,KAAK,EAAC,aAAa;gBAAAK,QAAA,EAAC;cAAW;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChD1C,OAAA;gBAAQ8C,KAAK,EAAC,SAAS;gBAAAK,QAAA,EAAC;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC1C,OAAA;gBAAQ8C,KAAK,EAAC,WAAW;gBAAAK,QAAA,EAAC;cAAS;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5C1C,OAAA;gBAAQ8C,KAAK,EAAC,kBAAkB;gBAAAK,QAAA,EAAC;cAAgB;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1D1C,OAAA;gBAAQ8C,KAAK,EAAC,SAAS;gBAAAK,QAAA,EAAC;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC1C,OAAA;gBAAQ8C,KAAK,EAAC,SAAS;gBAAAK,QAAA,EAAC;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC1C,OAAA;gBAAQ8C,KAAK,EAAC,YAAY;gBAAAK,QAAA,EAAC;cAAU;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9C1C,OAAA;gBAAQ8C,KAAK,EAAC,WAAW;gBAAAK,QAAA,EAAC;cAAS;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5C1C,OAAA;gBAAQ8C,KAAK,EAAC,kBAAkB;gBAAAK,QAAA,EAAC;cAAgB;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGN1C,OAAA;QAAKsC,SAAS,EAAC,uCAAuC;QAAAa,QAAA,gBAClDnD,OAAA,CAAC4C,QAAQ;UACLC,KAAK,EAAC,oBAAoB;UAC1BC,KAAK,EAAC,KAAK;UACXC,QAAQ,EAAC,oBAAoB;UAC7BC,IAAI,EAAExD,UAAW;UACjB0D,KAAK,EAAE;QAAK;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACF1C,OAAA,CAAC4C,QAAQ;UACLC,KAAK,EAAC,eAAe;UACrBC,KAAK,EAAC,KAAK;UACXC,QAAQ,EAAC,mBAAmB;UAC5BC,IAAI,EAAE9D;QAAS;UAAAqD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACF1C,OAAA,CAAC4C,QAAQ;UACLC,KAAK,EAAC,cAAc;UACpBC,KAAK,EAAC,KAAK;UACXC,QAAQ,EAAC,oBAAoB;UAC7BC,IAAI,EAAEtD;QAAY;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGN1C,OAAA;QAAKsC,SAAS,EAAC,uCAAuC;QAAAa,QAAA,gBAElDnD,OAAA;UAAKsC,SAAS,EAAC,yBAAyB;UAAAa,QAAA,GACnChD,UAAU,KAAK,SAAS,iBACrBH,OAAA;YAAKsC,SAAS,EAAC,2DAA2D;YAAAa,QAAA,gBACtEnD,OAAA;cAAIsC,SAAS,EAAC,sCAAsC;cAAAa,QAAA,EAAC;YAA0B;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpF1C,OAAA;cAAKsC,SAAS,EAAC,uCAAuC;cAAAa,QAAA,gBAClDnD,OAAA;gBAAKsC,SAAS,EAAC,wFAAwF;gBAAAa,QAAA,gBACnGnD,OAAA;kBAAKsC,SAAS,EAAC,wDAAwD;kBAAAa,QAAA,EAAC;gBAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjF1C,OAAA;kBAAKsC,SAAS,EAAC,6BAA6B;kBAAAa,QAAA,EAAC;gBAAkB;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC,eACN1C,OAAA;gBAAKsC,SAAS,EAAC,WAAW;gBAAAa,QAAA,gBACtBnD,OAAA;kBAAKsC,SAAS,EAAC,gGAAgG;kBAAAa,QAAA,gBAC3GnD,OAAA;oBAAKsC,SAAS,EAAC,yBAAyB;oBAAAa,QAAA,gBACpCnD,OAAA,CAACN,WAAW;sBAAC4C,SAAS,EAAC;oBAAwB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAClD1C,OAAA;sBAAAmD,QAAA,gBACInD,OAAA;wBAAKsC,SAAS,EAAC,8BAA8B;wBAAAa,QAAA,EAAC;sBAAO;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC3D1C,OAAA;wBAAKsC,SAAS,EAAC,wBAAwB;wBAAAa,QAAA,EAAC;sBAAa;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACN1C,OAAA;oBAAKsC,SAAS,EAAC,wCAAwC;oBAAAa,QAAA,EAAC;kBAAG;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC,eACN1C,OAAA;kBAAKsC,SAAS,EAAC,4FAA4F;kBAAAa,QAAA,gBACvGnD,OAAA;oBAAKsC,SAAS,EAAC,yBAAyB;oBAAAa,QAAA,gBACpCnD,OAAA,CAACL,OAAO;sBAAC2C,SAAS,EAAC;oBAAsB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC5C1C,OAAA;sBAAAmD,QAAA,gBACInD,OAAA;wBAAKsC,SAAS,EAAC,4BAA4B;wBAAAa,QAAA,EAAC;sBAAM;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACxD1C,OAAA;wBAAKsC,SAAS,EAAC,sBAAsB;wBAAAa,QAAA,EAAC;sBAAW;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACN1C,OAAA;oBAAKsC,SAAS,EAAC,sCAAsC;oBAAAa,QAAA,EAAC;kBAAE;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACR,EAEAvC,UAAU,KAAK,UAAU,iBACtBH,OAAA;YAAKsC,SAAS,EAAC,2DAA2D;YAAAa,QAAA,gBACtEnD,OAAA;cAAIsC,SAAS,EAAC,sCAAsC;cAAAa,QAAA,EAAC;YAAuB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjF1C,OAAA;cAAKsC,SAAS,EAAC,WAAW;cAAAa,QAAA,EACrBnC,cAAc,CAACK,QAAQ,CAAC6D,GAAG,CAAC,CAACpD,OAAO,EAAE0D,KAAK,kBACxCxF,OAAA,CAACoD,WAAW;gBAER7B,UAAU,EAAEO,OAAO,CAACP,UAAW;gBAC/B8B,KAAK,EAAEvB,OAAO,CAACR,IAAK;gBACpBF,KAAK,EAAEU,OAAO,CAACV,KAAM;gBACrBF,OAAO,EAAEY,OAAO,CAACZ;cAAQ,GAJpBsE,KAAK;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKb,CACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACR,EAEAvC,UAAU,KAAK,SAAS,iBACrBH,OAAA;YAAKsC,SAAS,EAAC,2DAA2D;YAAAa,QAAA,gBACtEnD,OAAA;cAAIsC,SAAS,EAAC,sCAAsC;cAAAa,QAAA,EAAC;YAAqB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/E1C,OAAA;cAAKsC,SAAS,EAAC,uCAAuC;cAAAa,QAAA,EACjDnC,cAAc,CAACQ,OAAO,CAAC0D,GAAG,CAAC,CAAC/C,KAAK,EAAEqD,KAAK,kBACrCxF,OAAA;gBAAiBsC,SAAS,EAAC,8FAA8F;gBAAAa,QAAA,gBACrHnD,OAAA;kBAAKsC,SAAS,EAAC,wCAAwC;kBAAAa,QAAA,gBACnDnD,OAAA;oBAAIsC,SAAS,EAAC,6BAA6B;oBAAAa,QAAA,EAAEhB,KAAK,CAACb;kBAAI;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC7D1C,OAAA,CAACb,KAAK;oBAACmD,SAAS,EAAC;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC,eACN1C,OAAA;kBAAKsC,SAAS,EAAC,WAAW;kBAAAa,QAAA,gBACtBnD,OAAA;oBAAKsC,SAAS,EAAC,kCAAkC;oBAAAa,QAAA,GAAEhB,KAAK,CAACT,aAAa,EAAC,GAAC;kBAAA;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC9E1C,OAAA;oBAAKsC,SAAS,EAAC,uBAAuB;oBAAAa,QAAA,GAAEhB,KAAK,CAACV,QAAQ,EAAC,WAAS;kBAAA;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACtE1C,OAAA;oBAAKsC,SAAS,EAAC,qCAAqC;oBAAAa,QAAA,eAChDnD,OAAA;sBACIsC,SAAS,EAAC,0DAA0D;sBACpEgB,KAAK,EAAE;wBAAEC,KAAK,EAAG,GAAEpB,KAAK,CAACT,aAAc;sBAAG;oBAAE;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1C;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA,GAdA8C,KAAK;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAeV,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACR,EAEAvC,UAAU,KAAK,SAAS,iBACrBH,OAAA;YAAKsC,SAAS,EAAC,2DAA2D;YAAAa,QAAA,gBACtEnD,OAAA;cAAIsC,SAAS,EAAC,sCAAsC;cAAAa,QAAA,EAAC;YAAsB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChF1C,OAAA;cAAKsC,SAAS,EAAC,WAAW;cAAAa,QAAA,EACrBnC,cAAc,CAACW,OAAO,CAACuD,GAAG,CAAC,CAAChD,MAAM,EAAEsD,KAAK,kBACtCxF,OAAA;gBAAiBsC,SAAS,EAAC,+HAA+H;gBAAAa,QAAA,gBACtJnD,OAAA;kBAAKsC,SAAS,EAAC,yBAAyB;kBAAAa,QAAA,gBACpCnD,OAAA;oBAAKsC,SAAS,EAAC,4BAA4B;oBAAAa,QAAA,eACvCnD,OAAA,CAACX,aAAa;sBAACiD,SAAS,EAAC;oBAAoB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CAAC,eACN1C,OAAA;oBAAAmD,QAAA,gBACInD,OAAA;sBAAIsC,SAAS,EAAC,6BAA6B;sBAAAa,QAAA,EAAEjB,MAAM,CAACZ;oBAAI;sBAAAiB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC9D1C,OAAA;sBAAGsC,SAAS,EAAC,uBAAuB;sBAAAa,QAAA,GAAEjB,MAAM,CAACT,QAAQ,EAAC,oBAAkB;oBAAA;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACN1C,OAAA;kBAAAmD,QAAA,eACInD,OAAA;oBAAKsC,SAAS,EAAG,mDAAkDK,kBAAkB,CAACT,MAAM,CAACR,aAAa,CAAE,EAAE;oBAAAyB,QAAA,GACzGjB,MAAM,CAACR,aAAa,EAAC,GAC1B;kBAAA;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA,GAdA8C,KAAK;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAeV,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACR,EAEAvC,UAAU,KAAK,UAAU,iBACtBH,OAAA;YAAKsC,SAAS,EAAC,WAAW;YAAAa,QAAA,GAErB,CAAC5C,cAAc,KAAK,KAAK,IAAIF,aAAa,KAAK,KAAK,IAAII,eAAe,KAAK,KAAK,KAAK,CAAC,MAAM;cAC1F,MAAM0D,YAAY,GAAGX,yBAAyB,CAAC,CAAC;cAChD,MAAMiC,YAAY,GAAGtB,YAAY,CAACV,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACtB,IAAI,KAAK,OAAO,CAAC;cAC3E,MAAMoC,YAAY,GAAGiB,YAAY,CAAChC,MAAM,CAACC,MAAM,IAAIA,MAAM,CAAC3B,MAAM,KAAK,SAAS,CAAC,CAACsC,MAAM;cACtF,MAAMI,WAAW,GAAGgB,YAAY,CAAChC,MAAM,CAACC,MAAM,IAAIA,MAAM,CAAC3B,MAAM,KAAK,QAAQ,CAAC,CAACsC,MAAM;cACpF,MAAMqB,YAAY,GAAGD,YAAY,CAACpB,MAAM;cACxC,MAAMsB,oBAAoB,GAAGD,YAAY,GAAG,CAAC,GAAGE,IAAI,CAACC,KAAK,CAAErB,YAAY,GAAGkB,YAAY,GAAI,GAAG,CAAC,GAAG,CAAC;cAEnG,oBACI1F,OAAA;gBAAKsC,SAAS,EAAC,+HAA+H;gBAAAa,QAAA,gBAC1InD,OAAA;kBAAKsC,SAAS,EAAC,wCAAwC;kBAAAa,QAAA,gBACnDnD,OAAA;oBAAAmD,QAAA,gBACInD,OAAA;sBAAIsC,SAAS,EAAC,yCAAyC;sBAAAa,QAAA,EAAC;oBAA2B;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACxF1C,OAAA;sBAAKsC,SAAS,EAAC,8BAA8B;sBAAAa,QAAA,GACxC5C,cAAc,KAAK,KAAK,iBACrBP,OAAA;wBAAMsC,SAAS,EAAC,+EAA+E;wBAAAa,QAAA,GAAC,UACpF,EAAC5C,cAAc;sBAAA;wBAAAgC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrB,CACT,EACArC,aAAa,KAAK,KAAK,iBACpBL,OAAA;wBAAMsC,SAAS,EAAC,yEAAyE;wBAAAa,QAAA,GAAC,SAC/E,EAAC9C,aAAa;sBAAA;wBAAAkC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CACT,EACAjC,eAAe,KAAK,KAAK,iBACtBT,OAAA;wBAAMsC,SAAS,EAAC,+EAA+E;wBAAAa,QAAA,GAAC,WACnF,EAAC1C,eAAe;sBAAA;wBAAA8B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvB,CACT;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACN1C,OAAA;oBAAKsC,SAAS,EAAC,iBAAiB;oBAAAa,QAAA,eAC5BnD,OAAA;sBAAKsC,SAAS,EAAC,kHAAkH;sBAAAa,QAAA,eAC7HnD,OAAA,CAACR,UAAU;wBAAC8C,SAAS,EAAC;sBAAoB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eAEN1C,OAAA;kBAAKsC,SAAS,EAAC,uCAAuC;kBAAAa,QAAA,gBAClDnD,OAAA;oBAAKsC,SAAS,EAAC,8LAA8L;oBAAAa,QAAA,gBACzMnD,OAAA;sBAAKsC,SAAS,EAAC;oBAA4G;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAClI1C,OAAA;sBAAKsC,SAAS,EAAC,UAAU;sBAAAa,QAAA,gBACrBnD,OAAA;wBAAKsC,SAAS,EAAC,wCAAwC;wBAAAa,QAAA,gBACnDnD,OAAA;0BAAKsC,SAAS,EAAC,8BAA8B;0BAAAa,QAAA,eACzCnD,OAAA,CAACR,UAAU;4BAAC8C,SAAS,EAAC;0BAAyB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjD,CAAC,eACN1C,OAAA;0BAAKsC,SAAS,EAAG,kCACbqD,oBAAoB,IAAI,EAAE,GAAG,6BAA6B,GAC1DA,oBAAoB,IAAI,EAAE,GAAG,+BAA+B,GAC5D,yBACH,EAAE;0BAAAxC,QAAA,EACEwC,oBAAoB,IAAI,EAAE,GAAG,WAAW,GAAGA,oBAAoB,IAAI,EAAE,GAAG,MAAM,GAAG;wBAAmB;0BAAApD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpG,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACN1C,OAAA;wBAAKsC,SAAS,EAAC,yCAAyC;wBAAAa,QAAA,GAAEwC,oBAAoB,EAAC,GAAC;sBAAA;wBAAApD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACtF1C,OAAA;wBAAKsC,SAAS,EAAC,qCAAqC;wBAAAa,QAAA,EAAC;sBAAe;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eAEN1C,OAAA;oBAAKsC,SAAS,EAAC,4LAA4L;oBAAAa,QAAA,gBACvMnD,OAAA;sBAAKsC,SAAS,EAAC;oBAA0G;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAChI1C,OAAA;sBAAKsC,SAAS,EAAC,UAAU;sBAAAa,QAAA,gBACrBnD,OAAA;wBAAKsC,SAAS,EAAC,wCAAwC;wBAAAa,QAAA,gBACnDnD,OAAA;0BAAKsC,SAAS,EAAC,6BAA6B;0BAAAa,QAAA,eACxCnD,OAAA,CAACN,WAAW;4BAAC4C,SAAS,EAAC;0BAAwB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjD,CAAC,eACN1C,OAAA;0BAAKsC,SAAS,EAAC,4DAA4D;0BAAAa,QAAA,GACtEuC,YAAY,GAAG,CAAC,GAAGE,IAAI,CAACC,KAAK,CAAErB,YAAY,GAAGkB,YAAY,GAAI,GAAG,CAAC,GAAG,CAAC,EAAC,GAC5E;wBAAA;0BAAAnD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACN1C,OAAA;wBAAKsC,SAAS,EAAC,wCAAwC;wBAAAa,QAAA,EAAEqB;sBAAY;wBAAAjC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC5E1C,OAAA;wBAAKsC,SAAS,EAAC,oCAAoC;wBAAAa,QAAA,EAAC;sBAAY;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eAEN1C,OAAA;oBAAKsC,SAAS,EAAC,wLAAwL;oBAAAa,QAAA,gBACnMnD,OAAA;sBAAKsC,SAAS,EAAC;oBAAsG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC5H1C,OAAA;sBAAKsC,SAAS,EAAC,UAAU;sBAAAa,QAAA,gBACrBnD,OAAA;wBAAKsC,SAAS,EAAC,wCAAwC;wBAAAa,QAAA,gBACnDnD,OAAA;0BAAKsC,SAAS,EAAC,2BAA2B;0BAAAa,QAAA,eACtCnD,OAAA,CAACL,OAAO;4BAAC2C,SAAS,EAAC;0BAAsB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3C,CAAC,eACN1C,OAAA;0BAAKsC,SAAS,EAAC,wDAAwD;0BAAAa,QAAA,GAClEuC,YAAY,GAAG,CAAC,GAAGE,IAAI,CAACC,KAAK,CAAEpB,WAAW,GAAGiB,YAAY,GAAI,GAAG,CAAC,GAAG,CAAC,EAAC,GAC3E;wBAAA;0BAAAnD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACN1C,OAAA;wBAAKsC,SAAS,EAAC,sCAAsC;wBAAAa,QAAA,EAAEsB;sBAAW;wBAAAlC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACzE1C,OAAA;wBAAKsC,SAAS,EAAC,kCAAkC;wBAAAa,QAAA,EAAC;sBAAW;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eAEN1C,OAAA;oBAAKsC,SAAS,EAAC,0LAA0L;oBAAAa,QAAA,gBACrMnD,OAAA;sBAAKsC,SAAS,EAAC;oBAAwG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC9H1C,OAAA;sBAAKsC,SAAS,EAAC,UAAU;sBAAAa,QAAA,gBACrBnD,OAAA;wBAAKsC,SAAS,EAAC,wCAAwC;wBAAAa,QAAA,gBACnDnD,OAAA;0BAAKsC,SAAS,EAAC,4BAA4B;0BAAAa,QAAA,eACvCnD,OAAA,CAACd,QAAQ;4BAACoD,SAAS,EAAC;0BAAuB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC7C,CAAC,eACN1C,OAAA;0BAAKsC,SAAS,EAAC,0DAA0D;0BAAAa,QAAA,EAAC;wBAE1E;0BAAAZ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACN1C,OAAA;wBAAKsC,SAAS,EAAC,uCAAuC;wBAAAa,QAAA,EAAEuC;sBAAY;wBAAAnD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC3E1C,OAAA;wBAAKsC,SAAS,EAAC,mCAAmC;wBAAAa,QAAA,EAAC;sBAAa;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAEd,CAAC,EAAE,CAAC,eAEJ1C,OAAA;cAAKsC,SAAS,EAAC,mIAAmI;cAAAa,QAAA,gBAE9InD,OAAA;gBAAKsC,SAAS,EAAC,mEAAmE;gBAAAa,QAAA,gBAC9EnD,OAAA;kBAAKsC,SAAS,EAAC,yBAAyB;kBAAAa,QAAA,gBACpCnD,OAAA;oBAAKsC,SAAS,EAAC,yEAAyE;oBAAAa,QAAA,eACpFnD,OAAA,CAACd,QAAQ;sBAACoD,SAAS,EAAC;oBAAoB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC,eACN1C,OAAA;oBAAAmD,QAAA,gBACInD,OAAA;sBAAIsC,SAAS,EAAC,kCAAkC;sBAAAa,QAAA,EAAC;oBAAoB;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC1E1C,OAAA;sBAAGsC,SAAS,EAAC,4BAA4B;sBAAAa,QAAA,EACnC5C,cAAc,KAAK,KAAK,IAAIF,aAAa,KAAK,KAAK,IAAII,eAAe,KAAK,KAAK,GAC5E,gCAAgC,GAChC;oBAA0B;sBAAA8B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eAGN1C,OAAA;kBAAKsC,SAAS,EAAC,sCAAsC;kBAAAa,QAAA,gBACjDnD,OAAA;oBAAKsC,SAAS,EAAC,aAAa;oBAAAa,QAAA,gBACxBnD,OAAA;sBAAKsC,SAAS,EAAC,iCAAiC;sBAAAa,QAAA,EAAE,IAAIpC,IAAI,CAAC,CAAC,CAAC+E,kBAAkB,CAAC,OAAO,EAAE;wBAAEC,KAAK,EAAE;sBAAQ,CAAC;oBAAC;sBAAAxD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACnH1C,OAAA;sBAAKsC,SAAS,EAAC,uBAAuB;sBAAAa,QAAA,EAAC;oBAAa;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC,eACN1C,OAAA;oBAAKsC,SAAS,EAAC;kBAAsB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5C1C,OAAA;oBAAKsC,SAAS,EAAC,aAAa;oBAAAa,QAAA,gBACxBnD,OAAA;sBAAKsC,SAAS,EAAC,iCAAiC;sBAAAa,QAAA,EAAEtC,YAAY,CAACmF,OAAO,CAAC;oBAAC;sBAAAzD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC/E1C,OAAA;sBAAKsC,SAAS,EAAC,uBAAuB;sBAAAa,QAAA,EAAC;oBAAQ;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAGN1C,OAAA;gBAAKsC,SAAS,EAAC,qGAAqG;gBAAAa,QAAA,gBAChHnD,OAAA;kBAAKsC,SAAS,EAAC,wCAAwC;kBAAAa,QAAA,gBACnDnD,OAAA;oBAAIsC,SAAS,EAAC,0DAA0D;oBAAAa,QAAA,gBACpEnD,OAAA,CAACH,IAAI;sBAACyC,SAAS,EAAC;oBAAuB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,mBAE9C;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL1C,OAAA;oBAAKsC,SAAS,EAAC,+DAA+D;oBAAAa,QAAA,EAAC;kBAE/E;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACN1C,OAAA;kBAAKsC,SAAS,EAAC,uCAAuC;kBAAAa,QAAA,gBAClDnD,OAAA;oBAAKsC,SAAS,EAAC,qHAAqH;oBAAAa,QAAA,gBAChInD,OAAA;sBAAKsC,SAAS,EAAC;oBAAsG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC5H1C,OAAA;sBAAAmD,QAAA,gBACInD,OAAA;wBAAMsC,SAAS,EAAC,8BAA8B;wBAAAa,QAAA,EAAC;sBAAO;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC7D1C,OAAA;wBAAKsC,SAAS,EAAC,wBAAwB;wBAAAa,QAAA,EAAC;sBAAc;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACN1C,OAAA;oBAAKsC,SAAS,EAAC,mHAAmH;oBAAAa,QAAA,gBAC9HnD,OAAA;sBAAKsC,SAAS,EAAC;oBAAgG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACtH1C,OAAA;sBAAAmD,QAAA,gBACInD,OAAA;wBAAMsC,SAAS,EAAC,4BAA4B;wBAAAa,QAAA,EAAC;sBAAM;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC1D1C,OAAA;wBAAKsC,SAAS,EAAC,sBAAsB;wBAAAa,QAAA,EAAC;sBAAY;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACN1C,OAAA;oBAAKsC,SAAS,EAAC,oHAAoH;oBAAAa,QAAA,gBAC/HnD,OAAA;sBAAKsC,SAAS,EAAC;oBAAmG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACzH1C,OAAA;sBAAAmD,QAAA,gBACInD,OAAA;wBAAMsC,SAAS,EAAC,6BAA6B;wBAAAa,QAAA,EAAC;sBAAO;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC5D1C,OAAA;wBAAKsC,SAAS,EAAC,uBAAuB;wBAAAa,QAAA,EAAC;sBAAU;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACN1C,OAAA;oBAAKsC,SAAS,EAAC,sHAAsH;oBAAAa,QAAA,gBACjInD,OAAA;sBAAKsC,SAAS,EAAC;oBAAyG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC/H1C,OAAA;sBAAAmD,QAAA,gBACInD,OAAA;wBAAMsC,SAAS,EAAC,+BAA+B;wBAAAa,QAAA,EAAC;sBAAK;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC5D1C,OAAA;wBAAKsC,SAAS,EAAC,yBAAyB;wBAAAa,QAAA,EAAC;sBAAkB;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAGN1C,OAAA;gBAAKsC,SAAS,EAAC,6BAA6B;gBAAAa,QAAA,gBACxCnD,OAAA;kBAAKsC,SAAS,EAAC;gBAAwF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9G1C,OAAA;kBAAKsC,SAAS,EAAC,uEAAuE;kBAAAa,QAAA,eAClFnD,OAAA,CAACF,YAAY;oBACTgD,KAAK,EAAEjC,YAAa;oBACpBuE,QAAQ,EAAEJ,gBAAiB;oBAC3BiB,WAAW,EAAEvB,cAAe;oBAC5BwB,aAAa,EAAEnB,gBAAiB;oBAChCzC,SAAS,EAAC;kBAA0B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAGN1C,OAAA;kBAAKsC,SAAS,EAAC,mEAAmE;kBAAAa,QAAA,eAC9EnD,OAAA;oBAAKsC,SAAS,EAAC,yBAAyB;oBAAAa,QAAA,gBACpCnD,OAAA;sBAAKsC,SAAS,EAAC;oBAAkC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACxD1C,OAAA;sBAAAmD,QAAA,EAAM;oBAAsC;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,EAGL7B,YAAY,IAAI,CAAC,MAAM;gBACpB,MAAM+D,UAAU,GAAGb,oBAAoB,CAAClD,YAAY,CAAC;gBACrD,MAAMgE,KAAK,GAAG,IAAI9D,IAAI,CAAC,CAAC;gBACxB,MAAMoF,OAAO,GAAGtF,YAAY,CAACuF,YAAY,CAAC,CAAC,KAAKvB,KAAK,CAACuB,YAAY,CAAC,CAAC;gBACpE,MAAMtB,QAAQ,GAAGjE,YAAY,GAAGgE,KAAK;gBAErC,oBACI7E,OAAA;kBAAKsC,SAAS,EAAC,gBAAgB;kBAAAa,QAAA,gBAE3BnD,OAAA;oBAAKsC,SAAS,EAAC,qFAAqF;oBAAAa,QAAA,eAChGnD,OAAA;sBAAKsC,SAAS,EAAC,mCAAmC;sBAAAa,QAAA,gBAC9CnD,OAAA;wBAAAmD,QAAA,gBACInD,OAAA;0BAAIsC,SAAS,EAAC,yBAAyB;0BAAAa,QAAA,EAClCtC,YAAY,CAACiF,kBAAkB,CAAC,OAAO,EAAE;4BACtCO,OAAO,EAAE,MAAM;4BACfC,IAAI,EAAE,SAAS;4BACfP,KAAK,EAAE,MAAM;4BACbQ,GAAG,EAAE;0BACT,CAAC;wBAAC;0BAAAhE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACL1C,OAAA;0BAAKsC,SAAS,EAAC,yCAAyC;0BAAAa,QAAA,GACnDgD,OAAO,iBACJnG,OAAA;4BAAMsC,SAAS,EAAC,wDAAwD;4BAAAa,QAAA,EAAC;0BAEzE;4BAAAZ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CACT,EACAoC,QAAQ,iBACL9E,OAAA;4BAAMsC,SAAS,EAAC,wDAAwD;4BAAAa,QAAA,EAAC;0BAEzE;4BAAAZ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CACT,EACAkC,UAAU,iBACP5E,OAAA;4BAAMsC,SAAS,EAAG,8CACdsC,UAAU,CAAC7C,MAAM,KAAK,SAAS,GAAG,gCAAgC,GAClE6C,UAAU,CAAC7C,MAAM,KAAK,QAAQ,GAAG,4BAA4B,GAC7D6C,UAAU,CAAC7C,MAAM,KAAK,SAAS,GAAG,8BAA8B,GAChE,kCACH,EAAE;4BAAAoB,QAAA,EACEyB,UAAU,CAAC7C,MAAM,KAAK,SAAS,GAAG,aAAa,GAC/C6C,UAAU,CAAC7C,MAAM,KAAK,QAAQ,GAAG,YAAY,GAC7C6C,UAAU,CAAC7C,MAAM,KAAK,SAAS,GAAG,SAAS,GAC3C;0BAAkB;4BAAAQ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CACT;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACA,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACN1C,OAAA;wBAAKsC,SAAS,EAAC,YAAY;wBAAAa,QAAA,gBACvBnD,OAAA;0BAAKsC,SAAS,EAAC,oBAAoB;0BAAAa,QAAA,EAAEtC,YAAY,CAACmF,OAAO,CAAC;wBAAC;0BAAAzD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAClE1C,OAAA;0BAAKsC,SAAS,EAAC,yBAAyB;0BAAAa,QAAA,EACnCtC,YAAY,CAACiF,kBAAkB,CAAC,OAAO,EAAE;4BAAEC,KAAK,EAAE;0BAAQ,CAAC;wBAAC;0BAAAxD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5D,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,EAGLkC,UAAU,IAAIA,UAAU,CAACL,OAAO,CAACF,MAAM,GAAG,CAAC,gBACxCrE,OAAA;oBAAKsC,SAAS,EAAC,2DAA2D;oBAAAa,QAAA,gBACtEnD,OAAA;sBAAKsC,SAAS,EAAC,wCAAwC;sBAAAa,QAAA,gBACnDnD,OAAA;wBAAIsC,SAAS,EAAC,iCAAiC;wBAAAa,QAAA,EAAC;sBAAa;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAClE1C,OAAA;wBAAKsC,SAAS,EAAC,uBAAuB;wBAAAa,QAAA,GACjCyB,UAAU,CAACL,OAAO,CAACF,MAAM,EAAC,GAAC,EAACO,UAAU,CAACL,OAAO,CAACF,MAAM,KAAK,CAAC,GAAG,OAAO,GAAG,SAAS;sBAAA;wBAAA9B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACN1C,OAAA;sBAAKsC,SAAS,EAAC,WAAW;sBAAAa,QAAA,EACrByB,UAAU,CAACL,OAAO,CAACW,GAAG,CAAC,CAACxB,MAAM,EAAE8B,KAAK,kBAClCxF,OAAA;wBAAiBsC,SAAS,EAAG,kGACzBoB,MAAM,CAAC3B,MAAM,KAAK,SAAS,GAAG,wFAAwF,GACtH2B,MAAM,CAAC3B,MAAM,KAAK,QAAQ,GAAG,gFAAgF,GAC7G,oFACH,EAAE;wBAAAoB,QAAA,gBACCnD,OAAA;0BAAKsC,SAAS,EAAC,KAAK;0BAAAa,QAAA,eAChBnD,OAAA;4BAAKsC,SAAS,EAAC,mCAAmC;4BAAAa,QAAA,gBAC9CnD,OAAA;8BAAKsC,SAAS,EAAC,yBAAyB;8BAAAa,QAAA,gBACpCnD,OAAA;gCAAKsC,SAAS,EAAG,4BACboB,MAAM,CAAC3B,MAAM,KAAK,SAAS,GAAG,cAAc,GAC5C2B,MAAM,CAAC3B,MAAM,KAAK,QAAQ,GAAG,YAAY,GACzC,aACH,EAAE;gCAAAoB,QAAA,EACEd,aAAa,CAACqB,MAAM,CAAC3B,MAAM,CAAC6B,WAAW,CAAC,CAAC;8BAAC;gCAAArB,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC1C,CAAC,eACN1C,OAAA;gCAAAmD,QAAA,gBACInD,OAAA;kCAAIsC,SAAS,EAAC,iCAAiC;kCAAAa,QAAA,EAAEO,MAAM,CAAC5B;gCAAO;kCAAAS,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAK,CAAC,eACrE1C,OAAA;kCAAKsC,SAAS,EAAC,oDAAoD;kCAAAa,QAAA,gBAC/DnD,OAAA;oCAAMsC,SAAS,EAAC,yCAAyC;oCAAAa,QAAA,EACpDO,MAAM,CAACxB;kCAAM;oCAAAK,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACZ,CAAC,eACP1C,OAAA;oCAAMsC,SAAS,EAAC,yCAAyC;oCAAAa,QAAA,EACpDO,MAAM,CAACvB;kCAAK;oCAAAI,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACX,CAAC,EACNgB,MAAM,CAACtB,IAAI,KAAK,SAAS,iBACtBpC,OAAA;oCAAMsC,SAAS,EAAC,uEAAuE;oCAAAa,QAAA,EAAC;kCAExF;oCAAAZ,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAM,CACT;gCAAA;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACA,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACL,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACL,CAAC,eACN1C,OAAA;8BAAKsC,SAAS,EAAC,YAAY;8BAAAa,QAAA,eACvBnD,OAAA;gCAAMsC,SAAS,EAAG,gFACdoB,MAAM,CAAC3B,MAAM,KAAK,SAAS,GAAG,8CAA8C,GAC5E2B,MAAM,CAAC3B,MAAM,KAAK,QAAQ,GAAG,wCAAwC,GACrE,2CACH,EAAE;gCAAAoB,QAAA,EACEO,MAAM,CAAC3B;8BAAM;gCAAAQ,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACZ;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACN,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACL;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eAEN1C,OAAA;0BAAKsC,SAAS,EAAG,qCACboB,MAAM,CAAC3B,MAAM,KAAK,SAAS,GAAG,8CAA8C,GAC5E2B,MAAM,CAAC3B,MAAM,KAAK,QAAQ,GAAG,0CAA0C,GACvE,4CACH;wBAAE;0BAAAQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA,GAhDJ8C,KAAK;wBAAAjD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAiDV,CACR;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,GACNoC,QAAQ,gBACR9E,OAAA;oBAAKsC,SAAS,EAAC,+FAA+F;oBAAAa,QAAA,gBAC1GnD,OAAA;sBAAKsC,SAAS,EAAC,mHAAmH;sBAAAa,QAAA,eAC9HnD,OAAA,CAACd,QAAQ;wBAACoD,SAAS,EAAC;sBAAoB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1C,CAAC,eACN1C,OAAA;sBAAIsC,SAAS,EAAC,sCAAsC;sBAAAa,QAAA,EAAC;oBAAW;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACrE1C,OAAA;sBAAGsC,SAAS,EAAC,eAAe;sBAAAa,QAAA,EAAC;oBAA8C;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9E,CAAC,gBAEN1C,OAAA;oBAAKsC,SAAS,EAAC,8FAA8F;oBAAAa,QAAA,gBACzGnD,OAAA;sBAAKsC,SAAS,EAAC,kHAAkH;sBAAAa,QAAA,eAC7HnD,OAAA,CAACd,QAAQ;wBAACoD,SAAS,EAAC;sBAAoB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1C,CAAC,eACN1C,OAAA;sBAAIsC,SAAS,EAAC,sCAAsC;sBAAAa,QAAA,EAAC;oBAAU;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACpE1C,OAAA;sBAAGsC,SAAS,EAAC,eAAe;sBAAAa,QAAA,EAAC;oBAAwC;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxE,CACR;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAEd,CAAC,EAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAGN1C,OAAA;UAAKsC,SAAS,EAAC,2DAA2D;UAAAa,QAAA,gBACtEnD,OAAA;YAAIsC,SAAS,EAAC,8DAA8D;YAAAa,QAAA,gBACxEnD,OAAA,CAACP,KAAK;cAAC6C,SAAS,EAAC;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3C1C,OAAA;cAAAmD,QAAA,EAAM;YAAe;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACL1C,OAAA;YAAKsC,SAAS,EAAC,WAAW;YAAAa,QAAA,EACrBnC,cAAc,CAACY,cAAc,CAACsD,GAAG,CAAC,CAACsB,QAAQ,EAAEhB,KAAK,kBAC/CxF,OAAA;cAAiBsC,SAAS,EAAC,8FAA8F;cAAAa,QAAA,GACpHd,aAAa,CAACmE,QAAQ,CAACzE,MAAM,CAAC,eAC/B/B,OAAA;gBAAKsC,SAAS,EAAC,QAAQ;gBAAAa,QAAA,gBACnBnD,OAAA;kBAAKsC,SAAS,EAAC,6BAA6B;kBAAAa,QAAA,EAAEqD,QAAQ,CAAC1E;gBAAO;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrE1C,OAAA;kBAAKsC,SAAS,EAAC,uBAAuB;kBAAAa,QAAA,GAAEqD,QAAQ,CAAC3E,IAAI,EAAC,UAAG,EAAC2E,QAAQ,CAACxE,IAAI;gBAAA;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC,eACN1C,OAAA;gBAAKsC,SAAS,EAAG;AACrD,sBAAsBkE,QAAQ,CAACzE,MAAM,KAAK,SAAS,GAAG,8CAA8C,GACxDyE,QAAQ,CAACzE,MAAM,KAAK,QAAQ,GAAG,wCAAwC,GACnE,iDACP,EAAE;gBAAAoB,QAAA,EACFqD,QAAQ,CAACzE;cAAM;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA,GAZA8C,KAAK;cAAAjD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaV,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACxC,EAAA,CAr2BID,cAAc;AAAAwG,EAAA,GAAdxG,cAAc;AAu2BpB,eAAeA,cAAc;;AAE7B;AACA,MAAMyG,cAAc,GAAI;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;EACjC,MAAMC,aAAa,GAAGD,QAAQ,CAACE,cAAc,CAAC,iBAAiB,CAAC;EAChE,IAAI,CAACD,aAAa,EAAE;IAChB,MAAME,YAAY,GAAGH,QAAQ,CAACI,aAAa,CAAC,OAAO,CAAC;IACpDD,YAAY,CAAC7B,EAAE,GAAG,iBAAiB;IACnC6B,YAAY,CAACE,WAAW,GAAGN,cAAc;IACzCC,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACJ,YAAY,CAAC;EAC3C;AACJ;AAAC,IAAAL,EAAA;AAAAU,YAAA,CAAAV,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
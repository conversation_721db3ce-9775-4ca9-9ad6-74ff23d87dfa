{"ast": null, "code": "/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 12.01h.01\",\n  key: \"7rp0yl\"\n}], [\"path\", {\n  d: \"M18 8v4a8 8 0 0 1-1.07 4\",\n  key: \"1st48v\"\n}], [\"circle\", {\n  cx: \"10\",\n  cy: \"12\",\n  r: \"4\",\n  key: \"19levz\"\n}], [\"rect\", {\n  x: \"2\",\n  y: \"4\",\n  width: \"20\",\n  height: \"16\",\n  rx: \"2\",\n  key: \"izxlao\"\n}]];\nconst Turntable = createLucideIcon(\"turntable\", __iconNode);\nexport { __iconNode, Turntable as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "r", "x", "y", "width", "height", "rx", "Turntable", "createLucideIcon"], "sources": ["D:\\techvritti\\Collegemanagement\\frontend\\node_modules\\lucide-react\\src\\icons\\turntable.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M10 12.01h.01', key: '7rp0yl' }],\n  ['path', { d: 'M18 8v4a8 8 0 0 1-1.07 4', key: '1st48v' }],\n  ['circle', { cx: '10', cy: '12', r: '4', key: '19levz' }],\n  ['rect', { x: '2', y: '4', width: '20', height: '16', rx: '2', key: 'izxlao' }],\n];\n\n/**\n * @component @name Turntable\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMTIuMDFoLjAxIiAvPgogIDxwYXRoIGQ9Ik0xOCA4djRhOCA4IDAgMCAxLTEuMDcgNCIgLz4KICA8Y2lyY2xlIGN4PSIxMCIgY3k9IjEyIiByPSI0IiAvPgogIDxyZWN0IHg9IjIiIHk9IjQiIHdpZHRoPSIyMCIgaGVpZ2h0PSIxNiIgcng9IjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/turntable\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Turntable = createLucideIcon('turntable', __iconNode);\n\nexport default Turntable;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAiBC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAA4BC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,UAAU;EAAEC,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAMC,CAAA,EAAG;EAAKH,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,QAAQ;EAAEI,CAAA,EAAG;EAAKC,CAAA,EAAG;EAAKC,KAAA,EAAO;EAAMC,MAAA,EAAQ;EAAMC,EAAA,EAAI;EAAKR,GAAA,EAAK;AAAA,CAAU,EAChF;AAaA,MAAMS,SAAA,GAAYC,gBAAA,CAAiB,aAAaZ,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
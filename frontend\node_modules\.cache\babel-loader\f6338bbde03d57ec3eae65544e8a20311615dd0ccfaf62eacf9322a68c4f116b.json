{"ast": null, "code": "/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m3 8 4-4 4 4\",\n  key: \"11wl7u\"\n}], [\"path\", {\n  d: \"M7 4v16\",\n  key: \"1glfcx\"\n}], [\"path\", {\n  d: \"M17 10V4h-2\",\n  key: \"zcsr5x\"\n}], [\"path\", {\n  d: \"M15 10h4\",\n  key: \"id2lce\"\n}], [\"rect\", {\n  x: \"15\",\n  y: \"14\",\n  width: \"4\",\n  height: \"6\",\n  ry: \"2\",\n  key: \"33xykx\"\n}]];\nconst ArrowUp10 = createLucideIcon(\"arrow-up-1-0\", __iconNode);\nexport { __iconNode, ArrowUp10 as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "x", "y", "width", "height", "ry", "ArrowUp10", "createLucideIcon"], "sources": ["D:\\techvritti\\Collegemanagement\\frontend\\node_modules\\lucide-react\\src\\icons\\arrow-up-1-0.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm3 8 4-4 4 4', key: '11wl7u' }],\n  ['path', { d: 'M7 4v16', key: '1glfcx' }],\n  ['path', { d: 'M17 10V4h-2', key: 'zcsr5x' }],\n  ['path', { d: 'M15 10h4', key: 'id2lce' }],\n  ['rect', { x: '15', y: '14', width: '4', height: '6', ry: '2', key: '33xykx' }],\n];\n\n/**\n * @component @name ArrowUp10\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMyA4IDQtNCA0IDQiIC8+CiAgPHBhdGggZD0iTTcgNHYxNiIgLz4KICA8cGF0aCBkPSJNMTcgMTBWNGgtMiIgLz4KICA8cGF0aCBkPSJNMTUgMTBoNCIgLz4KICA8cmVjdCB4PSIxNSIgeT0iMTQiIHdpZHRoPSI0IiBoZWlnaHQ9IjYiIHJ5PSIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/arrow-up-1-0\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowUp10 = createLucideIcon('arrow-up-1-0', __iconNode);\n\nexport default ArrowUp10;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAgBC,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAWC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAeC,GAAA,EAAK;AAAA,CAAU,GAC5C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAYC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAMC,CAAA,EAAG;EAAMC,KAAA,EAAO;EAAKC,MAAA,EAAQ;EAAKC,EAAA,EAAI;EAAKL,GAAA,EAAK;AAAA,CAAU,EAChF;AAaA,MAAMM,SAAA,GAAYC,gBAAA,CAAiB,gBAAgBT,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
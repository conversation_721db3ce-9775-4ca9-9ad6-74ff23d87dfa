{"ast": null, "code": "/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 2v15\",\n  key: \"1qf71f\"\n}], [\"path\", {\n  d: \"M7 22a4 4 0 0 1-4-4 1 1 0 0 1 1-1h16a1 1 0 0 1 1 1 4 4 0 0 1-4 4z\",\n  key: \"1pxcvx\"\n}], [\"path\", {\n  d: \"M9.159 2.46a1 1 0 0 1 1.521-.193l9.977 8.98A1 1 0 0 1 20 13H4a1 1 0 0 1-.824-1.567z\",\n  key: \"5oog16\"\n}]];\nconst Sailboat = createLucideIcon(\"sailboat\", __iconNode);\nexport { __iconNode, Sailboat as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Sailboat", "createLucideIcon"], "sources": ["D:\\techvritti\\Collegemanagement\\frontend\\node_modules\\lucide-react\\src\\icons\\sailboat.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M10 2v15', key: '1qf71f' }],\n  [\n    'path',\n    { d: 'M7 22a4 4 0 0 1-4-4 1 1 0 0 1 1-1h16a1 1 0 0 1 1 1 4 4 0 0 1-4 4z', key: '1pxcvx' },\n  ],\n  [\n    'path',\n    {\n      d: 'M9.159 2.46a1 1 0 0 1 1.521-.193l9.977 8.98A1 1 0 0 1 20 13H4a1 1 0 0 1-.824-1.567z',\n      key: '5oog16',\n    },\n  ],\n];\n\n/**\n * @component @name Sailboat\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMnYxNSIgLz4KICA8cGF0aCBkPSJNNyAyMmE0IDQgMCAwIDEtNC00IDEgMSAwIDAgMSAxLTFoMTZhMSAxIDAgMCAxIDEgMSA0IDQgMCAwIDEtNCA0eiIgLz4KICA8cGF0aCBkPSJNOS4xNTkgMi40NmExIDEgMCAwIDEgMS41MjEtLjE5M2w5Ljk3NyA4Ljk4QTEgMSAwIDAgMSAyMCAxM0g0YTEgMSAwIDAgMS0uODI0LTEuNTY3eiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/sailboat\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Sailboat = createLucideIcon('sailboat', __iconNode);\n\nexport default Sailboat;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAYC,GAAA,EAAK;AAAA,CAAU,GACzC,CACE,QACA;EAAED,CAAA,EAAG;EAAqEC,GAAA,EAAK;AAAA,EACjF,EACA,CACE,QACA;EACED,CAAA,EAAG;EACHC,GAAA,EAAK;AAAA,EACP,CAEJ;AAaA,MAAMC,QAAA,GAAWC,gBAAA,CAAiB,YAAYJ,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
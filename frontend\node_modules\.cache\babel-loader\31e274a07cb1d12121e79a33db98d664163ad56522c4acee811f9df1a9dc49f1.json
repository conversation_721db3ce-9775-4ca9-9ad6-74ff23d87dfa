{"ast": null, "code": "/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z\",\n  key: \"e79jfc\"\n}], [\"circle\", {\n  cx: \"13.5\",\n  cy: \"6.5\",\n  r: \".5\",\n  fill: \"currentColor\",\n  key: \"1okk4w\"\n}], [\"circle\", {\n  cx: \"17.5\",\n  cy: \"10.5\",\n  r: \".5\",\n  fill: \"currentColor\",\n  key: \"f64h9f\"\n}], [\"circle\", {\n  cx: \"6.5\",\n  cy: \"12.5\",\n  r: \".5\",\n  fill: \"currentColor\",\n  key: \"qy21gx\"\n}], [\"circle\", {\n  cx: \"8.5\",\n  cy: \"7.5\",\n  r: \".5\",\n  fill: \"currentColor\",\n  key: \"fotxhn\"\n}]];\nconst Palette = createLucideIcon(\"palette\", __iconNode);\nexport { __iconNode, Palette as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "r", "fill", "Palette", "createLucideIcon"], "sources": ["D:\\techvritti\\Collegemanagement\\frontend\\node_modules\\lucide-react\\src\\icons\\palette.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z',\n      key: 'e79jfc',\n    },\n  ],\n  ['circle', { cx: '13.5', cy: '6.5', r: '.5', fill: 'currentColor', key: '1okk4w' }],\n  ['circle', { cx: '17.5', cy: '10.5', r: '.5', fill: 'currentColor', key: 'f64h9f' }],\n  ['circle', { cx: '6.5', cy: '12.5', r: '.5', fill: 'currentColor', key: 'qy21gx' }],\n  ['circle', { cx: '8.5', cy: '7.5', r: '.5', fill: 'currentColor', key: 'fotxhn' }],\n];\n\n/**\n * @component @name Palette\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMjJhMSAxIDAgMCAxIDAtMjAgMTAgOSAwIDAgMSAxMCA5IDUgNSAwIDAgMS01IDVoLTIuMjVhMS43NSAxLjc1IDAgMCAwLTEuNCAyLjhsLjMuNGExLjc1IDEuNzUgMCAwIDEtMS40IDIuOHoiIC8+CiAgPGNpcmNsZSBjeD0iMTMuNSIgY3k9IjYuNSIgcj0iLjUiIGZpbGw9ImN1cnJlbnRDb2xvciIgLz4KICA8Y2lyY2xlIGN4PSIxNy41IiBjeT0iMTAuNSIgcj0iLjUiIGZpbGw9ImN1cnJlbnRDb2xvciIgLz4KICA8Y2lyY2xlIGN4PSI2LjUiIGN5PSIxMi41IiByPSIuNSIgZmlsbD0iY3VycmVudENvbG9yIiAvPgogIDxjaXJjbGUgY3g9IjguNSIgY3k9IjcuNSIgcj0iLjUiIGZpbGw9ImN1cnJlbnRDb2xvciIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/palette\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Palette = createLucideIcon('palette', __iconNode);\n\nexport default Palette;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CACE,QACA;EACEC,CAAA,EAAG;EACHC,GAAA,EAAK;AAAA,EAET,EACA,CAAC,UAAU;EAAEC,EAAA,EAAI;EAAQC,EAAA,EAAI;EAAOC,CAAA,EAAG;EAAMC,IAAA,EAAM;EAAgBJ,GAAA,EAAK;AAAA,CAAU,GAClF,CAAC,UAAU;EAAEC,EAAA,EAAI;EAAQC,EAAA,EAAI;EAAQC,CAAA,EAAG;EAAMC,IAAA,EAAM;EAAgBJ,GAAA,EAAK;AAAA,CAAU,GACnF,CAAC,UAAU;EAAEC,EAAA,EAAI;EAAOC,EAAA,EAAI;EAAQC,CAAA,EAAG;EAAMC,IAAA,EAAM;EAAgBJ,GAAA,EAAK;AAAA,CAAU,GAClF,CAAC,UAAU;EAAEC,EAAA,EAAI;EAAOC,EAAA,EAAI;EAAOC,CAAA,EAAG;EAAMC,IAAA,EAAM;EAAgBJ,GAAA,EAAK;AAAA,CAAU,EACnF;AAaA,MAAMK,OAAA,GAAUC,gBAAA,CAAiB,WAAWR,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
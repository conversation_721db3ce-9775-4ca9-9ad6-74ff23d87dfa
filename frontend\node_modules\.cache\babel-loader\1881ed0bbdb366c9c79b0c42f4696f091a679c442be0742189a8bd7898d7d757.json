{"ast": null, "code": "/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M13.77 3.043a34 34 0 0 0-3.54 0\",\n  key: \"1oaobr\"\n}], [\"path\", {\n  d: \"M13.771 20.956a33 33 0 0 1-3.541.001\",\n  key: \"95iq0j\"\n}], [\"path\", {\n  d: \"M20.18 17.74c-.51 1.15-1.29 1.93-2.439 2.44\",\n  key: \"1u6qty\"\n}], [\"path\", {\n  d: \"M20.18 6.259c-.51-1.148-1.291-1.929-2.44-2.438\",\n  key: \"1ew6g6\"\n}], [\"path\", {\n  d: \"M20.957 10.23a33 33 0 0 1 0 3.54\",\n  key: \"1l9npr\"\n}], [\"path\", {\n  d: \"M3.043 10.23a34 34 0 0 0 .001 3.541\",\n  key: \"1it6jm\"\n}], [\"path\", {\n  d: \"M6.26 20.179c-1.15-.508-1.93-1.29-2.44-2.438\",\n  key: \"14uchd\"\n}], [\"path\", {\n  d: \"M6.26 3.82c-1.149.51-1.93 1.291-2.44 2.44\",\n  key: \"8k4agb\"\n}]];\nconst SquircleDashed = createLucideIcon(\"squircle-dashed\", __iconNode);\nexport { __iconNode, SquircleDashed as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "SquircleDashed", "createLucideIcon"], "sources": ["D:\\techvritti\\Collegemanagement\\frontend\\node_modules\\lucide-react\\src\\icons\\squircle-dashed.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M13.77 3.043a34 34 0 0 0-3.54 0', key: '1oaobr' }],\n  ['path', { d: 'M13.771 20.956a33 33 0 0 1-3.541.001', key: '95iq0j' }],\n  ['path', { d: 'M20.18 17.74c-.51 1.15-1.29 1.93-2.439 2.44', key: '1u6qty' }],\n  ['path', { d: 'M20.18 6.259c-.51-1.148-1.291-1.929-2.44-2.438', key: '1ew6g6' }],\n  ['path', { d: 'M20.957 10.23a33 33 0 0 1 0 3.54', key: '1l9npr' }],\n  ['path', { d: 'M3.043 10.23a34 34 0 0 0 .001 3.541', key: '1it6jm' }],\n  ['path', { d: 'M6.26 20.179c-1.15-.508-1.93-1.29-2.44-2.438', key: '14uchd' }],\n  ['path', { d: 'M6.26 3.82c-1.149.51-1.93 1.291-2.44 2.44', key: '8k4agb' }],\n];\n\n/**\n * @component @name SquircleDashed\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTMuNzcgMy4wNDNhMzQgMzQgMCAwIDAtMy41NCAwIiAvPgogIDxwYXRoIGQ9Ik0xMy43NzEgMjAuOTU2YTMzIDMzIDAgMCAxLTMuNTQxLjAwMSIgLz4KICA8cGF0aCBkPSJNMjAuMTggMTcuNzRjLS41MSAxLjE1LTEuMjkgMS45My0yLjQzOSAyLjQ0IiAvPgogIDxwYXRoIGQ9Ik0yMC4xOCA2LjI1OWMtLjUxLTEuMTQ4LTEuMjkxLTEuOTI5LTIuNDQtMi40MzgiIC8+CiAgPHBhdGggZD0iTTIwLjk1NyAxMC4yM2EzMyAzMyAwIDAgMSAwIDMuNTQiIC8+CiAgPHBhdGggZD0iTTMuMDQzIDEwLjIzYTM0IDM0IDAgMCAwIC4wMDEgMy41NDEiIC8+CiAgPHBhdGggZD0iTTYuMjYgMjAuMTc5Yy0xLjE1LS41MDgtMS45My0xLjI5LTIuNDQtMi40MzgiIC8+CiAgPHBhdGggZD0iTTYuMjYgMy44MmMtMS4xNDkuNTEtMS45MyAxLjI5MS0yLjQ0IDIuNDQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/squircle-dashed\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SquircleDashed = createLucideIcon('squircle-dashed', __iconNode);\n\nexport default SquircleDashed;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAmCC,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAwCC,GAAA,EAAK;AAAA,CAAU,GACrE,CAAC,QAAQ;EAAED,CAAA,EAAG;EAA+CC,GAAA,EAAK;AAAA,CAAU,GAC5E,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAkDC,GAAA,EAAK;AAAA,CAAU,GAC/E,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAoCC,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAuCC,GAAA,EAAK;AAAA,CAAU,GACpE,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAgDC,GAAA,EAAK;AAAA,CAAU,GAC7E,CAAC,QAAQ;EAAED,CAAA,EAAG;EAA6CC,GAAA,EAAK;AAAA,CAAU,EAC5E;AAaA,MAAMC,cAAA,GAAiBC,gBAAA,CAAiB,mBAAmBJ,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
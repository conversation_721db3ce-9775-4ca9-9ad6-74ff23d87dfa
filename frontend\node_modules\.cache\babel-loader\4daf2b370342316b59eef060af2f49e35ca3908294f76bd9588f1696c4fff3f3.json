{"ast": null, "code": "var _jsxFileName = \"D:\\\\techvritti\\\\Collegemanagement\\\\frontend\\\\src\\\\Screens\\\\Student\\\\AttendancePage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Calendar, Users, BookOpen, GraduationCap, Filter, Download, TrendingUp, Clock, CheckCircle, XCircle, AlertCircle, Info } from 'lucide-react';\nimport 'react-calendar/dist/Calendar.css';\nimport CalendarView from 'react-calendar';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AttendancePage = () => {\n  _s();\n  const [activeView, setActiveView] = useState('overall');\n  const [selectedBatch, setSelectedBatch] = useState('all');\n  const [selectedCourse, setSelectedCourse] = useState('all');\n  const [selectedSubject, setSelectedSubject] = useState('all');\n  const [dateRange, setDateRange] = useState('thisMonth');\n  const [selectedDate, setSelectedDate] = useState(new Date());\n  const [attendanceData] = useState({\n    overall: {\n      present: 85,\n      absent: 15,\n      total: 100\n    },\n    subjects: [{\n      name: 'Mathematics',\n      present: 18,\n      total: 20,\n      percentage: 90\n    }, {\n      name: 'Physics',\n      present: 16,\n      total: 18,\n      percentage: 89\n    }, {\n      name: 'Chemistry',\n      present: 14,\n      total: 16,\n      percentage: 88\n    }, {\n      name: 'Computer Science',\n      present: 19,\n      total: 22,\n      percentage: 86\n    }, {\n      name: 'English',\n      present: 15,\n      total: 18,\n      percentage: 83\n    }],\n    batches: [{\n      name: 'Batch A',\n      students: 45,\n      avgAttendance: 87\n    }, {\n      name: 'Batch B',\n      students: 42,\n      avgAttendance: 85\n    }, {\n      name: 'Batch C',\n      students: 48,\n      avgAttendance: 89\n    }],\n    courses: [{\n      name: 'Engineering',\n      students: 135,\n      avgAttendance: 87\n    }, {\n      name: 'Science',\n      students: 98,\n      avgAttendance: 85\n    }, {\n      name: 'Commerce',\n      students: 76,\n      avgAttendance: 91\n    }],\n    recentActivity: [{\n      date: '2024-03-15',\n      subject: 'Mathematics',\n      status: 'present',\n      time: '09:00 AM'\n    }, {\n      date: '2024-03-15',\n      subject: 'Physics',\n      status: 'present',\n      time: '10:30 AM'\n    }, {\n      date: '2024-03-14',\n      subject: 'Chemistry',\n      status: 'absent',\n      time: '02:00 PM'\n    }, {\n      date: '2024-03-14',\n      subject: 'Computer Science',\n      status: 'present',\n      time: '03:30 PM'\n    }, {\n      date: '2024-03-13',\n      subject: 'English',\n      status: 'present',\n      time: '11:00 AM'\n    }],\n    // Enhanced daily attendance records for calendar view\n    dailyRecords: [\n    // January 2024 - Engineering Course\n    {\n      date: '2024-01-15',\n      subject: 'Mathematics',\n      status: 'Present',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-15',\n      subject: 'Physics',\n      status: 'Present',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-16',\n      subject: 'Chemistry',\n      status: 'Absent',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-16',\n      subject: 'Mathematics',\n      status: 'Present',\n      course: 'Engineering',\n      batch: 'Batch B',\n      type: 'class'\n    }, {\n      date: '2024-01-17',\n      subject: 'English',\n      status: 'Present',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-18',\n      subject: 'Computer Science',\n      status: 'Absent',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-19',\n      subject: 'Physics',\n      status: 'Present',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-20',\n      subject: 'Holiday',\n      status: 'Holiday',\n      course: 'all',\n      batch: 'all',\n      type: 'holiday'\n    }, {\n      date: '2024-01-21',\n      subject: 'Holiday',\n      status: 'Holiday',\n      course: 'all',\n      batch: 'all',\n      type: 'holiday'\n    }, {\n      date: '2024-01-22',\n      subject: 'Mathematics',\n      status: 'Present',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-23',\n      subject: 'Chemistry',\n      status: 'Present',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-24',\n      subject: 'English',\n      status: 'Absent',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-25',\n      subject: 'Computer Science',\n      status: 'Present',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-26',\n      subject: 'Physics',\n      status: 'Present',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-29',\n      subject: 'Mathematics',\n      status: 'Present',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-30',\n      subject: 'Chemistry',\n      status: 'Absent',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-31',\n      subject: 'English',\n      status: 'Present',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    },\n    // Science course records\n    {\n      date: '2024-01-15',\n      subject: 'Biology',\n      status: 'Present',\n      course: 'Science',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-16',\n      subject: 'Chemistry',\n      status: 'Present',\n      course: 'Science',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-17',\n      subject: 'Physics',\n      status: 'Absent',\n      course: 'Science',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-18',\n      subject: 'Mathematics',\n      status: 'Present',\n      course: 'Science',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-19',\n      subject: 'Biology',\n      status: 'Present',\n      course: 'Science',\n      batch: 'Batch A',\n      type: 'class'\n    },\n    // Commerce course records\n    {\n      date: '2024-01-15',\n      subject: 'Accounting',\n      status: 'Present',\n      course: 'Commerce',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-16',\n      subject: 'Economics',\n      status: 'Present',\n      course: 'Commerce',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-17',\n      subject: 'Business Studies',\n      status: 'Present',\n      course: 'Commerce',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-18',\n      subject: 'Accounting',\n      status: 'Absent',\n      course: 'Commerce',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-19',\n      subject: 'Economics',\n      status: 'Present',\n      course: 'Commerce',\n      batch: 'Batch A',\n      type: 'class'\n    }]\n  });\n  const getStatusIcon = status => {\n    switch (status) {\n      case 'present':\n        return /*#__PURE__*/_jsxDEV(CheckCircle, {\n          className: \"w-4 h-4 text-green-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 36\n        }, this);\n      case 'absent':\n        return /*#__PURE__*/_jsxDEV(XCircle, {\n          className: \"w-4 h-4 text-red-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 35\n        }, this);\n      case 'late':\n        return /*#__PURE__*/_jsxDEV(AlertCircle, {\n          className: \"w-4 h-4 text-yellow-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 33\n        }, this);\n      default:\n        return null;\n    }\n  };\n  const getAttendanceColor = percentage => {\n    if (percentage >= 90) return 'text-green-600 bg-green-50 border-green-200';\n    if (percentage >= 75) return 'text-yellow-600 bg-yellow-50 border-yellow-200';\n    return 'text-red-600 bg-red-50 border-red-200';\n  };\n  const StatCard = ({\n    title,\n    value,\n    subtitle,\n    icon: Icon,\n    trend\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-gradient-to-br from-white via-blue-50 to-blue-100 rounded-xl p-6 shadow-md border border-blue-100 hover:shadow-lg transition-shadow\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 bg-blue-600 rounded-lg\",\n          children: /*#__PURE__*/_jsxDEV(Icon, {\n            className: \"w-5 h-5 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-blue-700 font-semibold\",\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 17\n      }, this), trend && /*#__PURE__*/_jsxDEV(TrendingUp, {\n        className: \"w-4 h-4 text-green-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 27\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-1\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-3xl font-extrabold text-blue-900\",\n        children: value\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-blue-600\",\n        children: subtitle\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 95,\n    columnNumber: 9\n  }, this);\n  const ProgressBar = ({\n    percentage,\n    label,\n    total,\n    present\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-2\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-sm font-semibold text-gray-700\",\n        children: label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: `text-xs px-2 py-1 rounded-full border ${getAttendanceColor(percentage)}`,\n        children: [percentage, \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-full bg-gray-200 rounded-full h-2\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `h-2 rounded-full transition-all duration-300 ${percentage >= 90 ? 'bg-green-500' : percentage >= 75 ? 'bg-yellow-500' : 'bg-red-500'}`,\n        style: {\n          width: `${percentage}%`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between text-xs text-gray-500\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: [\"Present: \", present]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: [\"Total: \", total]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 113,\n    columnNumber: 9\n  }, this);\n\n  // Auto-switch to calendar view when filters are selected\n  useEffect(() => {\n    if (selectedCourse !== 'all' || selectedBatch !== 'all' || selectedSubject !== 'all') {\n      setActiveView('calendar');\n    }\n  }, [selectedCourse, selectedBatch, selectedSubject]);\n\n  // Filter attendance data based on selected filters\n  const getFilteredAttendanceData = () => {\n    return attendanceData.dailyRecords.filter(record => {\n      const courseMatch = selectedCourse === 'all' || record.course.toLowerCase() === selectedCourse.toLowerCase();\n      const batchMatch = selectedBatch === 'all' || record.batch.toLowerCase() === selectedBatch.toLowerCase();\n      const subjectMatch = selectedSubject === 'all' || record.subject.toLowerCase() === selectedSubject.toLowerCase();\n      return courseMatch && batchMatch && subjectMatch;\n    });\n  };\n\n  // Get attendance status for a specific date\n  const getAttendanceForDate = date => {\n    const dateStr = date.toISOString().split('T')[0];\n    const filteredData = getFilteredAttendanceData();\n    const dayRecords = filteredData.filter(record => record.date === dateStr);\n    if (dayRecords.length === 0) return null;\n\n    // Check if it's a holiday\n    if (dayRecords.some(record => record.type === 'holiday')) {\n      return {\n        status: 'holiday',\n        records: dayRecords\n      };\n    }\n\n    // Calculate overall status for the day\n    const presentCount = dayRecords.filter(record => record.status === 'Present').length;\n    const absentCount = dayRecords.filter(record => record.status === 'Absent').length;\n    if (presentCount > 0 && absentCount === 0) return {\n      status: 'present',\n      records: dayRecords\n    };\n    if (absentCount > 0 && presentCount === 0) return {\n      status: 'absent',\n      records: dayRecords\n    };\n    if (presentCount > 0 && absentCount > 0) return {\n      status: 'mixed',\n      records: dayRecords\n    };\n    return {\n      status: 'unknown',\n      records: dayRecords\n    };\n  };\n\n  // Custom tile content for calendar (small dots)\n  const getTileContent = ({\n    date,\n    view\n  }) => {\n    if (view !== 'month') return null;\n    const attendance = getAttendanceForDate(date);\n    if (!attendance) return null;\n    const today = new Date();\n    const isFuture = date > today;\n    if (isFuture) return null;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center mt-1\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `w-2 h-2 rounded-full ${attendance.status === 'present' ? 'bg-green-500' : attendance.status === 'absent' ? 'bg-red-500' : attendance.status === 'holiday' ? 'bg-gray-400' : attendance.status === 'mixed' ? 'bg-yellow-500' : 'bg-blue-400'}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 13\n    }, this);\n  };\n\n  // Custom tile class name for calendar styling\n  const getTileClassName = ({\n    date,\n    view\n  }) => {\n    if (view !== 'month') return null;\n    const attendance = getAttendanceForDate(date);\n    const today = new Date();\n    const isFuture = date > today;\n    if (isFuture) {\n      return 'future-date';\n    }\n    if (!attendance) return 'no-class';\n    switch (attendance.status) {\n      case 'present':\n        return 'present-day';\n      case 'absent':\n        return 'absent-day';\n      case 'holiday':\n        return 'holiday-day';\n      case 'mixed':\n        return 'mixed-day';\n      default:\n        return 'no-class';\n    }\n  };\n  const handleDateChange = date => {\n    setSelectedDate(date);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-tr from-blue-50 via-white to-pink-50 p-6\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto space-y-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl p-8 shadow-lg border border-blue-100\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-4xl font-extrabold text-blue-800 leading-tight\",\n              children: \"Attendance Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-blue-600 mt-2 text-lg\",\n              children: \"Monitor attendance by batch, course, and subject.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"flex items-center gap-2 px-5 py-2 bg-blue-700 text-white rounded-xl shadow hover:bg-blue-800 transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(Download, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Export\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"flex items-center gap-2 px-5 py-2 border border-blue-300 text-blue-700 rounded-xl hover:bg-blue-50 transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(Filter, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Filter\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl p-2 shadow-sm border border-blue-100\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap gap-2 justify-center\",\n          children: [{\n            id: 'overall',\n            label: 'Overall',\n            icon: TrendingUp\n          }, {\n            id: 'subjects',\n            label: 'By Subject',\n            icon: BookOpen\n          }, {\n            id: 'batches',\n            label: 'By Batch',\n            icon: Users\n          }, {\n            id: 'courses',\n            label: 'By Course',\n            icon: GraduationCap\n          }, {\n            id: 'calendar',\n            label: 'Calendar View',\n            icon: Calendar\n          }].map(({\n            id,\n            label,\n            icon: Icon\n          }) => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setActiveView(id),\n            className: `flex items-center gap-2 px-5 py-2 rounded-xl font-medium text-base transition-all\n                  ${activeView === id ? 'bg-blue-700 text-white shadow' : 'text-blue-700 hover:bg-blue-100'}`,\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 33\n            }, this)]\n          }, id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 29\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl p-6 shadow-md border border-blue-100\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-semibold text-blue-700 mb-2\",\n              children: \"Date Range\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: dateRange,\n              onChange: e => setDateRange(e.target.value),\n              className: \"w-full px-3 py-2 border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"today\",\n                children: \"Today\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"thisWeek\",\n                children: \"This Week\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"thisMonth\",\n                children: \"This Month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"lastMonth\",\n                children: \"Last Month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"thisYear\",\n                children: \"This Year\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-semibold text-blue-700 mb-2\",\n              children: \"Batch\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedBatch,\n              onChange: e => {\n                setSelectedBatch(e.target.value);\n                // Add smooth transition to calendar view\n                if (e.target.value !== 'all') {\n                  setTimeout(() => setActiveView('calendar'), 300);\n                }\n              },\n              className: \"w-full px-3 py-2 border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: \"All Batches\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Batch A\",\n                children: \"Batch A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Batch B\",\n                children: \"Batch B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Batch C\",\n                children: \"Batch C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-semibold text-blue-700 mb-2\",\n              children: \"Course\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedCourse,\n              onChange: e => {\n                setSelectedCourse(e.target.value);\n                // Add smooth transition to calendar view\n                if (e.target.value !== 'all') {\n                  setTimeout(() => setActiveView('calendar'), 300);\n                }\n              },\n              className: \"w-full px-3 py-2 border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: \"All Courses\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Engineering\",\n                children: \"Engineering\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Science\",\n                children: \"Science\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Commerce\",\n                children: \"Commerce\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-semibold text-blue-700 mb-2\",\n              children: \"Subject\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedSubject,\n              onChange: e => {\n                setSelectedSubject(e.target.value);\n                // Add smooth transition to calendar view\n                if (e.target.value !== 'all') {\n                  setTimeout(() => setActiveView('calendar'), 300);\n                }\n              },\n              className: \"w-full px-3 py-2 border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: \"All Subjects\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Mathematics\",\n                children: \"Mathematics\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Physics\",\n                children: \"Physics\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Chemistry\",\n                children: \"Chemistry\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Computer Science\",\n                children: \"Computer Science\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"English\",\n                children: \"English\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Biology\",\n                children: \"Biology\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Accounting\",\n                children: \"Accounting\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Economics\",\n                children: \"Economics\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Business Studies\",\n                children: \"Business Studies\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Overall Attendance\",\n          value: \"85%\",\n          subtitle: \"This month average\",\n          icon: TrendingUp,\n          trend: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Total Classes\",\n          value: \"124\",\n          subtitle: \"Classes conducted\",\n          icon: Calendar\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Present Days\",\n          value: \"105\",\n          subtitle: \"Out of 124 classes\",\n          icon: CheckCircle\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 xl:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"xl:col-span-2 space-y-8\",\n          children: [activeView === 'overall' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-2xl p-8 shadow-md border border-blue-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-blue-900 mb-6\",\n              children: \"Overall Attendance Summary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center p-8 bg-gradient-to-tr from-blue-50 via-blue-100 to-white rounded-xl shadow\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-5xl font-extrabold text-blue-700 mb-2 drop-shadow\",\n                  children: \"85%\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 396,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-blue-800 font-semibold\",\n                  children: \"Overall Attendance\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-5\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-xl shadow-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                      className: \"w-8 h-8 text-green-600\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 402,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"font-semibold text-green-900\",\n                        children: \"Present\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 404,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-green-700\",\n                        children: \"Days attended\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 405,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 403,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 401,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-extrabold text-green-600\",\n                    children: \"105\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 408,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between p-4 bg-red-50 border border-red-200 rounded-xl shadow-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [/*#__PURE__*/_jsxDEV(XCircle, {\n                      className: \"w-8 h-8 text-red-600\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 412,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"font-semibold text-red-900\",\n                        children: \"Absent\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 414,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-red-700\",\n                        children: \"Days missed\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 415,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 413,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 411,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-extrabold text-red-600\",\n                    children: \"19\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 418,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 29\n          }, this), activeView === 'subjects' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-2xl p-8 shadow-md border border-blue-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-blue-900 mb-6\",\n              children: \"Subject-wise Attendance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-6\",\n              children: attendanceData.subjects.map((subject, index) => /*#__PURE__*/_jsxDEV(ProgressBar, {\n                percentage: subject.percentage,\n                label: subject.name,\n                total: subject.total,\n                present: subject.present\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 41\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 29\n          }, this), activeView === 'batches' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-2xl p-8 shadow-md border border-blue-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-blue-900 mb-6\",\n              children: \"Batch-wise Attendance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n              children: attendanceData.batches.map((batch, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6 bg-blue-50 rounded-xl border border-blue-200 shadow hover:scale-105 transition-transform\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-semibold text-blue-900\",\n                    children: batch.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 449,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(Users, {\n                    className: \"w-5 h-5 text-blue-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-bold text-blue-900\",\n                    children: [batch.avgAttendance, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 453,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-blue-700\",\n                    children: [batch.students, \" students\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 454,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full bg-blue-200 rounded-full h-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"h-2 bg-blue-600 rounded-full transition-all duration-300\",\n                      style: {\n                        width: `${batch.avgAttendance}%`\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 456,\n                      columnNumber: 53\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 455,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 452,\n                  columnNumber: 45\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 41\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 29\n          }, this), activeView === 'courses' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-2xl p-8 shadow-md border border-blue-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-blue-900 mb-6\",\n              children: \"Course-wise Attendance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-6\",\n              children: attendanceData.courses.map((course, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between p-5 border border-blue-200 rounded-xl bg-blue-50 hover:bg-blue-100 transition-colors shadow\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-blue-700 rounded-lg\",\n                    children: /*#__PURE__*/_jsxDEV(GraduationCap, {\n                      className: \"w-6 h-6 text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 476,\n                      columnNumber: 53\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 475,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-semibold text-blue-900\",\n                      children: course.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 479,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-blue-700\",\n                      children: [course.students, \" students enrolled\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 480,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 478,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 474,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `text-lg font-bold px-4 py-1 rounded-full border ${getAttendanceColor(course.avgAttendance)}`,\n                    children: [course.avgAttendance, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 484,\n                    columnNumber: 49\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 483,\n                  columnNumber: 45\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 473,\n                columnNumber: 41\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 29\n          }, this), activeView === 'calendar' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-2xl p-8 shadow-md border border-blue-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-blue-900 mb-6\",\n              children: \"Calendar View\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(CalendarView, {\n              value: selectedDate,\n              onChange: handleDateChange,\n              className: \"bg-white rounded-xl shadow\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 495,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl p-8 shadow-md border border-blue-100\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-bold text-blue-900 mb-6 flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(Clock, {\n              className: \"w-5 h-5 text-blue-700\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Recent Activity\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-5\",\n            children: attendanceData.recentActivity.map((activity, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-3 p-4 bg-blue-50 rounded-xl border hover:bg-blue-100 transition-colors\",\n              children: [getStatusIcon(activity.status), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold text-blue-900\",\n                  children: activity.subject\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 517,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-blue-700\",\n                  children: [activity.date, \" \\u2022 \", activity.time]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 518,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `text-xs px-3 py-1 rounded-full capitalize border\n                    ${activity.status === 'present' ? 'bg-green-100 text-green-800 border-green-300' : activity.status === 'absent' ? 'bg-red-100 text-red-800 border-red-300' : 'bg-yellow-100 text-yellow-800 border-yellow-300'}`,\n                children: activity.status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 37\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 233,\n    columnNumber: 9\n  }, this);\n};\n_s(AttendancePage, \"U/CV1TzuG0fvLkcqq9qrhG4KLhg=\");\n_c = AttendancePage;\nexport default AttendancePage;\nvar _c;\n$RefreshReg$(_c, \"AttendancePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Calendar", "Users", "BookOpen", "GraduationCap", "Filter", "Download", "TrendingUp", "Clock", "CheckCircle", "XCircle", "AlertCircle", "Info", "CalendarView", "jsxDEV", "_jsxDEV", "AttendancePage", "_s", "activeView", "setActiveView", "selected<PERSON><PERSON>", "setSelectedBatch", "selectedCourse", "setSelectedCourse", "selectedSubject", "setSelectedSubject", "date<PERSON><PERSON><PERSON>", "setDateRange", "selectedDate", "setSelectedDate", "Date", "attendanceData", "overall", "present", "absent", "total", "subjects", "name", "percentage", "batches", "students", "avgAttendance", "courses", "recentActivity", "date", "subject", "status", "time", "dailyRecords", "course", "batch", "type", "getStatusIcon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getAttendanceColor", "StatCard", "title", "value", "subtitle", "icon", "Icon", "trend", "children", "ProgressBar", "label", "style", "width", "getFilteredAttendanceData", "filter", "record", "courseMatch", "toLowerCase", "batchMatch", "subjectMatch", "getAttendanceForDate", "dateStr", "toISOString", "split", "filteredData", "dayRecords", "length", "some", "records", "presentCount", "absentCount", "getTileContent", "view", "attendance", "today", "isFuture", "getTileClassName", "handleDateChange", "id", "map", "onClick", "onChange", "e", "target", "setTimeout", "index", "activity", "_c", "$RefreshReg$"], "sources": ["D:/techvritti/Collegemanagement/frontend/src/Screens/Student/AttendancePage.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport {\r\n    Calendar, Users, BookOpen, GraduationCap, Filter, Download,\r\n    TrendingUp, Clock, CheckCircle, XCircle, AlertCircle, Info\r\n} from 'lucide-react';\r\nimport 'react-calendar/dist/Calendar.css';\r\nimport CalendarView from 'react-calendar';\r\n\r\nconst AttendancePage = () => {\r\n    const [activeView, setActiveView] = useState('overall');\r\n    const [selectedBatch, setSelectedBatch] = useState('all');\r\n    const [selectedCourse, setSelectedCourse] = useState('all');\r\n    const [selectedSubject, setSelectedSubject] = useState('all');\r\n    const [dateRange, setDateRange] = useState('thisMonth');\r\n    const [selectedDate, setSelectedDate] = useState(new Date());\r\n    const [attendanceData] = useState({\r\n        overall: { present: 85, absent: 15, total: 100 },\r\n        subjects: [\r\n            { name: 'Mathematics', present: 18, total: 20, percentage: 90 },\r\n            { name: 'Physics', present: 16, total: 18, percentage: 89 },\r\n            { name: 'Chemistry', present: 14, total: 16, percentage: 88 },\r\n            { name: 'Computer Science', present: 19, total: 22, percentage: 86 },\r\n            { name: 'English', present: 15, total: 18, percentage: 83 }\r\n        ],\r\n        batches: [\r\n            { name: 'Batch A', students: 45, avgAttendance: 87 },\r\n            { name: 'Batch B', students: 42, avgAttendance: 85 },\r\n            { name: 'Batch C', students: 48, avgAttendance: 89 }\r\n        ],\r\n        courses: [\r\n            { name: 'Engineering', students: 135, avgAttendance: 87 },\r\n            { name: 'Science', students: 98, avgAttendance: 85 },\r\n            { name: 'Commerce', students: 76, avgAttendance: 91 }\r\n        ],\r\n        recentActivity: [\r\n            { date: '2024-03-15', subject: 'Mathematics', status: 'present', time: '09:00 AM' },\r\n            { date: '2024-03-15', subject: 'Physics', status: 'present', time: '10:30 AM' },\r\n            { date: '2024-03-14', subject: 'Chemistry', status: 'absent', time: '02:00 PM' },\r\n            { date: '2024-03-14', subject: 'Computer Science', status: 'present', time: '03:30 PM' },\r\n            { date: '2024-03-13', subject: 'English', status: 'present', time: '11:00 AM' }\r\n        ],\r\n        // Enhanced daily attendance records for calendar view\r\n        dailyRecords: [\r\n            // January 2024 - Engineering Course\r\n            { date: '2024-01-15', subject: 'Mathematics', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-15', subject: 'Physics', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-16', subject: 'Chemistry', status: 'Absent', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-16', subject: 'Mathematics', status: 'Present', course: 'Engineering', batch: 'Batch B', type: 'class' },\r\n            { date: '2024-01-17', subject: 'English', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-18', subject: 'Computer Science', status: 'Absent', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-19', subject: 'Physics', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-20', subject: 'Holiday', status: 'Holiday', course: 'all', batch: 'all', type: 'holiday' },\r\n            { date: '2024-01-21', subject: 'Holiday', status: 'Holiday', course: 'all', batch: 'all', type: 'holiday' },\r\n            { date: '2024-01-22', subject: 'Mathematics', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-23', subject: 'Chemistry', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-24', subject: 'English', status: 'Absent', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-25', subject: 'Computer Science', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-26', subject: 'Physics', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-29', subject: 'Mathematics', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-30', subject: 'Chemistry', status: 'Absent', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-31', subject: 'English', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n\r\n            // Science course records\r\n            { date: '2024-01-15', subject: 'Biology', status: 'Present', course: 'Science', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-16', subject: 'Chemistry', status: 'Present', course: 'Science', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-17', subject: 'Physics', status: 'Absent', course: 'Science', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-18', subject: 'Mathematics', status: 'Present', course: 'Science', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-19', subject: 'Biology', status: 'Present', course: 'Science', batch: 'Batch A', type: 'class' },\r\n\r\n            // Commerce course records\r\n            { date: '2024-01-15', subject: 'Accounting', status: 'Present', course: 'Commerce', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-16', subject: 'Economics', status: 'Present', course: 'Commerce', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-17', subject: 'Business Studies', status: 'Present', course: 'Commerce', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-18', subject: 'Accounting', status: 'Absent', course: 'Commerce', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-19', subject: 'Economics', status: 'Present', course: 'Commerce', batch: 'Batch A', type: 'class' },\r\n        ]\r\n    });\r\n\r\n    const getStatusIcon = (status) => {\r\n        switch (status) {\r\n            case 'present': return <CheckCircle className=\"w-4 h-4 text-green-500\" />;\r\n            case 'absent': return <XCircle className=\"w-4 h-4 text-red-500\" />;\r\n            case 'late': return <AlertCircle className=\"w-4 h-4 text-yellow-500\" />;\r\n            default: return null;\r\n        }\r\n    };\r\n\r\n    const getAttendanceColor = (percentage) => {\r\n        if (percentage >= 90) return 'text-green-600 bg-green-50 border-green-200';\r\n        if (percentage >= 75) return 'text-yellow-600 bg-yellow-50 border-yellow-200';\r\n        return 'text-red-600 bg-red-50 border-red-200';\r\n    };\r\n\r\n    const StatCard = ({ title, value, subtitle, icon: Icon, trend }) => (\r\n        <div className=\"bg-gradient-to-br from-white via-blue-50 to-blue-100 rounded-xl p-6 shadow-md border border-blue-100 hover:shadow-lg transition-shadow\">\r\n            <div className=\"flex items-center justify-between mb-4\">\r\n                <div className=\"flex items-center space-x-3\">\r\n                    <div className=\"p-2 bg-blue-600 rounded-lg\">\r\n                        <Icon className=\"w-5 h-5 text-white\" />\r\n                    </div>\r\n                    <h3 className=\"text-blue-700 font-semibold\">{title}</h3>\r\n                </div>\r\n                {trend && <TrendingUp className=\"w-4 h-4 text-green-500\" />}\r\n            </div>\r\n            <div className=\"space-y-1\">\r\n                <p className=\"text-3xl font-extrabold text-blue-900\">{value}</p>\r\n                <p className=\"text-sm text-blue-600\">{subtitle}</p>\r\n            </div>\r\n        </div>\r\n    );\r\n\r\n    const ProgressBar = ({ percentage, label, total, present }) => (\r\n        <div className=\"space-y-2\">\r\n            <div className=\"flex justify-between items-center\">\r\n                <span className=\"text-sm font-semibold text-gray-700\">{label}</span>\r\n                <span className={`text-xs px-2 py-1 rounded-full border ${getAttendanceColor(percentage)}`}>\r\n                    {percentage}%\r\n                </span>\r\n            </div>\r\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\r\n                <div\r\n                    className={`h-2 rounded-full transition-all duration-300 ${percentage >= 90 ? 'bg-green-500' : percentage >= 75 ? 'bg-yellow-500' : 'bg-red-500'}`}\r\n                    style={{ width: `${percentage}%` }}\r\n                ></div>\r\n            </div>\r\n            <div className=\"flex justify-between text-xs text-gray-500\">\r\n                <span>Present: {present}</span>\r\n                <span>Total: {total}</span>\r\n            </div>\r\n        </div>\r\n    );\r\n\r\n    // Auto-switch to calendar view when filters are selected\r\n    useEffect(() => {\r\n        if (selectedCourse !== 'all' || selectedBatch !== 'all' || selectedSubject !== 'all') {\r\n            setActiveView('calendar');\r\n        }\r\n    }, [selectedCourse, selectedBatch, selectedSubject]);\r\n\r\n    // Filter attendance data based on selected filters\r\n    const getFilteredAttendanceData = () => {\r\n        return attendanceData.dailyRecords.filter(record => {\r\n            const courseMatch = selectedCourse === 'all' || record.course.toLowerCase() === selectedCourse.toLowerCase();\r\n            const batchMatch = selectedBatch === 'all' || record.batch.toLowerCase() === selectedBatch.toLowerCase();\r\n            const subjectMatch = selectedSubject === 'all' || record.subject.toLowerCase() === selectedSubject.toLowerCase();\r\n\r\n            return courseMatch && batchMatch && subjectMatch;\r\n        });\r\n    };\r\n\r\n    // Get attendance status for a specific date\r\n    const getAttendanceForDate = (date) => {\r\n        const dateStr = date.toISOString().split('T')[0];\r\n        const filteredData = getFilteredAttendanceData();\r\n        const dayRecords = filteredData.filter(record => record.date === dateStr);\r\n\r\n        if (dayRecords.length === 0) return null;\r\n\r\n        // Check if it's a holiday\r\n        if (dayRecords.some(record => record.type === 'holiday')) {\r\n            return { status: 'holiday', records: dayRecords };\r\n        }\r\n\r\n        // Calculate overall status for the day\r\n        const presentCount = dayRecords.filter(record => record.status === 'Present').length;\r\n        const absentCount = dayRecords.filter(record => record.status === 'Absent').length;\r\n\r\n        if (presentCount > 0 && absentCount === 0) return { status: 'present', records: dayRecords };\r\n        if (absentCount > 0 && presentCount === 0) return { status: 'absent', records: dayRecords };\r\n        if (presentCount > 0 && absentCount > 0) return { status: 'mixed', records: dayRecords };\r\n\r\n        return { status: 'unknown', records: dayRecords };\r\n    };\r\n\r\n    // Custom tile content for calendar (small dots)\r\n    const getTileContent = ({ date, view }) => {\r\n        if (view !== 'month') return null;\r\n\r\n        const attendance = getAttendanceForDate(date);\r\n        if (!attendance) return null;\r\n\r\n        const today = new Date();\r\n        const isFuture = date > today;\r\n\r\n        if (isFuture) return null;\r\n\r\n        return (\r\n            <div className=\"flex justify-center mt-1\">\r\n                <div className={`w-2 h-2 rounded-full ${\r\n                    attendance.status === 'present' ? 'bg-green-500' :\r\n                    attendance.status === 'absent' ? 'bg-red-500' :\r\n                    attendance.status === 'holiday' ? 'bg-gray-400' :\r\n                    attendance.status === 'mixed' ? 'bg-yellow-500' :\r\n                    'bg-blue-400'\r\n                }`} />\r\n            </div>\r\n        );\r\n    };\r\n\r\n    // Custom tile class name for calendar styling\r\n    const getTileClassName = ({ date, view }) => {\r\n        if (view !== 'month') return null;\r\n\r\n        const attendance = getAttendanceForDate(date);\r\n        const today = new Date();\r\n        const isFuture = date > today;\r\n\r\n        if (isFuture) {\r\n            return 'future-date';\r\n        }\r\n\r\n        if (!attendance) return 'no-class';\r\n\r\n        switch (attendance.status) {\r\n            case 'present':\r\n                return 'present-day';\r\n            case 'absent':\r\n                return 'absent-day';\r\n            case 'holiday':\r\n                return 'holiday-day';\r\n            case 'mixed':\r\n                return 'mixed-day';\r\n            default:\r\n                return 'no-class';\r\n        }\r\n    };\r\n\r\n    const handleDateChange = (date) => {\r\n        setSelectedDate(date);\r\n    };\r\n\r\n    return (\r\n        <div className=\"min-h-screen bg-gradient-to-tr from-blue-50 via-white to-pink-50 p-6\">\r\n            <div className=\"max-w-7xl mx-auto space-y-8\">\r\n                {/* Header */}\r\n                <div className=\"bg-white rounded-2xl p-8 shadow-lg border border-blue-100\">\r\n                    <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6\">\r\n                        <div>\r\n                            <h1 className=\"text-4xl font-extrabold text-blue-800 leading-tight\">Attendance Dashboard</h1>\r\n                            <p className=\"text-blue-600 mt-2 text-lg\">Monitor attendance by batch, course, and subject.</p>\r\n                        </div>\r\n                        <div className=\"flex items-center gap-3\">\r\n                            <button className=\"flex items-center gap-2 px-5 py-2 bg-blue-700 text-white rounded-xl shadow hover:bg-blue-800 transition-colors\">\r\n                                <Download className=\"w-4 h-4\" />\r\n                                <span>Export</span>\r\n                            </button>\r\n                            <button className=\"flex items-center gap-2 px-5 py-2 border border-blue-300 text-blue-700 rounded-xl hover:bg-blue-50 transition-colors\">\r\n                                <Filter className=\"w-4 h-4\" />\r\n                                <span>Filter</span>\r\n                            </button>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* View Toggle */}\r\n                <div className=\"bg-white rounded-2xl p-2 shadow-sm border border-blue-100\">\r\n                    <div className=\"flex flex-wrap gap-2 justify-center\">\r\n                        {[\r\n                            { id: 'overall', label: 'Overall', icon: TrendingUp },\r\n                            { id: 'subjects', label: 'By Subject', icon: BookOpen },\r\n                            { id: 'batches', label: 'By Batch', icon: Users },\r\n                            { id: 'courses', label: 'By Course', icon: GraduationCap },\r\n                            { id: 'calendar', label: 'Calendar View', icon: Calendar }\r\n                        ].map(({ id, label, icon: Icon }) => (\r\n                            <button\r\n                                key={id}\r\n                                onClick={() => setActiveView(id)}\r\n                                className={`flex items-center gap-2 px-5 py-2 rounded-xl font-medium text-base transition-all\r\n                  ${activeView === id\r\n                                        ? 'bg-blue-700 text-white shadow'\r\n                                        : 'text-blue-700 hover:bg-blue-100'\r\n                                    }`}\r\n                            >\r\n                                <Icon className=\"w-4 h-4\" />\r\n                                <span>{label}</span>\r\n                            </button>\r\n                        ))}\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Filters */}\r\n                <div className=\"bg-white rounded-2xl p-6 shadow-md border border-blue-100\">\r\n                    <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\r\n                        <div>\r\n                            <label className=\"block text-sm font-semibold text-blue-700 mb-2\">Date Range</label>\r\n                            <select\r\n                                value={dateRange}\r\n                                onChange={(e) => setDateRange(e.target.value)}\r\n                                className=\"w-full px-3 py-2 border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                            >\r\n                                <option value=\"today\">Today</option>\r\n                                <option value=\"thisWeek\">This Week</option>\r\n                                <option value=\"thisMonth\">This Month</option>\r\n                                <option value=\"lastMonth\">Last Month</option>\r\n                                <option value=\"thisYear\">This Year</option>\r\n                            </select>\r\n                        </div>\r\n                        <div>\r\n                            <label className=\"block text-sm font-semibold text-blue-700 mb-2\">Batch</label>\r\n                            <select\r\n                                value={selectedBatch}\r\n                                onChange={(e) => {\r\n                                    setSelectedBatch(e.target.value);\r\n                                    // Add smooth transition to calendar view\r\n                                    if (e.target.value !== 'all') {\r\n                                        setTimeout(() => setActiveView('calendar'), 300);\r\n                                    }\r\n                                }}\r\n                                className=\"w-full px-3 py-2 border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\r\n                            >\r\n                                <option value=\"all\">All Batches</option>\r\n                                <option value=\"Batch A\">Batch A</option>\r\n                                <option value=\"Batch B\">Batch B</option>\r\n                                <option value=\"Batch C\">Batch C</option>\r\n                            </select>\r\n                        </div>\r\n                        <div>\r\n                            <label className=\"block text-sm font-semibold text-blue-700 mb-2\">Course</label>\r\n                            <select\r\n                                value={selectedCourse}\r\n                                onChange={(e) => {\r\n                                    setSelectedCourse(e.target.value);\r\n                                    // Add smooth transition to calendar view\r\n                                    if (e.target.value !== 'all') {\r\n                                        setTimeout(() => setActiveView('calendar'), 300);\r\n                                    }\r\n                                }}\r\n                                className=\"w-full px-3 py-2 border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\r\n                            >\r\n                                <option value=\"all\">All Courses</option>\r\n                                <option value=\"Engineering\">Engineering</option>\r\n                                <option value=\"Science\">Science</option>\r\n                                <option value=\"Commerce\">Commerce</option>\r\n                            </select>\r\n                        </div>\r\n                        <div>\r\n                            <label className=\"block text-sm font-semibold text-blue-700 mb-2\">Subject</label>\r\n                            <select\r\n                                value={selectedSubject}\r\n                                onChange={(e) => {\r\n                                    setSelectedSubject(e.target.value);\r\n                                    // Add smooth transition to calendar view\r\n                                    if (e.target.value !== 'all') {\r\n                                        setTimeout(() => setActiveView('calendar'), 300);\r\n                                    }\r\n                                }}\r\n                                className=\"w-full px-3 py-2 border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\r\n                            >\r\n                                <option value=\"all\">All Subjects</option>\r\n                                <option value=\"Mathematics\">Mathematics</option>\r\n                                <option value=\"Physics\">Physics</option>\r\n                                <option value=\"Chemistry\">Chemistry</option>\r\n                                <option value=\"Computer Science\">Computer Science</option>\r\n                                <option value=\"English\">English</option>\r\n                                <option value=\"Biology\">Biology</option>\r\n                                <option value=\"Accounting\">Accounting</option>\r\n                                <option value=\"Economics\">Economics</option>\r\n                                <option value=\"Business Studies\">Business Studies</option>\r\n                            </select>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Stats Cards */}\r\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\r\n                    <StatCard\r\n                        title=\"Overall Attendance\"\r\n                        value=\"85%\"\r\n                        subtitle=\"This month average\"\r\n                        icon={TrendingUp}\r\n                        trend={true}\r\n                    />\r\n                    <StatCard\r\n                        title=\"Total Classes\"\r\n                        value=\"124\"\r\n                        subtitle=\"Classes conducted\"\r\n                        icon={Calendar}\r\n                    />\r\n                    <StatCard\r\n                        title=\"Present Days\"\r\n                        value=\"105\"\r\n                        subtitle=\"Out of 124 classes\"\r\n                        icon={CheckCircle}\r\n                    />\r\n                </div>\r\n\r\n                {/* Main Content */}\r\n                <div className=\"grid grid-cols-1 xl:grid-cols-3 gap-8\">\r\n                    {/* Left Column - Main Data */}\r\n                    <div className=\"xl:col-span-2 space-y-8\">\r\n                        {activeView === 'overall' && (\r\n                            <div className=\"bg-white rounded-2xl p-8 shadow-md border border-blue-100\">\r\n                                <h3 className=\"text-xl font-bold text-blue-900 mb-6\">Overall Attendance Summary</h3>\r\n                                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\r\n                                    <div className=\"text-center p-8 bg-gradient-to-tr from-blue-50 via-blue-100 to-white rounded-xl shadow\">\r\n                                        <div className=\"text-5xl font-extrabold text-blue-700 mb-2 drop-shadow\">85%</div>\r\n                                        <div className=\"text-blue-800 font-semibold\">Overall Attendance</div>\r\n                                    </div>\r\n                                    <div className=\"space-y-5\">\r\n                                        <div className=\"flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-xl shadow-sm\">\r\n                                            <div className=\"flex items-center gap-3\">\r\n                                                <CheckCircle className=\"w-8 h-8 text-green-600\" />\r\n                                                <div>\r\n                                                    <div className=\"font-semibold text-green-900\">Present</div>\r\n                                                    <div className=\"text-sm text-green-700\">Days attended</div>\r\n                                                </div>\r\n                                            </div>\r\n                                            <div className=\"text-2xl font-extrabold text-green-600\">105</div>\r\n                                        </div>\r\n                                        <div className=\"flex items-center justify-between p-4 bg-red-50 border border-red-200 rounded-xl shadow-sm\">\r\n                                            <div className=\"flex items-center gap-3\">\r\n                                                <XCircle className=\"w-8 h-8 text-red-600\" />\r\n                                                <div>\r\n                                                    <div className=\"font-semibold text-red-900\">Absent</div>\r\n                                                    <div className=\"text-sm text-red-700\">Days missed</div>\r\n                                                </div>\r\n                                            </div>\r\n                                            <div className=\"text-2xl font-extrabold text-red-600\">19</div>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        )}\r\n\r\n                        {activeView === 'subjects' && (\r\n                            <div className=\"bg-white rounded-2xl p-8 shadow-md border border-blue-100\">\r\n                                <h3 className=\"text-xl font-bold text-blue-900 mb-6\">Subject-wise Attendance</h3>\r\n                                <div className=\"space-y-6\">\r\n                                    {attendanceData.subjects.map((subject, index) => (\r\n                                        <ProgressBar\r\n                                            key={index}\r\n                                            percentage={subject.percentage}\r\n                                            label={subject.name}\r\n                                            total={subject.total}\r\n                                            present={subject.present}\r\n                                        />\r\n                                    ))}\r\n                                </div>\r\n                            </div>\r\n                        )}\r\n\r\n                        {activeView === 'batches' && (\r\n                            <div className=\"bg-white rounded-2xl p-8 shadow-md border border-blue-100\">\r\n                                <h3 className=\"text-xl font-bold text-blue-900 mb-6\">Batch-wise Attendance</h3>\r\n                                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\r\n                                    {attendanceData.batches.map((batch, index) => (\r\n                                        <div key={index} className=\"p-6 bg-blue-50 rounded-xl border border-blue-200 shadow hover:scale-105 transition-transform\">\r\n                                            <div className=\"flex items-center justify-between mb-3\">\r\n                                                <h4 className=\"font-semibold text-blue-900\">{batch.name}</h4>\r\n                                                <Users className=\"w-5 h-5 text-blue-400\" />\r\n                                            </div>\r\n                                            <div className=\"space-y-2\">\r\n                                                <div className=\"text-2xl font-bold text-blue-900\">{batch.avgAttendance}%</div>\r\n                                                <div className=\"text-sm text-blue-700\">{batch.students} students</div>\r\n                                                <div className=\"w-full bg-blue-200 rounded-full h-2\">\r\n                                                    <div\r\n                                                        className=\"h-2 bg-blue-600 rounded-full transition-all duration-300\"\r\n                                                        style={{ width: `${batch.avgAttendance}%` }}\r\n                                                    ></div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                    ))}\r\n                                </div>\r\n                            </div>\r\n                        )}\r\n\r\n                        {activeView === 'courses' && (\r\n                            <div className=\"bg-white rounded-2xl p-8 shadow-md border border-blue-100\">\r\n                                <h3 className=\"text-xl font-bold text-blue-900 mb-6\">Course-wise Attendance</h3>\r\n                                <div className=\"space-y-6\">\r\n                                    {attendanceData.courses.map((course, index) => (\r\n                                        <div key={index} className=\"flex items-center justify-between p-5 border border-blue-200 rounded-xl bg-blue-50 hover:bg-blue-100 transition-colors shadow\">\r\n                                            <div className=\"flex items-center gap-4\">\r\n                                                <div className=\"p-3 bg-blue-700 rounded-lg\">\r\n                                                    <GraduationCap className=\"w-6 h-6 text-white\" />\r\n                                                </div>\r\n                                                <div>\r\n                                                    <h4 className=\"font-semibold text-blue-900\">{course.name}</h4>\r\n                                                    <p className=\"text-sm text-blue-700\">{course.students} students enrolled</p>\r\n                                                </div>\r\n                                            </div>\r\n                                            <div>\r\n                                                <div className={`text-lg font-bold px-4 py-1 rounded-full border ${getAttendanceColor(course.avgAttendance)}`}>\r\n                                                    {course.avgAttendance}%\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                    ))}\r\n                                </div>\r\n                            </div>\r\n                        )}\r\n\r\n                        {activeView === 'calendar' && (\r\n                            <div className=\"bg-white rounded-2xl p-8 shadow-md border border-blue-100\">\r\n                                <h3 className=\"text-xl font-bold text-blue-900 mb-6\">Calendar View</h3>\r\n                                <CalendarView\r\n                                    value={selectedDate}\r\n                                    onChange={handleDateChange}\r\n                                    className=\"bg-white rounded-xl shadow\"\r\n                                />\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n\r\n                    {/* Right Column - Recent Activity */}\r\n                    <div className=\"bg-white rounded-2xl p-8 shadow-md border border-blue-100\">\r\n                        <h3 className=\"text-xl font-bold text-blue-900 mb-6 flex items-center gap-2\">\r\n                            <Clock className=\"w-5 h-5 text-blue-700\" />\r\n                            <span>Recent Activity</span>\r\n                        </h3>\r\n                        <div className=\"space-y-5\">\r\n                            {attendanceData.recentActivity.map((activity, index) => (\r\n                                <div key={index} className=\"flex items-center gap-3 p-4 bg-blue-50 rounded-xl border hover:bg-blue-100 transition-colors\">\r\n                                    {getStatusIcon(activity.status)}\r\n                                    <div className=\"flex-1\">\r\n                                        <div className=\"font-semibold text-blue-900\">{activity.subject}</div>\r\n                                        <div className=\"text-sm text-blue-700\">{activity.date} • {activity.time}</div>\r\n                                    </div>\r\n                                    <div className={`text-xs px-3 py-1 rounded-full capitalize border\r\n                    ${activity.status === 'present' ? 'bg-green-100 text-green-800 border-green-300' :\r\n                                            activity.status === 'absent' ? 'bg-red-100 text-red-800 border-red-300' :\r\n                                                'bg-yellow-100 text-yellow-800 border-yellow-300'\r\n                                        }`}>\r\n                                        {activity.status}\r\n                                    </div>\r\n                                </div>\r\n                            ))}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default AttendancePage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACIC,QAAQ,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,MAAM,EAAEC,QAAQ,EAC1DC,UAAU,EAAEC,KAAK,EAAEC,WAAW,EAAEC,OAAO,EAAEC,WAAW,EAAEC,IAAI,QACvD,cAAc;AACrB,OAAO,kCAAkC;AACzC,OAAOC,YAAY,MAAM,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,SAAS,CAAC;EACvD,MAAM,CAACqB,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACuB,cAAc,EAAEC,iBAAiB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACyB,eAAe,EAAEC,kBAAkB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,WAAW,CAAC;EACvD,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC,IAAI+B,IAAI,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACC,cAAc,CAAC,GAAGhC,QAAQ,CAAC;IAC9BiC,OAAO,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAEC,MAAM,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAI,CAAC;IAChDC,QAAQ,EAAE,CACN;MAAEC,IAAI,EAAE,aAAa;MAAEJ,OAAO,EAAE,EAAE;MAAEE,KAAK,EAAE,EAAE;MAAEG,UAAU,EAAE;IAAG,CAAC,EAC/D;MAAED,IAAI,EAAE,SAAS;MAAEJ,OAAO,EAAE,EAAE;MAAEE,KAAK,EAAE,EAAE;MAAEG,UAAU,EAAE;IAAG,CAAC,EAC3D;MAAED,IAAI,EAAE,WAAW;MAAEJ,OAAO,EAAE,EAAE;MAAEE,KAAK,EAAE,EAAE;MAAEG,UAAU,EAAE;IAAG,CAAC,EAC7D;MAAED,IAAI,EAAE,kBAAkB;MAAEJ,OAAO,EAAE,EAAE;MAAEE,KAAK,EAAE,EAAE;MAAEG,UAAU,EAAE;IAAG,CAAC,EACpE;MAAED,IAAI,EAAE,SAAS;MAAEJ,OAAO,EAAE,EAAE;MAAEE,KAAK,EAAE,EAAE;MAAEG,UAAU,EAAE;IAAG,CAAC,CAC9D;IACDC,OAAO,EAAE,CACL;MAAEF,IAAI,EAAE,SAAS;MAAEG,QAAQ,EAAE,EAAE;MAAEC,aAAa,EAAE;IAAG,CAAC,EACpD;MAAEJ,IAAI,EAAE,SAAS;MAAEG,QAAQ,EAAE,EAAE;MAAEC,aAAa,EAAE;IAAG,CAAC,EACpD;MAAEJ,IAAI,EAAE,SAAS;MAAEG,QAAQ,EAAE,EAAE;MAAEC,aAAa,EAAE;IAAG,CAAC,CACvD;IACDC,OAAO,EAAE,CACL;MAAEL,IAAI,EAAE,aAAa;MAAEG,QAAQ,EAAE,GAAG;MAAEC,aAAa,EAAE;IAAG,CAAC,EACzD;MAAEJ,IAAI,EAAE,SAAS;MAAEG,QAAQ,EAAE,EAAE;MAAEC,aAAa,EAAE;IAAG,CAAC,EACpD;MAAEJ,IAAI,EAAE,UAAU;MAAEG,QAAQ,EAAE,EAAE;MAAEC,aAAa,EAAE;IAAG,CAAC,CACxD;IACDE,cAAc,EAAE,CACZ;MAAEC,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,aAAa;MAAEC,MAAM,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAW,CAAC,EACnF;MAAEH,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAW,CAAC,EAC/E;MAAEH,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,WAAW;MAAEC,MAAM,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAW,CAAC,EAChF;MAAEH,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,kBAAkB;MAAEC,MAAM,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAW,CAAC,EACxF;MAAEH,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAW,CAAC,CAClF;IACD;IACAC,YAAY,EAAE;IACV;IACA;MAAEJ,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,aAAa;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACzH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACrH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,WAAW;MAAEC,MAAM,EAAE,QAAQ;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACtH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,aAAa;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACzH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACrH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,kBAAkB;MAAEC,MAAM,EAAE,QAAQ;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EAC7H;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACrH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,KAAK;MAAEC,KAAK,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAU,CAAC,EAC3G;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,KAAK;MAAEC,KAAK,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAU,CAAC,EAC3G;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,aAAa;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACzH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,WAAW;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACvH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,QAAQ;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACpH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,kBAAkB;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EAC9H;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACrH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,aAAa;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACzH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,WAAW;MAAEC,MAAM,EAAE,QAAQ;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACtH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC;IAErH;IACA;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,SAAS;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACjH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,WAAW;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,SAAS;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACnH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,QAAQ;MAAEG,MAAM,EAAE,SAAS;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EAChH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,aAAa;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,SAAS;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACrH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,SAAS;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC;IAEjH;IACA;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,YAAY;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,UAAU;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACrH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,WAAW;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,UAAU;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACpH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,kBAAkB;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,UAAU;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EAC3H;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,YAAY;MAAEC,MAAM,EAAE,QAAQ;MAAEG,MAAM,EAAE,UAAU;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACpH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,WAAW;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,UAAU;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC;EAE5H,CAAC,CAAC;EAEF,MAAMC,aAAa,GAAIN,MAAM,IAAK;IAC9B,QAAQA,MAAM;MACV,KAAK,SAAS;QAAE,oBAAO/B,OAAA,CAACN,WAAW;UAAC4C,SAAS,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzE,KAAK,QAAQ;QAAE,oBAAO1C,OAAA,CAACL,OAAO;UAAC2C,SAAS,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAClE,KAAK,MAAM;QAAE,oBAAO1C,OAAA,CAACJ,WAAW;UAAC0C,SAAS,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvE;QAAS,OAAO,IAAI;IACxB;EACJ,CAAC;EAED,MAAMC,kBAAkB,GAAIpB,UAAU,IAAK;IACvC,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO,6CAA6C;IAC1E,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO,gDAAgD;IAC7E,OAAO,uCAAuC;EAClD,CAAC;EAED,MAAMqB,QAAQ,GAAGA,CAAC;IAAEC,KAAK;IAAEC,KAAK;IAAEC,QAAQ;IAAEC,IAAI,EAAEC,IAAI;IAAEC;EAAM,CAAC,kBAC3DlD,OAAA;IAAKsC,SAAS,EAAC,wIAAwI;IAAAa,QAAA,gBACnJnD,OAAA;MAAKsC,SAAS,EAAC,wCAAwC;MAAAa,QAAA,gBACnDnD,OAAA;QAAKsC,SAAS,EAAC,6BAA6B;QAAAa,QAAA,gBACxCnD,OAAA;UAAKsC,SAAS,EAAC,4BAA4B;UAAAa,QAAA,eACvCnD,OAAA,CAACiD,IAAI;YAACX,SAAS,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eACN1C,OAAA;UAAIsC,SAAS,EAAC,6BAA6B;UAAAa,QAAA,EAAEN;QAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,EACLQ,KAAK,iBAAIlD,OAAA,CAACR,UAAU;QAAC8C,SAAS,EAAC;MAAwB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CAAC,eACN1C,OAAA;MAAKsC,SAAS,EAAC,WAAW;MAAAa,QAAA,gBACtBnD,OAAA;QAAGsC,SAAS,EAAC,uCAAuC;QAAAa,QAAA,EAAEL;MAAK;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChE1C,OAAA;QAAGsC,SAAS,EAAC,uBAAuB;QAAAa,QAAA,EAAEJ;MAAQ;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACR;EAED,MAAMU,WAAW,GAAGA,CAAC;IAAE7B,UAAU;IAAE8B,KAAK;IAAEjC,KAAK;IAAEF;EAAQ,CAAC,kBACtDlB,OAAA;IAAKsC,SAAS,EAAC,WAAW;IAAAa,QAAA,gBACtBnD,OAAA;MAAKsC,SAAS,EAAC,mCAAmC;MAAAa,QAAA,gBAC9CnD,OAAA;QAAMsC,SAAS,EAAC,qCAAqC;QAAAa,QAAA,EAAEE;MAAK;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACpE1C,OAAA;QAAMsC,SAAS,EAAG,yCAAwCK,kBAAkB,CAACpB,UAAU,CAAE,EAAE;QAAA4B,QAAA,GACtF5B,UAAU,EAAC,GAChB;MAAA;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eACN1C,OAAA;MAAKsC,SAAS,EAAC,qCAAqC;MAAAa,QAAA,eAChDnD,OAAA;QACIsC,SAAS,EAAG,gDAA+Cf,UAAU,IAAI,EAAE,GAAG,cAAc,GAAGA,UAAU,IAAI,EAAE,GAAG,eAAe,GAAG,YAAa,EAAE;QACnJ+B,KAAK,EAAE;UAAEC,KAAK,EAAG,GAAEhC,UAAW;QAAG;MAAE;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eACN1C,OAAA;MAAKsC,SAAS,EAAC,4CAA4C;MAAAa,QAAA,gBACvDnD,OAAA;QAAAmD,QAAA,GAAM,WAAS,EAACjC,OAAO;MAAA;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC/B1C,OAAA;QAAAmD,QAAA,GAAM,SAAO,EAAC/B,KAAK;MAAA;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACR;;EAED;EACAzD,SAAS,CAAC,MAAM;IACZ,IAAIsB,cAAc,KAAK,KAAK,IAAIF,aAAa,KAAK,KAAK,IAAII,eAAe,KAAK,KAAK,EAAE;MAClFL,aAAa,CAAC,UAAU,CAAC;IAC7B;EACJ,CAAC,EAAE,CAACG,cAAc,EAAEF,aAAa,EAAEI,eAAe,CAAC,CAAC;;EAEpD;EACA,MAAM+C,yBAAyB,GAAGA,CAAA,KAAM;IACpC,OAAOxC,cAAc,CAACiB,YAAY,CAACwB,MAAM,CAACC,MAAM,IAAI;MAChD,MAAMC,WAAW,GAAGpD,cAAc,KAAK,KAAK,IAAImD,MAAM,CAACxB,MAAM,CAAC0B,WAAW,CAAC,CAAC,KAAKrD,cAAc,CAACqD,WAAW,CAAC,CAAC;MAC5G,MAAMC,UAAU,GAAGxD,aAAa,KAAK,KAAK,IAAIqD,MAAM,CAACvB,KAAK,CAACyB,WAAW,CAAC,CAAC,KAAKvD,aAAa,CAACuD,WAAW,CAAC,CAAC;MACxG,MAAME,YAAY,GAAGrD,eAAe,KAAK,KAAK,IAAIiD,MAAM,CAAC5B,OAAO,CAAC8B,WAAW,CAAC,CAAC,KAAKnD,eAAe,CAACmD,WAAW,CAAC,CAAC;MAEhH,OAAOD,WAAW,IAAIE,UAAU,IAAIC,YAAY;IACpD,CAAC,CAAC;EACN,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAIlC,IAAI,IAAK;IACnC,MAAMmC,OAAO,GAAGnC,IAAI,CAACoC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAChD,MAAMC,YAAY,GAAGX,yBAAyB,CAAC,CAAC;IAChD,MAAMY,UAAU,GAAGD,YAAY,CAACV,MAAM,CAACC,MAAM,IAAIA,MAAM,CAAC7B,IAAI,KAAKmC,OAAO,CAAC;IAEzE,IAAII,UAAU,CAACC,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;;IAExC;IACA,IAAID,UAAU,CAACE,IAAI,CAACZ,MAAM,IAAIA,MAAM,CAACtB,IAAI,KAAK,SAAS,CAAC,EAAE;MACtD,OAAO;QAAEL,MAAM,EAAE,SAAS;QAAEwC,OAAO,EAAEH;MAAW,CAAC;IACrD;;IAEA;IACA,MAAMI,YAAY,GAAGJ,UAAU,CAACX,MAAM,CAACC,MAAM,IAAIA,MAAM,CAAC3B,MAAM,KAAK,SAAS,CAAC,CAACsC,MAAM;IACpF,MAAMI,WAAW,GAAGL,UAAU,CAACX,MAAM,CAACC,MAAM,IAAIA,MAAM,CAAC3B,MAAM,KAAK,QAAQ,CAAC,CAACsC,MAAM;IAElF,IAAIG,YAAY,GAAG,CAAC,IAAIC,WAAW,KAAK,CAAC,EAAE,OAAO;MAAE1C,MAAM,EAAE,SAAS;MAAEwC,OAAO,EAAEH;IAAW,CAAC;IAC5F,IAAIK,WAAW,GAAG,CAAC,IAAID,YAAY,KAAK,CAAC,EAAE,OAAO;MAAEzC,MAAM,EAAE,QAAQ;MAAEwC,OAAO,EAAEH;IAAW,CAAC;IAC3F,IAAII,YAAY,GAAG,CAAC,IAAIC,WAAW,GAAG,CAAC,EAAE,OAAO;MAAE1C,MAAM,EAAE,OAAO;MAAEwC,OAAO,EAAEH;IAAW,CAAC;IAExF,OAAO;MAAErC,MAAM,EAAE,SAAS;MAAEwC,OAAO,EAAEH;IAAW,CAAC;EACrD,CAAC;;EAED;EACA,MAAMM,cAAc,GAAGA,CAAC;IAAE7C,IAAI;IAAE8C;EAAK,CAAC,KAAK;IACvC,IAAIA,IAAI,KAAK,OAAO,EAAE,OAAO,IAAI;IAEjC,MAAMC,UAAU,GAAGb,oBAAoB,CAAClC,IAAI,CAAC;IAC7C,IAAI,CAAC+C,UAAU,EAAE,OAAO,IAAI;IAE5B,MAAMC,KAAK,GAAG,IAAI9D,IAAI,CAAC,CAAC;IACxB,MAAM+D,QAAQ,GAAGjD,IAAI,GAAGgD,KAAK;IAE7B,IAAIC,QAAQ,EAAE,OAAO,IAAI;IAEzB,oBACI9E,OAAA;MAAKsC,SAAS,EAAC,0BAA0B;MAAAa,QAAA,eACrCnD,OAAA;QAAKsC,SAAS,EAAG,wBACbsC,UAAU,CAAC7C,MAAM,KAAK,SAAS,GAAG,cAAc,GAChD6C,UAAU,CAAC7C,MAAM,KAAK,QAAQ,GAAG,YAAY,GAC7C6C,UAAU,CAAC7C,MAAM,KAAK,SAAS,GAAG,aAAa,GAC/C6C,UAAU,CAAC7C,MAAM,KAAK,OAAO,GAAG,eAAe,GAC/C,aACH;MAAE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd,CAAC;;EAED;EACA,MAAMqC,gBAAgB,GAAGA,CAAC;IAAElD,IAAI;IAAE8C;EAAK,CAAC,KAAK;IACzC,IAAIA,IAAI,KAAK,OAAO,EAAE,OAAO,IAAI;IAEjC,MAAMC,UAAU,GAAGb,oBAAoB,CAAClC,IAAI,CAAC;IAC7C,MAAMgD,KAAK,GAAG,IAAI9D,IAAI,CAAC,CAAC;IACxB,MAAM+D,QAAQ,GAAGjD,IAAI,GAAGgD,KAAK;IAE7B,IAAIC,QAAQ,EAAE;MACV,OAAO,aAAa;IACxB;IAEA,IAAI,CAACF,UAAU,EAAE,OAAO,UAAU;IAElC,QAAQA,UAAU,CAAC7C,MAAM;MACrB,KAAK,SAAS;QACV,OAAO,aAAa;MACxB,KAAK,QAAQ;QACT,OAAO,YAAY;MACvB,KAAK,SAAS;QACV,OAAO,aAAa;MACxB,KAAK,OAAO;QACR,OAAO,WAAW;MACtB;QACI,OAAO,UAAU;IACzB;EACJ,CAAC;EAED,MAAMiD,gBAAgB,GAAInD,IAAI,IAAK;IAC/Bf,eAAe,CAACe,IAAI,CAAC;EACzB,CAAC;EAED,oBACI7B,OAAA;IAAKsC,SAAS,EAAC,sEAAsE;IAAAa,QAAA,eACjFnD,OAAA;MAAKsC,SAAS,EAAC,6BAA6B;MAAAa,QAAA,gBAExCnD,OAAA;QAAKsC,SAAS,EAAC,2DAA2D;QAAAa,QAAA,eACtEnD,OAAA;UAAKsC,SAAS,EAAC,oEAAoE;UAAAa,QAAA,gBAC/EnD,OAAA;YAAAmD,QAAA,gBACInD,OAAA;cAAIsC,SAAS,EAAC,qDAAqD;cAAAa,QAAA,EAAC;YAAoB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7F1C,OAAA;cAAGsC,SAAS,EAAC,4BAA4B;cAAAa,QAAA,EAAC;YAAiD;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9F,CAAC,eACN1C,OAAA;YAAKsC,SAAS,EAAC,yBAAyB;YAAAa,QAAA,gBACpCnD,OAAA;cAAQsC,SAAS,EAAC,gHAAgH;cAAAa,QAAA,gBAC9HnD,OAAA,CAACT,QAAQ;gBAAC+C,SAAS,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChC1C,OAAA;gBAAAmD,QAAA,EAAM;cAAM;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACT1C,OAAA;cAAQsC,SAAS,EAAC,sHAAsH;cAAAa,QAAA,gBACpInD,OAAA,CAACV,MAAM;gBAACgD,SAAS,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9B1C,OAAA;gBAAAmD,QAAA,EAAM;cAAM;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGN1C,OAAA;QAAKsC,SAAS,EAAC,2DAA2D;QAAAa,QAAA,eACtEnD,OAAA;UAAKsC,SAAS,EAAC,qCAAqC;UAAAa,QAAA,EAC/C,CACG;YAAE8B,EAAE,EAAE,SAAS;YAAE5B,KAAK,EAAE,SAAS;YAAEL,IAAI,EAAExD;UAAW,CAAC,EACrD;YAAEyF,EAAE,EAAE,UAAU;YAAE5B,KAAK,EAAE,YAAY;YAAEL,IAAI,EAAE5D;UAAS,CAAC,EACvD;YAAE6F,EAAE,EAAE,SAAS;YAAE5B,KAAK,EAAE,UAAU;YAAEL,IAAI,EAAE7D;UAAM,CAAC,EACjD;YAAE8F,EAAE,EAAE,SAAS;YAAE5B,KAAK,EAAE,WAAW;YAAEL,IAAI,EAAE3D;UAAc,CAAC,EAC1D;YAAE4F,EAAE,EAAE,UAAU;YAAE5B,KAAK,EAAE,eAAe;YAAEL,IAAI,EAAE9D;UAAS,CAAC,CAC7D,CAACgG,GAAG,CAAC,CAAC;YAAED,EAAE;YAAE5B,KAAK;YAAEL,IAAI,EAAEC;UAAK,CAAC,kBAC5BjD,OAAA;YAEImF,OAAO,EAAEA,CAAA,KAAM/E,aAAa,CAAC6E,EAAE,CAAE;YACjC3C,SAAS,EAAG;AAC5C,oBAAoBnC,UAAU,KAAK8E,EAAE,GACK,+BAA+B,GAC/B,iCACL,EAAE;YAAA9B,QAAA,gBAEPnD,OAAA,CAACiD,IAAI;cAACX,SAAS,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5B1C,OAAA;cAAAmD,QAAA,EAAOE;YAAK;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GATfuC,EAAE;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUH,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGN1C,OAAA;QAAKsC,SAAS,EAAC,2DAA2D;QAAAa,QAAA,eACtEnD,OAAA;UAAKsC,SAAS,EAAC,uCAAuC;UAAAa,QAAA,gBAClDnD,OAAA;YAAAmD,QAAA,gBACInD,OAAA;cAAOsC,SAAS,EAAC,gDAAgD;cAAAa,QAAA,EAAC;YAAU;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpF1C,OAAA;cACI8C,KAAK,EAAEnC,SAAU;cACjByE,QAAQ,EAAGC,CAAC,IAAKzE,YAAY,CAACyE,CAAC,CAACC,MAAM,CAACxC,KAAK,CAAE;cAC9CR,SAAS,EAAC,8GAA8G;cAAAa,QAAA,gBAExHnD,OAAA;gBAAQ8C,KAAK,EAAC,OAAO;gBAAAK,QAAA,EAAC;cAAK;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpC1C,OAAA;gBAAQ8C,KAAK,EAAC,UAAU;gBAAAK,QAAA,EAAC;cAAS;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC3C1C,OAAA;gBAAQ8C,KAAK,EAAC,WAAW;gBAAAK,QAAA,EAAC;cAAU;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC7C1C,OAAA;gBAAQ8C,KAAK,EAAC,WAAW;gBAAAK,QAAA,EAAC;cAAU;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC7C1C,OAAA;gBAAQ8C,KAAK,EAAC,UAAU;gBAAAK,QAAA,EAAC;cAAS;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACN1C,OAAA;YAAAmD,QAAA,gBACInD,OAAA;cAAOsC,SAAS,EAAC,gDAAgD;cAAAa,QAAA,EAAC;YAAK;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/E1C,OAAA;cACI8C,KAAK,EAAEzC,aAAc;cACrB+E,QAAQ,EAAGC,CAAC,IAAK;gBACb/E,gBAAgB,CAAC+E,CAAC,CAACC,MAAM,CAACxC,KAAK,CAAC;gBAChC;gBACA,IAAIuC,CAAC,CAACC,MAAM,CAACxC,KAAK,KAAK,KAAK,EAAE;kBAC1ByC,UAAU,CAAC,MAAMnF,aAAa,CAAC,UAAU,CAAC,EAAE,GAAG,CAAC;gBACpD;cACJ,CAAE;cACFkC,SAAS,EAAC,0IAA0I;cAAAa,QAAA,gBAEpJnD,OAAA;gBAAQ8C,KAAK,EAAC,KAAK;gBAAAK,QAAA,EAAC;cAAW;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC1C,OAAA;gBAAQ8C,KAAK,EAAC,SAAS;gBAAAK,QAAA,EAAC;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC1C,OAAA;gBAAQ8C,KAAK,EAAC,SAAS;gBAAAK,QAAA,EAAC;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC1C,OAAA;gBAAQ8C,KAAK,EAAC,SAAS;gBAAAK,QAAA,EAAC;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACN1C,OAAA;YAAAmD,QAAA,gBACInD,OAAA;cAAOsC,SAAS,EAAC,gDAAgD;cAAAa,QAAA,EAAC;YAAM;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChF1C,OAAA;cACI8C,KAAK,EAAEvC,cAAe;cACtB6E,QAAQ,EAAGC,CAAC,IAAK;gBACb7E,iBAAiB,CAAC6E,CAAC,CAACC,MAAM,CAACxC,KAAK,CAAC;gBACjC;gBACA,IAAIuC,CAAC,CAACC,MAAM,CAACxC,KAAK,KAAK,KAAK,EAAE;kBAC1ByC,UAAU,CAAC,MAAMnF,aAAa,CAAC,UAAU,CAAC,EAAE,GAAG,CAAC;gBACpD;cACJ,CAAE;cACFkC,SAAS,EAAC,0IAA0I;cAAAa,QAAA,gBAEpJnD,OAAA;gBAAQ8C,KAAK,EAAC,KAAK;gBAAAK,QAAA,EAAC;cAAW;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC1C,OAAA;gBAAQ8C,KAAK,EAAC,aAAa;gBAAAK,QAAA,EAAC;cAAW;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChD1C,OAAA;gBAAQ8C,KAAK,EAAC,SAAS;gBAAAK,QAAA,EAAC;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC1C,OAAA;gBAAQ8C,KAAK,EAAC,UAAU;gBAAAK,QAAA,EAAC;cAAQ;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACN1C,OAAA;YAAAmD,QAAA,gBACInD,OAAA;cAAOsC,SAAS,EAAC,gDAAgD;cAAAa,QAAA,EAAC;YAAO;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjF1C,OAAA;cACI8C,KAAK,EAAErC,eAAgB;cACvB2E,QAAQ,EAAGC,CAAC,IAAK;gBACb3E,kBAAkB,CAAC2E,CAAC,CAACC,MAAM,CAACxC,KAAK,CAAC;gBAClC;gBACA,IAAIuC,CAAC,CAACC,MAAM,CAACxC,KAAK,KAAK,KAAK,EAAE;kBAC1ByC,UAAU,CAAC,MAAMnF,aAAa,CAAC,UAAU,CAAC,EAAE,GAAG,CAAC;gBACpD;cACJ,CAAE;cACFkC,SAAS,EAAC,0IAA0I;cAAAa,QAAA,gBAEpJnD,OAAA;gBAAQ8C,KAAK,EAAC,KAAK;gBAAAK,QAAA,EAAC;cAAY;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACzC1C,OAAA;gBAAQ8C,KAAK,EAAC,aAAa;gBAAAK,QAAA,EAAC;cAAW;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChD1C,OAAA;gBAAQ8C,KAAK,EAAC,SAAS;gBAAAK,QAAA,EAAC;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC1C,OAAA;gBAAQ8C,KAAK,EAAC,WAAW;gBAAAK,QAAA,EAAC;cAAS;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5C1C,OAAA;gBAAQ8C,KAAK,EAAC,kBAAkB;gBAAAK,QAAA,EAAC;cAAgB;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1D1C,OAAA;gBAAQ8C,KAAK,EAAC,SAAS;gBAAAK,QAAA,EAAC;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC1C,OAAA;gBAAQ8C,KAAK,EAAC,SAAS;gBAAAK,QAAA,EAAC;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC1C,OAAA;gBAAQ8C,KAAK,EAAC,YAAY;gBAAAK,QAAA,EAAC;cAAU;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9C1C,OAAA;gBAAQ8C,KAAK,EAAC,WAAW;gBAAAK,QAAA,EAAC;cAAS;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5C1C,OAAA;gBAAQ8C,KAAK,EAAC,kBAAkB;gBAAAK,QAAA,EAAC;cAAgB;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGN1C,OAAA;QAAKsC,SAAS,EAAC,uCAAuC;QAAAa,QAAA,gBAClDnD,OAAA,CAAC4C,QAAQ;UACLC,KAAK,EAAC,oBAAoB;UAC1BC,KAAK,EAAC,KAAK;UACXC,QAAQ,EAAC,oBAAoB;UAC7BC,IAAI,EAAExD,UAAW;UACjB0D,KAAK,EAAE;QAAK;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACF1C,OAAA,CAAC4C,QAAQ;UACLC,KAAK,EAAC,eAAe;UACrBC,KAAK,EAAC,KAAK;UACXC,QAAQ,EAAC,mBAAmB;UAC5BC,IAAI,EAAE9D;QAAS;UAAAqD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACF1C,OAAA,CAAC4C,QAAQ;UACLC,KAAK,EAAC,cAAc;UACpBC,KAAK,EAAC,KAAK;UACXC,QAAQ,EAAC,oBAAoB;UAC7BC,IAAI,EAAEtD;QAAY;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGN1C,OAAA;QAAKsC,SAAS,EAAC,uCAAuC;QAAAa,QAAA,gBAElDnD,OAAA;UAAKsC,SAAS,EAAC,yBAAyB;UAAAa,QAAA,GACnChD,UAAU,KAAK,SAAS,iBACrBH,OAAA;YAAKsC,SAAS,EAAC,2DAA2D;YAAAa,QAAA,gBACtEnD,OAAA;cAAIsC,SAAS,EAAC,sCAAsC;cAAAa,QAAA,EAAC;YAA0B;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpF1C,OAAA;cAAKsC,SAAS,EAAC,uCAAuC;cAAAa,QAAA,gBAClDnD,OAAA;gBAAKsC,SAAS,EAAC,wFAAwF;gBAAAa,QAAA,gBACnGnD,OAAA;kBAAKsC,SAAS,EAAC,wDAAwD;kBAAAa,QAAA,EAAC;gBAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjF1C,OAAA;kBAAKsC,SAAS,EAAC,6BAA6B;kBAAAa,QAAA,EAAC;gBAAkB;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC,eACN1C,OAAA;gBAAKsC,SAAS,EAAC,WAAW;gBAAAa,QAAA,gBACtBnD,OAAA;kBAAKsC,SAAS,EAAC,gGAAgG;kBAAAa,QAAA,gBAC3GnD,OAAA;oBAAKsC,SAAS,EAAC,yBAAyB;oBAAAa,QAAA,gBACpCnD,OAAA,CAACN,WAAW;sBAAC4C,SAAS,EAAC;oBAAwB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAClD1C,OAAA;sBAAAmD,QAAA,gBACInD,OAAA;wBAAKsC,SAAS,EAAC,8BAA8B;wBAAAa,QAAA,EAAC;sBAAO;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC3D1C,OAAA;wBAAKsC,SAAS,EAAC,wBAAwB;wBAAAa,QAAA,EAAC;sBAAa;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACN1C,OAAA;oBAAKsC,SAAS,EAAC,wCAAwC;oBAAAa,QAAA,EAAC;kBAAG;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC,eACN1C,OAAA;kBAAKsC,SAAS,EAAC,4FAA4F;kBAAAa,QAAA,gBACvGnD,OAAA;oBAAKsC,SAAS,EAAC,yBAAyB;oBAAAa,QAAA,gBACpCnD,OAAA,CAACL,OAAO;sBAAC2C,SAAS,EAAC;oBAAsB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC5C1C,OAAA;sBAAAmD,QAAA,gBACInD,OAAA;wBAAKsC,SAAS,EAAC,4BAA4B;wBAAAa,QAAA,EAAC;sBAAM;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACxD1C,OAAA;wBAAKsC,SAAS,EAAC,sBAAsB;wBAAAa,QAAA,EAAC;sBAAW;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACN1C,OAAA;oBAAKsC,SAAS,EAAC,sCAAsC;oBAAAa,QAAA,EAAC;kBAAE;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACR,EAEAvC,UAAU,KAAK,UAAU,iBACtBH,OAAA;YAAKsC,SAAS,EAAC,2DAA2D;YAAAa,QAAA,gBACtEnD,OAAA;cAAIsC,SAAS,EAAC,sCAAsC;cAAAa,QAAA,EAAC;YAAuB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjF1C,OAAA;cAAKsC,SAAS,EAAC,WAAW;cAAAa,QAAA,EACrBnC,cAAc,CAACK,QAAQ,CAAC6D,GAAG,CAAC,CAACpD,OAAO,EAAE0D,KAAK,kBACxCxF,OAAA,CAACoD,WAAW;gBAER7B,UAAU,EAAEO,OAAO,CAACP,UAAW;gBAC/B8B,KAAK,EAAEvB,OAAO,CAACR,IAAK;gBACpBF,KAAK,EAAEU,OAAO,CAACV,KAAM;gBACrBF,OAAO,EAAEY,OAAO,CAACZ;cAAQ,GAJpBsE,KAAK;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKb,CACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACR,EAEAvC,UAAU,KAAK,SAAS,iBACrBH,OAAA;YAAKsC,SAAS,EAAC,2DAA2D;YAAAa,QAAA,gBACtEnD,OAAA;cAAIsC,SAAS,EAAC,sCAAsC;cAAAa,QAAA,EAAC;YAAqB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/E1C,OAAA;cAAKsC,SAAS,EAAC,uCAAuC;cAAAa,QAAA,EACjDnC,cAAc,CAACQ,OAAO,CAAC0D,GAAG,CAAC,CAAC/C,KAAK,EAAEqD,KAAK,kBACrCxF,OAAA;gBAAiBsC,SAAS,EAAC,8FAA8F;gBAAAa,QAAA,gBACrHnD,OAAA;kBAAKsC,SAAS,EAAC,wCAAwC;kBAAAa,QAAA,gBACnDnD,OAAA;oBAAIsC,SAAS,EAAC,6BAA6B;oBAAAa,QAAA,EAAEhB,KAAK,CAACb;kBAAI;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC7D1C,OAAA,CAACb,KAAK;oBAACmD,SAAS,EAAC;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC,eACN1C,OAAA;kBAAKsC,SAAS,EAAC,WAAW;kBAAAa,QAAA,gBACtBnD,OAAA;oBAAKsC,SAAS,EAAC,kCAAkC;oBAAAa,QAAA,GAAEhB,KAAK,CAACT,aAAa,EAAC,GAAC;kBAAA;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC9E1C,OAAA;oBAAKsC,SAAS,EAAC,uBAAuB;oBAAAa,QAAA,GAAEhB,KAAK,CAACV,QAAQ,EAAC,WAAS;kBAAA;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACtE1C,OAAA;oBAAKsC,SAAS,EAAC,qCAAqC;oBAAAa,QAAA,eAChDnD,OAAA;sBACIsC,SAAS,EAAC,0DAA0D;sBACpEgB,KAAK,EAAE;wBAAEC,KAAK,EAAG,GAAEpB,KAAK,CAACT,aAAc;sBAAG;oBAAE;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1C;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA,GAdA8C,KAAK;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAeV,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACR,EAEAvC,UAAU,KAAK,SAAS,iBACrBH,OAAA;YAAKsC,SAAS,EAAC,2DAA2D;YAAAa,QAAA,gBACtEnD,OAAA;cAAIsC,SAAS,EAAC,sCAAsC;cAAAa,QAAA,EAAC;YAAsB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChF1C,OAAA;cAAKsC,SAAS,EAAC,WAAW;cAAAa,QAAA,EACrBnC,cAAc,CAACW,OAAO,CAACuD,GAAG,CAAC,CAAChD,MAAM,EAAEsD,KAAK,kBACtCxF,OAAA;gBAAiBsC,SAAS,EAAC,+HAA+H;gBAAAa,QAAA,gBACtJnD,OAAA;kBAAKsC,SAAS,EAAC,yBAAyB;kBAAAa,QAAA,gBACpCnD,OAAA;oBAAKsC,SAAS,EAAC,4BAA4B;oBAAAa,QAAA,eACvCnD,OAAA,CAACX,aAAa;sBAACiD,SAAS,EAAC;oBAAoB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CAAC,eACN1C,OAAA;oBAAAmD,QAAA,gBACInD,OAAA;sBAAIsC,SAAS,EAAC,6BAA6B;sBAAAa,QAAA,EAAEjB,MAAM,CAACZ;oBAAI;sBAAAiB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC9D1C,OAAA;sBAAGsC,SAAS,EAAC,uBAAuB;sBAAAa,QAAA,GAAEjB,MAAM,CAACT,QAAQ,EAAC,oBAAkB;oBAAA;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACN1C,OAAA;kBAAAmD,QAAA,eACInD,OAAA;oBAAKsC,SAAS,EAAG,mDAAkDK,kBAAkB,CAACT,MAAM,CAACR,aAAa,CAAE,EAAE;oBAAAyB,QAAA,GACzGjB,MAAM,CAACR,aAAa,EAAC,GAC1B;kBAAA;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA,GAdA8C,KAAK;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAeV,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACR,EAEAvC,UAAU,KAAK,UAAU,iBACtBH,OAAA;YAAKsC,SAAS,EAAC,2DAA2D;YAAAa,QAAA,gBACtEnD,OAAA;cAAIsC,SAAS,EAAC,sCAAsC;cAAAa,QAAA,EAAC;YAAa;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvE1C,OAAA,CAACF,YAAY;cACTgD,KAAK,EAAEjC,YAAa;cACpBuE,QAAQ,EAAEJ,gBAAiB;cAC3B1C,SAAS,EAAC;YAA4B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAGN1C,OAAA;UAAKsC,SAAS,EAAC,2DAA2D;UAAAa,QAAA,gBACtEnD,OAAA;YAAIsC,SAAS,EAAC,8DAA8D;YAAAa,QAAA,gBACxEnD,OAAA,CAACP,KAAK;cAAC6C,SAAS,EAAC;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3C1C,OAAA;cAAAmD,QAAA,EAAM;YAAe;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACL1C,OAAA;YAAKsC,SAAS,EAAC,WAAW;YAAAa,QAAA,EACrBnC,cAAc,CAACY,cAAc,CAACsD,GAAG,CAAC,CAACO,QAAQ,EAAED,KAAK,kBAC/CxF,OAAA;cAAiBsC,SAAS,EAAC,8FAA8F;cAAAa,QAAA,GACpHd,aAAa,CAACoD,QAAQ,CAAC1D,MAAM,CAAC,eAC/B/B,OAAA;gBAAKsC,SAAS,EAAC,QAAQ;gBAAAa,QAAA,gBACnBnD,OAAA;kBAAKsC,SAAS,EAAC,6BAA6B;kBAAAa,QAAA,EAAEsC,QAAQ,CAAC3D;gBAAO;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrE1C,OAAA;kBAAKsC,SAAS,EAAC,uBAAuB;kBAAAa,QAAA,GAAEsC,QAAQ,CAAC5D,IAAI,EAAC,UAAG,EAAC4D,QAAQ,CAACzD,IAAI;gBAAA;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC,eACN1C,OAAA;gBAAKsC,SAAS,EAAG;AACrD,sBAAsBmD,QAAQ,CAAC1D,MAAM,KAAK,SAAS,GAAG,8CAA8C,GACxD0D,QAAQ,CAAC1D,MAAM,KAAK,QAAQ,GAAG,wCAAwC,GACnE,iDACP,EAAE;gBAAAoB,QAAA,EACFsC,QAAQ,CAAC1D;cAAM;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA,GAZA8C,KAAK;cAAAjD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaV,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACxC,EAAA,CA9gBID,cAAc;AAAAyF,EAAA,GAAdzF,cAAc;AAghBpB,eAAeA,cAAc;AAAC,IAAAyF,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
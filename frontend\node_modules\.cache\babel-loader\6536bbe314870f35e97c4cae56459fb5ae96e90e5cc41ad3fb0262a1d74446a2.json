{"ast": null, "code": "/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m10 11 11 .9a1 1 0 0 1 .8 1.1l-.665 4.158a1 1 0 0 1-.988.842H20\",\n  key: \"she1j9\"\n}], [\"path\", {\n  d: \"M16 18h-5\",\n  key: \"bq60fd\"\n}], [\"path\", {\n  d: \"M18 5a1 1 0 0 0-1 1v5.573\",\n  key: \"1kv8ia\"\n}], [\"path\", {\n  d: \"M3 4h8.129a1 1 0 0 1 .99.863L13 11.246\",\n  key: \"1q1ert\"\n}], [\"path\", {\n  d: \"M4 11V4\",\n  key: \"9ft8pt\"\n}], [\"path\", {\n  d: \"M7 15h.01\",\n  key: \"k5ht0j\"\n}], [\"path\", {\n  d: \"M8 10.1V4\",\n  key: \"1j<PERSON><PERSON>\"\n}], [\"circle\", {\n  cx: \"18\",\n  cy: \"18\",\n  r: \"2\",\n  key: \"1emm8v\"\n}], [\"circle\", {\n  cx: \"7\",\n  cy: \"15\",\n  r: \"5\",\n  key: \"ddtuc\"\n}]];\nconst Tractor = createLucideIcon(\"tractor\", __iconNode);\nexport { __iconNode, Tractor as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "r", "Tractor", "createLucideIcon"], "sources": ["D:\\techvritti\\Collegemanagement\\frontend\\node_modules\\lucide-react\\src\\icons\\tractor.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm10 11 11 .9a1 1 0 0 1 .8 1.1l-.665 4.158a1 1 0 0 1-.988.842H20', key: 'she1j9' }],\n  ['path', { d: 'M16 18h-5', key: 'bq60fd' }],\n  ['path', { d: 'M18 5a1 1 0 0 0-1 1v5.573', key: '1kv8ia' }],\n  ['path', { d: 'M3 4h8.129a1 1 0 0 1 .99.863L13 11.246', key: '1q1ert' }],\n  ['path', { d: 'M4 11V4', key: '9ft8pt' }],\n  ['path', { d: 'M7 15h.01', key: 'k5ht0j' }],\n  ['path', { d: 'M8 10.1V4', key: '1jgyzo' }],\n  ['circle', { cx: '18', cy: '18', r: '2', key: '1emm8v' }],\n  ['circle', { cx: '7', cy: '15', r: '5', key: 'ddtuc' }],\n];\n\n/**\n * @component @name Tractor\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTAgMTEgMTEgLjlhMSAxIDAgMCAxIC44IDEuMWwtLjY2NSA0LjE1OGExIDEgMCAwIDEtLjk4OC44NDJIMjAiIC8+CiAgPHBhdGggZD0iTTE2IDE4aC01IiAvPgogIDxwYXRoIGQ9Ik0xOCA1YTEgMSAwIDAgMC0xIDF2NS41NzMiIC8+CiAgPHBhdGggZD0iTTMgNGg4LjEyOWExIDEgMCAwIDEgLjk5Ljg2M0wxMyAxMS4yNDYiIC8+CiAgPHBhdGggZD0iTTQgMTFWNCIgLz4KICA8cGF0aCBkPSJNNyAxNWguMDEiIC8+CiAgPHBhdGggZD0iTTggMTAuMVY0IiAvPgogIDxjaXJjbGUgY3g9IjE4IiBjeT0iMTgiIHI9IjIiIC8+CiAgPGNpcmNsZSBjeD0iNyIgY3k9IjE1IiByPSI1IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/tractor\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Tractor = createLucideIcon('tractor', __iconNode);\n\nexport default Tractor;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAmEC,GAAA,EAAK;AAAA,CAAU,GAChG,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAaC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAA6BC,GAAA,EAAK;AAAA,CAAU,GAC1D,CAAC,QAAQ;EAAED,CAAA,EAAG;EAA0CC,GAAA,EAAK;AAAA,CAAU,GACvE,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAWC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAaC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAaC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,UAAU;EAAEC,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAMC,CAAA,EAAG;EAAKH,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,UAAU;EAAEC,EAAA,EAAI;EAAKC,EAAA,EAAI;EAAMC,CAAA,EAAG;EAAKH,GAAA,EAAK;AAAA,CAAS,EACxD;AAaA,MAAMI,OAAA,GAAUC,gBAAA,CAAiB,WAAWP,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
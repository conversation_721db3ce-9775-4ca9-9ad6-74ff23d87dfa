{"ast": null, "code": "/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 3H4a2 2 0 0 0-2 2v16.286a.71.71 0 0 0 1.212.502l2.202-2.202A2 2 0 0 1 6.828 19H20a2 2 0 0 0 2-2v-4\",\n  key: \"11da1y\"\n}], [\"path\", {\n  d: \"M16 3h6v6\",\n  key: \"1bx56c\"\n}], [\"path\", {\n  d: \"m16 9 6-6\",\n  key: \"m4dnic\"\n}]];\nconst MessageSquareShare = createLucideIcon(\"message-square-share\", __iconNode);\nexport { __iconNode, MessageSquareShare as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "MessageSquareShare", "createLucideIcon"], "sources": ["D:\\techvritti\\Collegemanagement\\frontend\\node_modules\\lucide-react\\src\\icons\\message-square-share.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M12 3H4a2 2 0 0 0-2 2v16.286a.71.71 0 0 0 1.212.502l2.202-2.202A2 2 0 0 1 6.828 19H20a2 2 0 0 0 2-2v-4',\n      key: '11da1y',\n    },\n  ],\n  ['path', { d: 'M16 3h6v6', key: '1bx56c' }],\n  ['path', { d: 'm16 9 6-6', key: 'm4dnic' }],\n];\n\n/**\n * @component @name MessageSquareShare\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgM0g0YTIgMiAwIDAgMC0yIDJ2MTYuMjg2YS43MS43MSAwIDAgMCAxLjIxMi41MDJsMi4yMDItMi4yMDJBMiAyIDAgMCAxIDYuODI4IDE5SDIwYTIgMiAwIDAgMCAyLTJ2LTQiIC8+CiAgPHBhdGggZD0iTTE2IDNoNnY2IiAvPgogIDxwYXRoIGQ9Im0xNiA5IDYtNiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/message-square-share\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MessageSquareShare = createLucideIcon('message-square-share', __iconNode);\n\nexport default MessageSquareShare;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CACE,QACA;EACEC,CAAA,EAAG;EACHC,GAAA,EAAK;AAAA,EAET,EACA,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAaC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAaC,GAAA,EAAK;AAAA,CAAU,EAC5C;AAaA,MAAMC,kBAAA,GAAqBC,gBAAA,CAAiB,wBAAwBJ,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 19h.01\",\n  key: \"1wutuc\"\n}], [\"path\", {\n  d: \"M12 3h.01\",\n  key: \"n36tog\"\n}], [\"path\", {\n  d: \"M16 19h.01\",\n  key: \"1vcnzz\"\n}], [\"path\", {\n  d: \"M16 3h.01\",\n  key: \"ll0zb8\"\n}], [\"path\", {\n  d: \"M2 13h.01\",\n  key: \"1aptou\"\n}], [\"path\", {\n  d: \"M2 17v4.286a.71.71 0 0 0 1.212.502l2.202-2.202A2 2 0 0 1 6.828 19H8\",\n  key: \"4cp7zq\"\n}], [\"path\", {\n  d: \"M2 5a2 2 0 0 1 2-2\",\n  key: \"1iztiu\"\n}], [\"path\", {\n  d: \"M2 9h.01\",\n  key: \"1nzd1v\"\n}], [\"path\", {\n  d: \"M20 3a2 2 0 0 1 2 2\",\n  key: \"m48m3a\"\n}], [\"path\", {\n  d: \"M22 13h.01\",\n  key: \"ke7esy\"\n}], [\"path\", {\n  d: \"M22 17a2 2 0 0 1-2 2\",\n  key: \"17q5fo\"\n}], [\"path\", {\n  d: \"M22 9h.01\",\n  key: \"npkp49\"\n}], [\"path\", {\n  d: \"M8 3h.01\",\n  key: \"133hau\"\n}]];\nconst MessageSquareDashed = createLucideIcon(\"message-square-dashed\", __iconNode);\nexport { __iconNode, MessageSquareDashed as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "MessageSquareDashed", "createLucideIcon"], "sources": ["D:\\techvritti\\Collegemanagement\\frontend\\node_modules\\lucide-react\\src\\icons\\message-square-dashed.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 19h.01', key: '1wutuc' }],\n  ['path', { d: 'M12 3h.01', key: 'n36tog' }],\n  ['path', { d: 'M16 19h.01', key: '1vcnzz' }],\n  ['path', { d: 'M16 3h.01', key: 'll0zb8' }],\n  ['path', { d: 'M2 13h.01', key: '1aptou' }],\n  [\n    'path',\n    { d: 'M2 17v4.286a.71.71 0 0 0 1.212.502l2.202-2.202A2 2 0 0 1 6.828 19H8', key: '4cp7zq' },\n  ],\n  ['path', { d: 'M2 5a2 2 0 0 1 2-2', key: '1iztiu' }],\n  ['path', { d: 'M2 9h.01', key: '1nzd1v' }],\n  ['path', { d: 'M20 3a2 2 0 0 1 2 2', key: 'm48m3a' }],\n  ['path', { d: 'M22 13h.01', key: 'ke7esy' }],\n  ['path', { d: 'M22 17a2 2 0 0 1-2 2', key: '17q5fo' }],\n  ['path', { d: 'M22 9h.01', key: 'npkp49' }],\n  ['path', { d: 'M8 3h.01', key: '133hau' }],\n];\n\n/**\n * @component @name MessageSquareDashed\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMTloLjAxIiAvPgogIDxwYXRoIGQ9Ik0xMiAzaC4wMSIgLz4KICA8cGF0aCBkPSJNMTYgMTloLjAxIiAvPgogIDxwYXRoIGQ9Ik0xNiAzaC4wMSIgLz4KICA8cGF0aCBkPSJNMiAxM2guMDEiIC8+CiAgPHBhdGggZD0iTTIgMTd2NC4yODZhLjcxLjcxIDAgMCAwIDEuMjEyLjUwMmwyLjIwMi0yLjIwMkEyIDIgMCAwIDEgNi44MjggMTlIOCIgLz4KICA8cGF0aCBkPSJNMiA1YTIgMiAwIDAgMSAyLTIiIC8+CiAgPHBhdGggZD0iTTIgOWguMDEiIC8+CiAgPHBhdGggZD0iTTIwIDNhMiAyIDAgMCAxIDIgMiIgLz4KICA8cGF0aCBkPSJNMjIgMTNoLjAxIiAvPgogIDxwYXRoIGQ9Ik0yMiAxN2EyIDIgMCAwIDEtMiAyIiAvPgogIDxwYXRoIGQ9Ik0yMiA5aC4wMSIgLz4KICA8cGF0aCBkPSJNOCAzaC4wMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/message-square-dashed\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MessageSquareDashed = createLucideIcon('message-square-dashed', __iconNode);\n\nexport default MessageSquareDashed;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAcC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAaC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAcC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAaC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAaC,GAAA,EAAK;AAAA,CAAU,GAC1C,CACE,QACA;EAAED,CAAA,EAAG;EAAuEC,GAAA,EAAK;AAAA,EACnF,EACA,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAsBC,GAAA,EAAK;AAAA,CAAU,GACnD,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAYC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAuBC,GAAA,EAAK;AAAA,CAAU,GACpD,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAcC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAwBC,GAAA,EAAK;AAAA,CAAU,GACrD,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAaC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAYC,GAAA,EAAK;AAAA,CAAU,EAC3C;AAaA,MAAMC,mBAAA,GAAsBC,gBAAA,CAAiB,yBAAyBJ,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
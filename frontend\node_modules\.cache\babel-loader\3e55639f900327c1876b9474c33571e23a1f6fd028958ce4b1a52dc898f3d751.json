{"ast": null, "code": "/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"11\",\n  cy: \"11\",\n  r: \"8\",\n  key: \"4ej97u\"\n}], [\"path\", {\n  d: \"m21 21-4.3-4.3\",\n  key: \"1qie3q\"\n}], [\"path\", {\n  d: \"M11 11a2 2 0 0 0 4 0 4 4 0 0 0-8 0 6 6 0 0 0 12 0\",\n  key: \"107gwy\"\n}]];\nconst Lollipop = createLucideIcon(\"lollipop\", __iconNode);\nexport { __iconNode, Lollipop as default };", "map": {"version": 3, "names": ["__iconNode", "cx", "cy", "r", "key", "d", "Lollipop", "createLucideIcon"], "sources": ["D:\\techvritti\\Collegemanagement\\frontend\\node_modules\\lucide-react\\src\\icons\\lollipop.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '11', cy: '11', r: '8', key: '4ej97u' }],\n  ['path', { d: 'm21 21-4.3-4.3', key: '1qie3q' }],\n  ['path', { d: 'M11 11a2 2 0 0 0 4 0 4 4 0 0 0-8 0 6 6 0 0 0 12 0', key: '107gwy' }],\n];\n\n/**\n * @component @name Lollipop\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMSIgY3k9IjExIiByPSI4IiAvPgogIDxwYXRoIGQ9Im0yMSAyMS00LjMtNC4zIiAvPgogIDxwYXRoIGQ9Ik0xMSAxMWEyIDIgMCAwIDAgNCAwIDQgNCAwIDAgMC04IDAgNiA2IDAgMCAwIDEyIDAiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/lollipop\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Lollipop = createLucideIcon('lollipop', __iconNode);\n\nexport default Lollipop;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,UAAU;EAAEC,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAMC,CAAA,EAAG;EAAKC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAkBD,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAqDD,GAAA,EAAK;AAAA,CAAU,EACpF;AAaA,MAAME,QAAA,GAAWC,gBAAA,CAAiB,YAAYP,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
import React, { useState, useEffect } from 'react';
import {
    Calendar, Users, BookOpen, GraduationCap, Filter, Download,
    TrendingUp, Clock, CheckCircle, XCircle, AlertCircle, Info
} from 'lucide-react';
import 'react-calendar/dist/Calendar.css';
import CalendarView from 'react-calendar';

const AttendancePage = () => {
    const [activeView, setActiveView] = useState('overall');
    const [selectedBatch, setSelectedBatch] = useState('all');
    const [selectedCourse, setSelectedCourse] = useState('all');
    const [selectedSubject, setSelectedSubject] = useState('all');
    const [dateRange, setDateRange] = useState('thisMonth');
    const [selectedDate, setSelectedDate] = useState(new Date());
    const [attendanceData] = useState({
        overall: { present: 85, absent: 15, total: 100 },
        subjects: [
            { name: 'Mathematics', present: 18, total: 20, percentage: 90 },
            { name: 'Physics', present: 16, total: 18, percentage: 89 },
            { name: 'Chemistry', present: 14, total: 16, percentage: 88 },
            { name: 'Computer Science', present: 19, total: 22, percentage: 86 },
            { name: 'English', present: 15, total: 18, percentage: 83 }
        ],
        batches: [
            { name: 'Batch A', students: 45, avgAttendance: 87 },
            { name: 'Batch B', students: 42, avgAttendance: 85 },
            { name: 'Batch C', students: 48, avgAttendance: 89 }
        ],
        courses: [
            { name: 'Engineering', students: 135, avgAttendance: 87 },
            { name: 'Science', students: 98, avgAttendance: 85 },
            { name: 'Commerce', students: 76, avgAttendance: 91 }
        ],
        recentActivity: [
            { date: '2024-03-15', subject: 'Mathematics', status: 'present', time: '09:00 AM' },
            { date: '2024-03-15', subject: 'Physics', status: 'present', time: '10:30 AM' },
            { date: '2024-03-14', subject: 'Chemistry', status: 'absent', time: '02:00 PM' },
            { date: '2024-03-14', subject: 'Computer Science', status: 'present', time: '03:30 PM' },
            { date: '2024-03-13', subject: 'English', status: 'present', time: '11:00 AM' }
        ],
        // Enhanced daily attendance records for calendar view
        dailyRecords: [
            // January 2024 - Engineering Course
            { date: '2024-01-15', subject: 'Mathematics', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },
            { date: '2024-01-15', subject: 'Physics', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },
            { date: '2024-01-16', subject: 'Chemistry', status: 'Absent', course: 'Engineering', batch: 'Batch A', type: 'class' },
            { date: '2024-01-16', subject: 'Mathematics', status: 'Present', course: 'Engineering', batch: 'Batch B', type: 'class' },
            { date: '2024-01-17', subject: 'English', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },
            { date: '2024-01-18', subject: 'Computer Science', status: 'Absent', course: 'Engineering', batch: 'Batch A', type: 'class' },
            { date: '2024-01-19', subject: 'Physics', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },
            { date: '2024-01-20', subject: 'Holiday', status: 'Holiday', course: 'all', batch: 'all', type: 'holiday' },
            { date: '2024-01-21', subject: 'Holiday', status: 'Holiday', course: 'all', batch: 'all', type: 'holiday' },
            { date: '2024-01-22', subject: 'Mathematics', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },
            { date: '2024-01-23', subject: 'Chemistry', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },
            { date: '2024-01-24', subject: 'English', status: 'Absent', course: 'Engineering', batch: 'Batch A', type: 'class' },
            { date: '2024-01-25', subject: 'Computer Science', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },
            { date: '2024-01-26', subject: 'Physics', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },
            { date: '2024-01-29', subject: 'Mathematics', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },
            { date: '2024-01-30', subject: 'Chemistry', status: 'Absent', course: 'Engineering', batch: 'Batch A', type: 'class' },
            { date: '2024-01-31', subject: 'English', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },

            // Science course records
            { date: '2024-01-15', subject: 'Biology', status: 'Present', course: 'Science', batch: 'Batch A', type: 'class' },
            { date: '2024-01-16', subject: 'Chemistry', status: 'Present', course: 'Science', batch: 'Batch A', type: 'class' },
            { date: '2024-01-17', subject: 'Physics', status: 'Absent', course: 'Science', batch: 'Batch A', type: 'class' },
            { date: '2024-01-18', subject: 'Mathematics', status: 'Present', course: 'Science', batch: 'Batch A', type: 'class' },
            { date: '2024-01-19', subject: 'Biology', status: 'Present', course: 'Science', batch: 'Batch A', type: 'class' },

            // Commerce course records
            { date: '2024-01-15', subject: 'Accounting', status: 'Present', course: 'Commerce', batch: 'Batch A', type: 'class' },
            { date: '2024-01-16', subject: 'Economics', status: 'Present', course: 'Commerce', batch: 'Batch A', type: 'class' },
            { date: '2024-01-17', subject: 'Business Studies', status: 'Present', course: 'Commerce', batch: 'Batch A', type: 'class' },
            { date: '2024-01-18', subject: 'Accounting', status: 'Absent', course: 'Commerce', batch: 'Batch A', type: 'class' },
            { date: '2024-01-19', subject: 'Economics', status: 'Present', course: 'Commerce', batch: 'Batch A', type: 'class' },
        ]
    });

    const getStatusIcon = (status) => {
        switch (status) {
            case 'present': return <CheckCircle className="w-4 h-4 text-green-500" />;
            case 'absent': return <XCircle className="w-4 h-4 text-red-500" />;
            case 'late': return <AlertCircle className="w-4 h-4 text-yellow-500" />;
            default: return null;
        }
    };

    const getAttendanceColor = (percentage) => {
        if (percentage >= 90) return 'text-green-600 bg-green-50 border-green-200';
        if (percentage >= 75) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
        return 'text-red-600 bg-red-50 border-red-200';
    };

    const StatCard = ({ title, value, subtitle, icon: Icon, trend }) => (
        <div className="bg-gradient-to-br from-white via-blue-50 to-blue-100 rounded-xl p-6 shadow-md border border-blue-100 hover:shadow-lg transition-shadow">
            <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-600 rounded-lg">
                        <Icon className="w-5 h-5 text-white" />
                    </div>
                    <h3 className="text-blue-700 font-semibold">{title}</h3>
                </div>
                {trend && <TrendingUp className="w-4 h-4 text-green-500" />}
            </div>
            <div className="space-y-1">
                <p className="text-3xl font-extrabold text-blue-900">{value}</p>
                <p className="text-sm text-blue-600">{subtitle}</p>
            </div>
        </div>
    );

    const ProgressBar = ({ percentage, label, total, present }) => (
        <div className="space-y-2">
            <div className="flex justify-between items-center">
                <span className="text-sm font-semibold text-gray-700">{label}</span>
                <span className={`text-xs px-2 py-1 rounded-full border ${getAttendanceColor(percentage)}`}>
                    {percentage}%
                </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                    className={`h-2 rounded-full transition-all duration-300 ${percentage >= 90 ? 'bg-green-500' : percentage >= 75 ? 'bg-yellow-500' : 'bg-red-500'}`}
                    style={{ width: `${percentage}%` }}
                ></div>
            </div>
            <div className="flex justify-between text-xs text-gray-500">
                <span>Present: {present}</span>
                <span>Total: {total}</span>
            </div>
        </div>
    );

    // Auto-switch to calendar view when filters are selected
    useEffect(() => {
        if (selectedCourse !== 'all' || selectedBatch !== 'all' || selectedSubject !== 'all') {
            setActiveView('calendar');
        }
    }, [selectedCourse, selectedBatch, selectedSubject]);

    // Filter attendance data based on selected filters
    const getFilteredAttendanceData = () => {
        return attendanceData.dailyRecords.filter(record => {
            const courseMatch = selectedCourse === 'all' || record.course.toLowerCase() === selectedCourse.toLowerCase();
            const batchMatch = selectedBatch === 'all' || record.batch.toLowerCase() === selectedBatch.toLowerCase();
            const subjectMatch = selectedSubject === 'all' || record.subject.toLowerCase() === selectedSubject.toLowerCase();

            return courseMatch && batchMatch && subjectMatch;
        });
    };

    // Get attendance status for a specific date
    const getAttendanceForDate = (date) => {
        const dateStr = date.toISOString().split('T')[0];
        const filteredData = getFilteredAttendanceData();
        const dayRecords = filteredData.filter(record => record.date === dateStr);

        if (dayRecords.length === 0) return null;

        // Check if it's a holiday
        if (dayRecords.some(record => record.type === 'holiday')) {
            return { status: 'holiday', records: dayRecords };
        }

        // Calculate overall status for the day
        const presentCount = dayRecords.filter(record => record.status === 'Present').length;
        const absentCount = dayRecords.filter(record => record.status === 'Absent').length;

        if (presentCount > 0 && absentCount === 0) return { status: 'present', records: dayRecords };
        if (absentCount > 0 && presentCount === 0) return { status: 'absent', records: dayRecords };
        if (presentCount > 0 && absentCount > 0) return { status: 'mixed', records: dayRecords };

        return { status: 'unknown', records: dayRecords };
    };

    // Custom tile content for calendar (small dots)
    const getTileContent = ({ date, view }) => {
        if (view !== 'month') return null;

        const attendance = getAttendanceForDate(date);
        if (!attendance) return null;

        const today = new Date();
        const isFuture = date > today;

        if (isFuture) return null;

        return (
            <div className="flex justify-center mt-1">
                <div className={`w-2 h-2 rounded-full ${
                    attendance.status === 'present' ? 'bg-green-500' :
                    attendance.status === 'absent' ? 'bg-red-500' :
                    attendance.status === 'holiday' ? 'bg-gray-400' :
                    attendance.status === 'mixed' ? 'bg-yellow-500' :
                    'bg-blue-400'
                }`} />
            </div>
        );
    };

    // Custom tile class name for calendar styling
    const getTileClassName = ({ date, view }) => {
        if (view !== 'month') return null;

        const attendance = getAttendanceForDate(date);
        const today = new Date();
        const isFuture = date > today;

        if (isFuture) {
            return 'future-date';
        }

        if (!attendance) return 'no-class';

        switch (attendance.status) {
            case 'present':
                return 'present-day';
            case 'absent':
                return 'absent-day';
            case 'holiday':
                return 'holiday-day';
            case 'mixed':
                return 'mixed-day';
            default:
                return 'no-class';
        }
    };

    const handleDateChange = (date) => {
        setSelectedDate(date);
    };

    return (
        <div className="min-h-screen bg-gradient-to-tr from-blue-50 via-white to-pink-50 p-6">
            <div className="max-w-7xl mx-auto space-y-8">
                {/* Header */}
                <div className="bg-white rounded-2xl p-8 shadow-lg border border-blue-100">
                    <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                        <div>
                            <h1 className="text-4xl font-extrabold text-blue-800 leading-tight">Attendance Dashboard</h1>
                            <p className="text-blue-600 mt-2 text-lg">Monitor attendance by batch, course, and subject.</p>
                        </div>
                        <div className="flex items-center gap-3">
                            <button className="flex items-center gap-2 px-5 py-2 bg-blue-700 text-white rounded-xl shadow hover:bg-blue-800 transition-colors">
                                <Download className="w-4 h-4" />
                                <span>Export</span>
                            </button>
                            <button className="flex items-center gap-2 px-5 py-2 border border-blue-300 text-blue-700 rounded-xl hover:bg-blue-50 transition-colors">
                                <Filter className="w-4 h-4" />
                                <span>Filter</span>
                            </button>
                        </div>
                    </div>
                </div>

                {/* View Toggle */}
                <div className="bg-white rounded-2xl p-2 shadow-sm border border-blue-100">
                    <div className="flex flex-wrap gap-2 justify-center">
                        {[
                            { id: 'overall', label: 'Overall', icon: TrendingUp },
                            { id: 'subjects', label: 'By Subject', icon: BookOpen },
                            { id: 'batches', label: 'By Batch', icon: Users },
                            { id: 'courses', label: 'By Course', icon: GraduationCap },
                            { id: 'calendar', label: 'Calendar View', icon: Calendar }
                        ].map(({ id, label, icon: Icon }) => (
                            <button
                                key={id}
                                onClick={() => setActiveView(id)}
                                className={`flex items-center gap-2 px-5 py-2 rounded-xl font-medium text-base transition-all
                  ${activeView === id
                                        ? 'bg-blue-700 text-white shadow'
                                        : 'text-blue-700 hover:bg-blue-100'
                                    }`}
                            >
                                <Icon className="w-4 h-4" />
                                <span>{label}</span>
                            </button>
                        ))}
                    </div>
                </div>

                {/* Filters */}
                <div className="bg-white rounded-2xl p-6 shadow-md border border-blue-100">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <div>
                            <label className="block text-sm font-semibold text-blue-700 mb-2">Date Range</label>
                            <select
                                value={dateRange}
                                onChange={(e) => setDateRange(e.target.value)}
                                className="w-full px-3 py-2 border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            >
                                <option value="today">Today</option>
                                <option value="thisWeek">This Week</option>
                                <option value="thisMonth">This Month</option>
                                <option value="lastMonth">Last Month</option>
                                <option value="thisYear">This Year</option>
                            </select>
                        </div>
                        <div>
                            <label className="block text-sm font-semibold text-blue-700 mb-2">Batch</label>
                            <select
                                value={selectedBatch}
                                onChange={(e) => {
                                    setSelectedBatch(e.target.value);
                                    // Add smooth transition to calendar view
                                    if (e.target.value !== 'all') {
                                        setTimeout(() => setActiveView('calendar'), 300);
                                    }
                                }}
                                className="w-full px-3 py-2 border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                            >
                                <option value="all">All Batches</option>
                                <option value="Batch A">Batch A</option>
                                <option value="Batch B">Batch B</option>
                                <option value="Batch C">Batch C</option>
                            </select>
                        </div>
                        <div>
                            <label className="block text-sm font-semibold text-blue-700 mb-2">Course</label>
                            <select
                                value={selectedCourse}
                                onChange={(e) => {
                                    setSelectedCourse(e.target.value);
                                    // Add smooth transition to calendar view
                                    if (e.target.value !== 'all') {
                                        setTimeout(() => setActiveView('calendar'), 300);
                                    }
                                }}
                                className="w-full px-3 py-2 border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                            >
                                <option value="all">All Courses</option>
                                <option value="Engineering">Engineering</option>
                                <option value="Science">Science</option>
                                <option value="Commerce">Commerce</option>
                            </select>
                        </div>
                        <div>
                            <label className="block text-sm font-semibold text-blue-700 mb-2">Subject</label>
                            <select
                                value={selectedSubject}
                                onChange={(e) => {
                                    setSelectedSubject(e.target.value);
                                    // Add smooth transition to calendar view
                                    if (e.target.value !== 'all') {
                                        setTimeout(() => setActiveView('calendar'), 300);
                                    }
                                }}
                                className="w-full px-3 py-2 border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                            >
                                <option value="all">All Subjects</option>
                                <option value="Mathematics">Mathematics</option>
                                <option value="Physics">Physics</option>
                                <option value="Chemistry">Chemistry</option>
                                <option value="Computer Science">Computer Science</option>
                                <option value="English">English</option>
                                <option value="Biology">Biology</option>
                                <option value="Accounting">Accounting</option>
                                <option value="Economics">Economics</option>
                                <option value="Business Studies">Business Studies</option>
                            </select>
                        </div>
                    </div>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <StatCard
                        title="Overall Attendance"
                        value="85%"
                        subtitle="This month average"
                        icon={TrendingUp}
                        trend={true}
                    />
                    <StatCard
                        title="Total Classes"
                        value="124"
                        subtitle="Classes conducted"
                        icon={Calendar}
                    />
                    <StatCard
                        title="Present Days"
                        value="105"
                        subtitle="Out of 124 classes"
                        icon={CheckCircle}
                    />
                </div>

                {/* Main Content */}
                <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
                    {/* Left Column - Main Data */}
                    <div className="xl:col-span-2 space-y-8">
                        {activeView === 'overall' && (
                            <div className="bg-white rounded-2xl p-8 shadow-md border border-blue-100">
                                <h3 className="text-xl font-bold text-blue-900 mb-6">Overall Attendance Summary</h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                                    <div className="text-center p-8 bg-gradient-to-tr from-blue-50 via-blue-100 to-white rounded-xl shadow">
                                        <div className="text-5xl font-extrabold text-blue-700 mb-2 drop-shadow">85%</div>
                                        <div className="text-blue-800 font-semibold">Overall Attendance</div>
                                    </div>
                                    <div className="space-y-5">
                                        <div className="flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-xl shadow-sm">
                                            <div className="flex items-center gap-3">
                                                <CheckCircle className="w-8 h-8 text-green-600" />
                                                <div>
                                                    <div className="font-semibold text-green-900">Present</div>
                                                    <div className="text-sm text-green-700">Days attended</div>
                                                </div>
                                            </div>
                                            <div className="text-2xl font-extrabold text-green-600">105</div>
                                        </div>
                                        <div className="flex items-center justify-between p-4 bg-red-50 border border-red-200 rounded-xl shadow-sm">
                                            <div className="flex items-center gap-3">
                                                <XCircle className="w-8 h-8 text-red-600" />
                                                <div>
                                                    <div className="font-semibold text-red-900">Absent</div>
                                                    <div className="text-sm text-red-700">Days missed</div>
                                                </div>
                                            </div>
                                            <div className="text-2xl font-extrabold text-red-600">19</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}

                        {activeView === 'subjects' && (
                            <div className="bg-white rounded-2xl p-8 shadow-md border border-blue-100">
                                <h3 className="text-xl font-bold text-blue-900 mb-6">Subject-wise Attendance</h3>
                                <div className="space-y-6">
                                    {attendanceData.subjects.map((subject, index) => (
                                        <ProgressBar
                                            key={index}
                                            percentage={subject.percentage}
                                            label={subject.name}
                                            total={subject.total}
                                            present={subject.present}
                                        />
                                    ))}
                                </div>
                            </div>
                        )}

                        {activeView === 'batches' && (
                            <div className="bg-white rounded-2xl p-8 shadow-md border border-blue-100">
                                <h3 className="text-xl font-bold text-blue-900 mb-6">Batch-wise Attendance</h3>
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    {attendanceData.batches.map((batch, index) => (
                                        <div key={index} className="p-6 bg-blue-50 rounded-xl border border-blue-200 shadow hover:scale-105 transition-transform">
                                            <div className="flex items-center justify-between mb-3">
                                                <h4 className="font-semibold text-blue-900">{batch.name}</h4>
                                                <Users className="w-5 h-5 text-blue-400" />
                                            </div>
                                            <div className="space-y-2">
                                                <div className="text-2xl font-bold text-blue-900">{batch.avgAttendance}%</div>
                                                <div className="text-sm text-blue-700">{batch.students} students</div>
                                                <div className="w-full bg-blue-200 rounded-full h-2">
                                                    <div
                                                        className="h-2 bg-blue-600 rounded-full transition-all duration-300"
                                                        style={{ width: `${batch.avgAttendance}%` }}
                                                    ></div>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}

                        {activeView === 'courses' && (
                            <div className="bg-white rounded-2xl p-8 shadow-md border border-blue-100">
                                <h3 className="text-xl font-bold text-blue-900 mb-6">Course-wise Attendance</h3>
                                <div className="space-y-6">
                                    {attendanceData.courses.map((course, index) => (
                                        <div key={index} className="flex items-center justify-between p-5 border border-blue-200 rounded-xl bg-blue-50 hover:bg-blue-100 transition-colors shadow">
                                            <div className="flex items-center gap-4">
                                                <div className="p-3 bg-blue-700 rounded-lg">
                                                    <GraduationCap className="w-6 h-6 text-white" />
                                                </div>
                                                <div>
                                                    <h4 className="font-semibold text-blue-900">{course.name}</h4>
                                                    <p className="text-sm text-blue-700">{course.students} students enrolled</p>
                                                </div>
                                            </div>
                                            <div>
                                                <div className={`text-lg font-bold px-4 py-1 rounded-full border ${getAttendanceColor(course.avgAttendance)}`}>
                                                    {course.avgAttendance}%
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}

                        {activeView === 'calendar' && (
                            <div className="space-y-6">
                                {/* Filtered Statistics Summary */}
                                {(selectedCourse !== 'all' || selectedBatch !== 'all' || selectedSubject !== 'all') && (() => {
                                    const filteredData = getFilteredAttendanceData();
                                    const classRecords = filteredData.filter(record => record.type === 'class');
                                    const presentCount = classRecords.filter(record => record.status === 'Present').length;
                                    const absentCount = classRecords.filter(record => record.status === 'Absent').length;
                                    const totalClasses = classRecords.length;
                                    const attendancePercentage = totalClasses > 0 ? Math.round((presentCount / totalClasses) * 100) : 0;

                                    return (
                                        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 shadow-md border border-blue-200">
                                            <h4 className="text-lg font-bold text-blue-900 mb-4">Filtered Attendance Summary</h4>
                                            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                                <div className="text-center p-4 bg-white rounded-xl shadow-sm">
                                                    <div className="text-2xl font-bold text-blue-700">{attendancePercentage}%</div>
                                                    <div className="text-sm text-blue-600">Attendance Rate</div>
                                                </div>
                                                <div className="text-center p-4 bg-white rounded-xl shadow-sm">
                                                    <div className="text-2xl font-bold text-green-600">{presentCount}</div>
                                                    <div className="text-sm text-green-600">Present</div>
                                                </div>
                                                <div className="text-center p-4 bg-white rounded-xl shadow-sm">
                                                    <div className="text-2xl font-bold text-red-600">{absentCount}</div>
                                                    <div className="text-sm text-red-600">Absent</div>
                                                </div>
                                                <div className="text-center p-4 bg-white rounded-xl shadow-sm">
                                                    <div className="text-2xl font-bold text-gray-700">{totalClasses}</div>
                                                    <div className="text-sm text-gray-600">Total Classes</div>
                                                </div>
                                            </div>
                                            <div className="mt-4 text-sm text-blue-600">
                                                Filtered by: {selectedCourse !== 'all' && `Course: ${selectedCourse}`}
                                                {selectedBatch !== 'all' && ` | Batch: ${selectedBatch}`}
                                                {selectedSubject !== 'all' && ` | Subject: ${selectedSubject}`}
                                            </div>
                                        </div>
                                    );
                                })()}

                                <div className="bg-white rounded-2xl p-8 shadow-md border border-blue-100">
                                    <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
                                        <h3 className="text-xl font-bold text-blue-900">Calendar View</h3>
                                        {(selectedCourse === 'all' && selectedBatch === 'all' && selectedSubject === 'all') && (
                                            <div className="text-sm text-blue-600 mt-2 lg:mt-0">
                                                Showing all attendance records
                                            </div>
                                        )}
                                    </div>

                                    {/* Calendar Legend */}
                                    <div className="mb-6 p-4 bg-gray-50 rounded-xl border border-gray-200">
                                        <h4 className="text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                                            <Info className="w-4 h-4" />
                                            Legend
                                        </h4>
                                        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
                                            <div className="flex items-center gap-2">
                                                <div className="w-4 h-4 bg-green-200 border-2 border-green-400 rounded"></div>
                                                <span className="text-gray-700">Present</span>
                                            </div>
                                            <div className="flex items-center gap-2">
                                                <div className="w-4 h-4 bg-red-200 border-2 border-red-400 rounded"></div>
                                                <span className="text-gray-700">Absent</span>
                                            </div>
                                            <div className="flex items-center gap-2">
                                                <div className="w-4 h-4 bg-gray-200 border-2 border-gray-400 rounded"></div>
                                                <span className="text-gray-700">Holiday</span>
                                            </div>
                                            <div className="flex items-center gap-2">
                                                <div className="w-4 h-4 bg-yellow-200 border-2 border-yellow-400 rounded"></div>
                                                <span className="text-gray-700">Mixed</span>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Enhanced Calendar */}
                                    <div className="calendar-container">
                                        <CalendarView
                                            value={selectedDate}
                                            onChange={handleDateChange}
                                            tileContent={getTileContent}
                                            tileClassName={getTileClassName}
                                            className="enhanced-calendar w-full"
                                        />
                                    </div>

                                    {/* Selected Date Details */}
                                    {selectedDate && (() => {
                                        const attendance = getAttendanceForDate(selectedDate);
                                        if (attendance && attendance.records.length > 0) {
                                            return (
                                                <div className="mt-6 p-4 bg-blue-50 rounded-xl border border-blue-200">
                                                    <h4 className="font-semibold text-blue-900 mb-3">
                                                        Details for {selectedDate.toLocaleDateString()}
                                                    </h4>
                                                    <div className="space-y-2">
                                                        {attendance.records.map((record, index) => (
                                                            <div key={index} className="flex items-center justify-between p-2 bg-white rounded-lg border">
                                                                <div className="flex items-center gap-3">
                                                                    {getStatusIcon(record.status.toLowerCase())}
                                                                    <div>
                                                                        <span className="font-medium text-gray-900">{record.subject}</span>
                                                                        <div className="text-sm text-gray-600">
                                                                            {record.course} • {record.batch}
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <span className={`text-xs px-2 py-1 rounded-full capitalize border
                                                                    ${record.status === 'Present' ? 'bg-green-100 text-green-800 border-green-300' :
                                                                    record.status === 'Absent' ? 'bg-red-100 text-red-800 border-red-300' :
                                                                    'bg-gray-100 text-gray-800 border-gray-300'
                                                                }`}>
                                                                    {record.status}
                                                                </span>
                                                            </div>
                                                        ))}
                                                    </div>
                                                </div>
                                            );
                                        }
                                        return null;
                                    })()}
                                </div>
                            </div>
                        )}
                    </div>

                    {/* Right Column - Recent Activity */}
                    <div className="bg-white rounded-2xl p-8 shadow-md border border-blue-100">
                        <h3 className="text-xl font-bold text-blue-900 mb-6 flex items-center gap-2">
                            <Clock className="w-5 h-5 text-blue-700" />
                            <span>Recent Activity</span>
                        </h3>
                        <div className="space-y-5">
                            {attendanceData.recentActivity.map((activity, index) => (
                                <div key={index} className="flex items-center gap-3 p-4 bg-blue-50 rounded-xl border hover:bg-blue-100 transition-colors">
                                    {getStatusIcon(activity.status)}
                                    <div className="flex-1">
                                        <div className="font-semibold text-blue-900">{activity.subject}</div>
                                        <div className="text-sm text-blue-700">{activity.date} • {activity.time}</div>
                                    </div>
                                    <div className={`text-xs px-3 py-1 rounded-full capitalize border
                    ${activity.status === 'present' ? 'bg-green-100 text-green-800 border-green-300' :
                                            activity.status === 'absent' ? 'bg-red-100 text-red-800 border-red-300' :
                                                'bg-yellow-100 text-yellow-800 border-yellow-300'
                                        }`}>
                                        {activity.status}
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AttendancePage;

// Custom CSS styles for enhanced calendar
const calendarStyles = `
.enhanced-calendar {
    width: 100%;
    border: none;
    font-family: inherit;
}

.enhanced-calendar .react-calendar__tile {
    position: relative;
    padding: 0.75em 0.5em;
    background: white;
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease;
}

.enhanced-calendar .react-calendar__tile:hover {
    background-color: #f3f4f6;
}

.enhanced-calendar .react-calendar__tile--active {
    background: #3b82f6 !important;
    color: white;
}

.enhanced-calendar .react-calendar__tile.present-day {
    background-color: #dcfce7;
    border-color: #16a34a;
    color: #15803d;
}

.enhanced-calendar .react-calendar__tile.absent-day {
    background-color: #fecaca;
    border-color: #dc2626;
    color: #b91c1c;
}

.enhanced-calendar .react-calendar__tile.holiday-day {
    background-color: #f3f4f6;
    border-color: #6b7280;
    color: #4b5563;
}

.enhanced-calendar .react-calendar__tile.mixed-day {
    background-color: #fef3c7;
    border-color: #d97706;
    color: #92400e;
}

.enhanced-calendar .react-calendar__tile.future-date {
    background-color: #f9fafb;
    color: #9ca3af;
}

.enhanced-calendar .react-calendar__tile.no-class {
    background-color: #ffffff;
    color: #6b7280;
}

.enhanced-calendar .react-calendar__navigation button {
    background: #3b82f6;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-weight: 500;
}

.enhanced-calendar .react-calendar__navigation button:hover {
    background: #2563eb;
}

.enhanced-calendar .react-calendar__navigation button:disabled {
    background: #9ca3af;
}

.enhanced-calendar .react-calendar__month-view__weekdays {
    background: #f8fafc;
    font-weight: 600;
    color: #475569;
}

.enhanced-calendar .react-calendar__month-view__weekdays__weekday {
    padding: 0.5rem;
    text-align: center;
}
`;

// Inject styles into the document
if (typeof document !== 'undefined') {
    const existingStyle = document.getElementById('calendar-styles');
    if (!existingStyle) {
        const styleElement = document.createElement('style');
        styleElement.id = 'calendar-styles';
        styleElement.textContent = calendarStyles;
        document.head.appendChild(styleElement);
    }
}
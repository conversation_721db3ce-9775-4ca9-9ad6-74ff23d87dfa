import React, { useState, useEffect } from 'react';
import {
    Calendar, Users, BookOpen, GraduationCap, Filter, Download,
    TrendingUp, Clock, CheckCircle, XCircle, AlertCircle, Info
} from 'lucide-react';
import 'react-calendar/dist/Calendar.css';
import CalendarView from 'react-calendar';

const AttendancePage = () => {
    const [activeView, setActiveView] = useState('overall');
    const [selectedBatch, setSelectedBatch] = useState('all');
    const [selectedCourse, setSelectedCourse] = useState('all');
    const [selectedSubject, setSelectedSubject] = useState('all');
    const [dateRange, setDateRange] = useState('thisMonth');
    const [selectedDate, setSelectedDate] = useState(new Date());
    const [attendanceData] = useState({
        overall: { present: 85, absent: 15, total: 100 },
        subjects: [
            { name: 'Mathematics', present: 18, total: 20, percentage: 90 },
            { name: 'Physics', present: 16, total: 18, percentage: 89 },
            { name: 'Chemistry', present: 14, total: 16, percentage: 88 },
            { name: 'Computer Science', present: 19, total: 22, percentage: 86 },
            { name: 'English', present: 15, total: 18, percentage: 83 }
        ],
        batches: [
            { name: 'Batch A', students: 45, avgAttendance: 87 },
            { name: 'Batch B', students: 42, avgAttendance: 85 },
            { name: 'Batch C', students: 48, avgAttendance: 89 }
        ],
        courses: [
            { name: 'Engineering', students: 135, avgAttendance: 87 },
            { name: 'Science', students: 98, avgAttendance: 85 },
            { name: 'Commerce', students: 76, avgAttendance: 91 }
        ],
        recentActivity: [
            { date: '2024-03-15', subject: 'Mathematics', status: 'present', time: '09:00 AM' },
            { date: '2024-03-15', subject: 'Physics', status: 'present', time: '10:30 AM' },
            { date: '2024-03-14', subject: 'Chemistry', status: 'absent', time: '02:00 PM' },
            { date: '2024-03-14', subject: 'Computer Science', status: 'present', time: '03:30 PM' },
            { date: '2024-03-13', subject: 'English', status: 'present', time: '11:00 AM' }
        ],
        // Enhanced daily attendance records for calendar view
        dailyRecords: [
            // January 2024 - Engineering Course
            { date: '2024-01-15', subject: 'Mathematics', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },
            { date: '2024-01-15', subject: 'Physics', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },
            { date: '2024-01-16', subject: 'Chemistry', status: 'Absent', course: 'Engineering', batch: 'Batch A', type: 'class' },
            { date: '2024-01-16', subject: 'Mathematics', status: 'Present', course: 'Engineering', batch: 'Batch B', type: 'class' },
            { date: '2024-01-17', subject: 'English', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },
            { date: '2024-01-18', subject: 'Computer Science', status: 'Absent', course: 'Engineering', batch: 'Batch A', type: 'class' },
            { date: '2024-01-19', subject: 'Physics', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },
            { date: '2024-01-20', subject: 'Holiday', status: 'Holiday', course: 'all', batch: 'all', type: 'holiday' },
            { date: '2024-01-21', subject: 'Holiday', status: 'Holiday', course: 'all', batch: 'all', type: 'holiday' },
            { date: '2024-01-22', subject: 'Mathematics', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },
            { date: '2024-01-23', subject: 'Chemistry', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },
            { date: '2024-01-24', subject: 'English', status: 'Absent', course: 'Engineering', batch: 'Batch A', type: 'class' },
            { date: '2024-01-25', subject: 'Computer Science', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },
            { date: '2024-01-26', subject: 'Physics', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },
            { date: '2024-01-29', subject: 'Mathematics', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },
            { date: '2024-01-30', subject: 'Chemistry', status: 'Absent', course: 'Engineering', batch: 'Batch A', type: 'class' },
            { date: '2024-01-31', subject: 'English', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },

            // Science course records
            { date: '2024-01-15', subject: 'Biology', status: 'Present', course: 'Science', batch: 'Batch A', type: 'class' },
            { date: '2024-01-16', subject: 'Chemistry', status: 'Present', course: 'Science', batch: 'Batch A', type: 'class' },
            { date: '2024-01-17', subject: 'Physics', status: 'Absent', course: 'Science', batch: 'Batch A', type: 'class' },
            { date: '2024-01-18', subject: 'Mathematics', status: 'Present', course: 'Science', batch: 'Batch A', type: 'class' },
            { date: '2024-01-19', subject: 'Biology', status: 'Present', course: 'Science', batch: 'Batch A', type: 'class' },

            // Commerce course records
            { date: '2024-01-15', subject: 'Accounting', status: 'Present', course: 'Commerce', batch: 'Batch A', type: 'class' },
            { date: '2024-01-16', subject: 'Economics', status: 'Present', course: 'Commerce', batch: 'Batch A', type: 'class' },
            { date: '2024-01-17', subject: 'Business Studies', status: 'Present', course: 'Commerce', batch: 'Batch A', type: 'class' },
            { date: '2024-01-18', subject: 'Accounting', status: 'Absent', course: 'Commerce', batch: 'Batch A', type: 'class' },
            { date: '2024-01-19', subject: 'Economics', status: 'Present', course: 'Commerce', batch: 'Batch A', type: 'class' },
        ]
    });

    const getStatusIcon = (status) => {
        switch (status) {
            case 'present': return <CheckCircle className="w-4 h-4 text-green-500" />;
            case 'absent': return <XCircle className="w-4 h-4 text-red-500" />;
            case 'late': return <AlertCircle className="w-4 h-4 text-yellow-500" />;
            default: return null;
        }
    };

    const getAttendanceColor = (percentage) => {
        if (percentage >= 90) return 'text-green-600 bg-green-50 border-green-200';
        if (percentage >= 75) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
        return 'text-red-600 bg-red-50 border-red-200';
    };

    const StatCard = ({ title, value, subtitle, icon: Icon, trend }) => (
        <div className="bg-gradient-to-br from-white via-blue-50 to-blue-100 rounded-xl p-6 shadow-md border border-blue-100 hover:shadow-lg transition-shadow">
            <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-600 rounded-lg">
                        <Icon className="w-5 h-5 text-white" />
                    </div>
                    <h3 className="text-blue-700 font-semibold">{title}</h3>
                </div>
                {trend && <TrendingUp className="w-4 h-4 text-green-500" />}
            </div>
            <div className="space-y-1">
                <p className="text-3xl font-extrabold text-blue-900">{value}</p>
                <p className="text-sm text-blue-600">{subtitle}</p>
            </div>
        </div>
    );

    const ProgressBar = ({ percentage, label, total, present }) => (
        <div className="space-y-2">
            <div className="flex justify-between items-center">
                <span className="text-sm font-semibold text-gray-700">{label}</span>
                <span className={`text-xs px-2 py-1 rounded-full border ${getAttendanceColor(percentage)}`}>
                    {percentage}%
                </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                    className={`h-2 rounded-full transition-all duration-300 ${percentage >= 90 ? 'bg-green-500' : percentage >= 75 ? 'bg-yellow-500' : 'bg-red-500'}`}
                    style={{ width: `${percentage}%` }}
                ></div>
            </div>
            <div className="flex justify-between text-xs text-gray-500">
                <span>Present: {present}</span>
                <span>Total: {total}</span>
            </div>
        </div>
    );

    // Auto-switch to calendar view when filters are selected
    useEffect(() => {
        if (selectedCourse !== 'all' || selectedBatch !== 'all' || selectedSubject !== 'all') {
            setActiveView('calendar');
        }
    }, [selectedCourse, selectedBatch, selectedSubject]);

    // Filter attendance data based on selected filters
    const getFilteredAttendanceData = () => {
        return attendanceData.dailyRecords.filter(record => {
            const courseMatch = selectedCourse === 'all' || record.course.toLowerCase() === selectedCourse.toLowerCase();
            const batchMatch = selectedBatch === 'all' || record.batch.toLowerCase() === selectedBatch.toLowerCase();
            const subjectMatch = selectedSubject === 'all' || record.subject.toLowerCase() === selectedSubject.toLowerCase();

            return courseMatch && batchMatch && subjectMatch;
        });
    };

    // Get attendance status for a specific date
    const getAttendanceForDate = (date) => {
        const dateStr = date.toISOString().split('T')[0];
        const filteredData = getFilteredAttendanceData();
        const dayRecords = filteredData.filter(record => record.date === dateStr);

        if (dayRecords.length === 0) return null;

        // Check if it's a holiday
        if (dayRecords.some(record => record.type === 'holiday')) {
            return { status: 'holiday', records: dayRecords };
        }

        // Calculate overall status for the day
        const presentCount = dayRecords.filter(record => record.status === 'Present').length;
        const absentCount = dayRecords.filter(record => record.status === 'Absent').length;

        if (presentCount > 0 && absentCount === 0) return { status: 'present', records: dayRecords };
        if (absentCount > 0 && presentCount === 0) return { status: 'absent', records: dayRecords };
        if (presentCount > 0 && absentCount > 0) return { status: 'mixed', records: dayRecords };

        return { status: 'unknown', records: dayRecords };
    };

    // Custom tile content for calendar (small dots)
    const getTileContent = ({ date, view }) => {
        if (view !== 'month') return null;

        const attendance = getAttendanceForDate(date);
        if (!attendance) return null;

        const today = new Date();
        const isFuture = date > today;

        if (isFuture) return null;

        return (
            <div className="flex justify-center mt-1">
                <div className={`w-2 h-2 rounded-full ${
                    attendance.status === 'present' ? 'bg-green-500' :
                    attendance.status === 'absent' ? 'bg-red-500' :
                    attendance.status === 'holiday' ? 'bg-gray-400' :
                    attendance.status === 'mixed' ? 'bg-yellow-500' :
                    'bg-blue-400'
                }`} />
            </div>
        );
    };

    // Custom tile class name for calendar styling
    const getTileClassName = ({ date, view }) => {
        if (view !== 'month') return null;

        const attendance = getAttendanceForDate(date);
        const today = new Date();
        const isFuture = date > today;

        if (isFuture) {
            return 'future-date';
        }

        if (!attendance) return 'no-class';

        switch (attendance.status) {
            case 'present':
                return 'present-day';
            case 'absent':
                return 'absent-day';
            case 'holiday':
                return 'holiday-day';
            case 'mixed':
                return 'mixed-day';
            default:
                return 'no-class';
        }
    };

    const handleDateChange = (date) => {
        setSelectedDate(date);

        // Add a subtle animation effect when date is selected
        const calendarElement = document.querySelector('.enhanced-calendar');
        if (calendarElement) {
            calendarElement.style.transform = 'scale(0.98)';
            setTimeout(() => {
                calendarElement.style.transform = 'scale(1)';
            }, 150);
        }
    };

    return (
        <div className="min-h-screen bg-gradient-to-tr from-blue-50 via-white to-pink-50 p-6">
            <div className="max-w-7xl mx-auto space-y-8">
                {/* Header */}
                <div className="bg-white rounded-2xl p-8 shadow-lg border border-blue-100">
                    <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                        <div>
                            <h1 className="text-4xl font-extrabold text-blue-800 leading-tight">Attendance Dashboard</h1>
                            <p className="text-blue-600 mt-2 text-lg">Monitor attendance by batch, course, and subject.</p>
                        </div>
                        <div className="flex items-center gap-3">
                            <button className="flex items-center gap-2 px-5 py-2 bg-blue-700 text-white rounded-xl shadow hover:bg-blue-800 transition-colors">
                                <Download className="w-4 h-4" />
                                <span>Export</span>
                            </button>
                            <button className="flex items-center gap-2 px-5 py-2 border border-blue-300 text-blue-700 rounded-xl hover:bg-blue-50 transition-colors">
                                <Filter className="w-4 h-4" />
                                <span>Filter</span>
                            </button>
                        </div>
                    </div>
                </div>

                {/* View Toggle */}
                <div className="bg-white rounded-2xl p-2 shadow-sm border border-blue-100">
                    <div className="flex flex-wrap gap-2 justify-center">
                        {[
                            { id: 'overall', label: 'Overall', icon: TrendingUp },
                            { id: 'subjects', label: 'By Subject', icon: BookOpen },
                            { id: 'batches', label: 'By Batch', icon: Users },
                            { id: 'courses', label: 'By Course', icon: GraduationCap },
                            { id: 'calendar', label: 'Calendar View', icon: Calendar }
                        ].map(({ id, label, icon: Icon }) => (
                            <button
                                key={id}
                                onClick={() => setActiveView(id)}
                                className={`flex items-center gap-2 px-5 py-2 rounded-xl font-medium text-base transition-all
                  ${activeView === id
                                        ? 'bg-blue-700 text-white shadow'
                                        : 'text-blue-700 hover:bg-blue-100'
                                    }`}
                            >
                                <Icon className="w-4 h-4" />
                                <span>{label}</span>
                            </button>
                        ))}
                    </div>
                </div>

                {/* Filters */}
                <div className="bg-white rounded-2xl p-6 shadow-md border border-blue-100">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <div>
                            <label className="block text-sm font-semibold text-blue-700 mb-2">Date Range</label>
                            <select
                                value={dateRange}
                                onChange={(e) => setDateRange(e.target.value)}
                                className="w-full px-3 py-2 border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            >
                                <option value="today">Today</option>
                                <option value="thisWeek">This Week</option>
                                <option value="thisMonth">This Month</option>
                                <option value="lastMonth">Last Month</option>
                                <option value="thisYear">This Year</option>
                            </select>
                        </div>
                        <div>
                            <label className="block text-sm font-semibold text-blue-700 mb-2">Batch</label>
                            <select
                                value={selectedBatch}
                                onChange={(e) => {
                                    setSelectedBatch(e.target.value);
                                    // Add smooth transition to calendar view
                                    if (e.target.value !== 'all') {
                                        setTimeout(() => setActiveView('calendar'), 300);
                                    }
                                }}
                                className="w-full px-3 py-2 border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                            >
                                <option value="all">All Batches</option>
                                <option value="Batch A">Batch A</option>
                                <option value="Batch B">Batch B</option>
                                <option value="Batch C">Batch C</option>
                            </select>
                        </div>
                        <div>
                            <label className="block text-sm font-semibold text-blue-700 mb-2">Course</label>
                            <select
                                value={selectedCourse}
                                onChange={(e) => {
                                    setSelectedCourse(e.target.value);
                                    // Add smooth transition to calendar view
                                    if (e.target.value !== 'all') {
                                        setTimeout(() => setActiveView('calendar'), 300);
                                    }
                                }}
                                className="w-full px-3 py-2 border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                            >
                                <option value="all">All Courses</option>
                                <option value="Engineering">Engineering</option>
                                <option value="Science">Science</option>
                                <option value="Commerce">Commerce</option>
                            </select>
                        </div>
                        <div>
                            <label className="block text-sm font-semibold text-blue-700 mb-2">Subject</label>
                            <select
                                value={selectedSubject}
                                onChange={(e) => {
                                    setSelectedSubject(e.target.value);
                                    // Add smooth transition to calendar view
                                    if (e.target.value !== 'all') {
                                        setTimeout(() => setActiveView('calendar'), 300);
                                    }
                                }}
                                className="w-full px-3 py-2 border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                            >
                                <option value="all">All Subjects</option>
                                <option value="Mathematics">Mathematics</option>
                                <option value="Physics">Physics</option>
                                <option value="Chemistry">Chemistry</option>
                                <option value="Computer Science">Computer Science</option>
                                <option value="English">English</option>
                                <option value="Biology">Biology</option>
                                <option value="Accounting">Accounting</option>
                                <option value="Economics">Economics</option>
                                <option value="Business Studies">Business Studies</option>
                            </select>
                        </div>
                    </div>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <StatCard
                        title="Overall Attendance"
                        value="85%"
                        subtitle="This month average"
                        icon={TrendingUp}
                        trend={true}
                    />
                    <StatCard
                        title="Total Classes"
                        value="124"
                        subtitle="Classes conducted"
                        icon={Calendar}
                    />
                    <StatCard
                        title="Present Days"
                        value="105"
                        subtitle="Out of 124 classes"
                        icon={CheckCircle}
                    />
                </div>

                {/* Main Content */}
                <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
                    {/* Left Column - Main Data */}
                    <div className="xl:col-span-2 space-y-8">
                        {activeView === 'overall' && (
                            <div className="bg-white rounded-2xl p-8 shadow-md border border-blue-100">
                                <h3 className="text-xl font-bold text-blue-900 mb-6">Overall Attendance Summary</h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                                    <div className="text-center p-8 bg-gradient-to-tr from-blue-50 via-blue-100 to-white rounded-xl shadow">
                                        <div className="text-5xl font-extrabold text-blue-700 mb-2 drop-shadow">85%</div>
                                        <div className="text-blue-800 font-semibold">Overall Attendance</div>
                                    </div>
                                    <div className="space-y-5">
                                        <div className="flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-xl shadow-sm">
                                            <div className="flex items-center gap-3">
                                                <CheckCircle className="w-8 h-8 text-green-600" />
                                                <div>
                                                    <div className="font-semibold text-green-900">Present</div>
                                                    <div className="text-sm text-green-700">Days attended</div>
                                                </div>
                                            </div>
                                            <div className="text-2xl font-extrabold text-green-600">105</div>
                                        </div>
                                        <div className="flex items-center justify-between p-4 bg-red-50 border border-red-200 rounded-xl shadow-sm">
                                            <div className="flex items-center gap-3">
                                                <XCircle className="w-8 h-8 text-red-600" />
                                                <div>
                                                    <div className="font-semibold text-red-900">Absent</div>
                                                    <div className="text-sm text-red-700">Days missed</div>
                                                </div>
                                            </div>
                                            <div className="text-2xl font-extrabold text-red-600">19</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}

                        {activeView === 'subjects' && (
                            <div className="bg-white rounded-2xl p-8 shadow-md border border-blue-100">
                                <h3 className="text-xl font-bold text-blue-900 mb-6">Subject-wise Attendance</h3>
                                <div className="space-y-6">
                                    {attendanceData.subjects.map((subject, index) => (
                                        <ProgressBar
                                            key={index}
                                            percentage={subject.percentage}
                                            label={subject.name}
                                            total={subject.total}
                                            present={subject.present}
                                        />
                                    ))}
                                </div>
                            </div>
                        )}

                        {activeView === 'batches' && (
                            <div className="bg-white rounded-2xl p-8 shadow-md border border-blue-100">
                                <h3 className="text-xl font-bold text-blue-900 mb-6">Batch-wise Attendance</h3>
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    {attendanceData.batches.map((batch, index) => (
                                        <div key={index} className="p-6 bg-blue-50 rounded-xl border border-blue-200 shadow hover:scale-105 transition-transform">
                                            <div className="flex items-center justify-between mb-3">
                                                <h4 className="font-semibold text-blue-900">{batch.name}</h4>
                                                <Users className="w-5 h-5 text-blue-400" />
                                            </div>
                                            <div className="space-y-2">
                                                <div className="text-2xl font-bold text-blue-900">{batch.avgAttendance}%</div>
                                                <div className="text-sm text-blue-700">{batch.students} students</div>
                                                <div className="w-full bg-blue-200 rounded-full h-2">
                                                    <div
                                                        className="h-2 bg-blue-600 rounded-full transition-all duration-300"
                                                        style={{ width: `${batch.avgAttendance}%` }}
                                                    ></div>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}

                        {activeView === 'courses' && (
                            <div className="bg-white rounded-2xl p-8 shadow-md border border-blue-100">
                                <h3 className="text-xl font-bold text-blue-900 mb-6">Course-wise Attendance</h3>
                                <div className="space-y-6">
                                    {attendanceData.courses.map((course, index) => (
                                        <div key={index} className="flex items-center justify-between p-5 border border-blue-200 rounded-xl bg-blue-50 hover:bg-blue-100 transition-colors shadow">
                                            <div className="flex items-center gap-4">
                                                <div className="p-3 bg-blue-700 rounded-lg">
                                                    <GraduationCap className="w-6 h-6 text-white" />
                                                </div>
                                                <div>
                                                    <h4 className="font-semibold text-blue-900">{course.name}</h4>
                                                    <p className="text-sm text-blue-700">{course.students} students enrolled</p>
                                                </div>
                                            </div>
                                            <div>
                                                <div className={`text-lg font-bold px-4 py-1 rounded-full border ${getAttendanceColor(course.avgAttendance)}`}>
                                                    {course.avgAttendance}%
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}

                        {activeView === 'calendar' && (
                            <div className="space-y-8">
                                {/* Enhanced Filtered Statistics Summary */}
                                {(selectedCourse !== 'all' || selectedBatch !== 'all' || selectedSubject !== 'all') && (() => {
                                    const filteredData = getFilteredAttendanceData();
                                    const classRecords = filteredData.filter(record => record.type === 'class');
                                    const presentCount = classRecords.filter(record => record.status === 'Present').length;
                                    const absentCount = classRecords.filter(record => record.status === 'Absent').length;
                                    const totalClasses = classRecords.length;
                                    const attendancePercentage = totalClasses > 0 ? Math.round((presentCount / totalClasses) * 100) : 0;

                                    return (
                                        <div className="bg-gradient-to-br from-indigo-50 via-blue-50 to-purple-50 rounded-3xl p-8 shadow-xl border border-indigo-100 backdrop-blur-sm">
                                            <div className="flex items-center justify-between mb-6">
                                                <div>
                                                    <h4 className="text-2xl font-bold text-indigo-900 mb-2">Filtered Attendance Summary</h4>
                                                    <div className="flex flex-wrap gap-2 text-sm">
                                                        {selectedCourse !== 'all' && (
                                                            <span className="px-3 py-1 bg-indigo-100 text-indigo-700 rounded-full border border-indigo-200">
                                                                Course: {selectedCourse}
                                                            </span>
                                                        )}
                                                        {selectedBatch !== 'all' && (
                                                            <span className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full border border-blue-200">
                                                                Batch: {selectedBatch}
                                                            </span>
                                                        )}
                                                        {selectedSubject !== 'all' && (
                                                            <span className="px-3 py-1 bg-purple-100 text-purple-700 rounded-full border border-purple-200">
                                                                Subject: {selectedSubject}
                                                            </span>
                                                        )}
                                                    </div>
                                                </div>
                                                <div className="hidden md:block">
                                                    <div className="w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                                                        <TrendingUp className="w-8 h-8 text-white" />
                                                    </div>
                                                </div>
                                            </div>

                                            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                                                <div className="group relative overflow-hidden bg-gradient-to-br from-white to-indigo-50 rounded-2xl p-6 shadow-lg border border-indigo-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                                                    <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-indigo-400 to-indigo-600 rounded-bl-3xl opacity-10"></div>
                                                    <div className="relative">
                                                        <div className="flex items-center justify-between mb-3">
                                                            <div className="p-2 bg-indigo-100 rounded-xl">
                                                                <TrendingUp className="w-5 h-5 text-indigo-600" />
                                                            </div>
                                                            <div className={`text-xs px-2 py-1 rounded-full ${
                                                                attendancePercentage >= 90 ? 'bg-green-100 text-green-700' :
                                                                attendancePercentage >= 75 ? 'bg-yellow-100 text-yellow-700' :
                                                                'bg-red-100 text-red-700'
                                                            }`}>
                                                                {attendancePercentage >= 90 ? 'Excellent' : attendancePercentage >= 75 ? 'Good' : 'Needs Improvement'}
                                                            </div>
                                                        </div>
                                                        <div className="text-3xl font-bold text-indigo-700 mb-1">{attendancePercentage}%</div>
                                                        <div className="text-sm text-indigo-600 font-medium">Attendance Rate</div>
                                                    </div>
                                                </div>

                                                <div className="group relative overflow-hidden bg-gradient-to-br from-white to-green-50 rounded-2xl p-6 shadow-lg border border-green-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                                                    <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-bl-3xl opacity-10"></div>
                                                    <div className="relative">
                                                        <div className="flex items-center justify-between mb-3">
                                                            <div className="p-2 bg-green-100 rounded-xl">
                                                                <CheckCircle className="w-5 h-5 text-green-600" />
                                                            </div>
                                                            <div className="text-xs px-2 py-1 bg-green-100 text-green-700 rounded-full">
                                                                {totalClasses > 0 ? Math.round((presentCount / totalClasses) * 100) : 0}%
                                                            </div>
                                                        </div>
                                                        <div className="text-3xl font-bold text-green-700 mb-1">{presentCount}</div>
                                                        <div className="text-sm text-green-600 font-medium">Present Days</div>
                                                    </div>
                                                </div>

                                                <div className="group relative overflow-hidden bg-gradient-to-br from-white to-red-50 rounded-2xl p-6 shadow-lg border border-red-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                                                    <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-red-400 to-red-600 rounded-bl-3xl opacity-10"></div>
                                                    <div className="relative">
                                                        <div className="flex items-center justify-between mb-3">
                                                            <div className="p-2 bg-red-100 rounded-xl">
                                                                <XCircle className="w-5 h-5 text-red-600" />
                                                            </div>
                                                            <div className="text-xs px-2 py-1 bg-red-100 text-red-700 rounded-full">
                                                                {totalClasses > 0 ? Math.round((absentCount / totalClasses) * 100) : 0}%
                                                            </div>
                                                        </div>
                                                        <div className="text-3xl font-bold text-red-700 mb-1">{absentCount}</div>
                                                        <div className="text-sm text-red-600 font-medium">Absent Days</div>
                                                    </div>
                                                </div>

                                                <div className="group relative overflow-hidden bg-gradient-to-br from-white to-gray-50 rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                                                    <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-gray-400 to-gray-600 rounded-bl-3xl opacity-10"></div>
                                                    <div className="relative">
                                                        <div className="flex items-center justify-between mb-3">
                                                            <div className="p-2 bg-gray-100 rounded-xl">
                                                                <Calendar className="w-5 h-5 text-gray-600" />
                                                            </div>
                                                            <div className="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded-full">
                                                                100%
                                                            </div>
                                                        </div>
                                                        <div className="text-3xl font-bold text-gray-700 mb-1">{totalClasses}</div>
                                                        <div className="text-sm text-gray-600 font-medium">Total Classes</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    );
                                })()}

                                <div className="bg-gradient-to-br from-white via-blue-50/30 to-indigo-50/50 rounded-3xl p-8 shadow-2xl border border-blue-100/50 backdrop-blur-sm">
                                    {/* Header Section */}
                                    <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
                                        <div className="flex items-center gap-4">
                                            <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl shadow-lg">
                                                <Calendar className="w-7 h-7 text-white" />
                                            </div>
                                            <div>
                                                <h3 className="text-2xl font-bold text-blue-900">Interactive Calendar</h3>
                                                <p className="text-blue-600 text-sm mt-1">
                                                    {(selectedCourse === 'all' && selectedBatch === 'all' && selectedSubject === 'all')
                                                        ? 'Showing all attendance records'
                                                        : 'Filtered attendance view'}
                                                </p>
                                            </div>
                                        </div>

                                        {/* Quick Stats */}
                                        <div className="flex items-center gap-4 mt-4 lg:mt-0">
                                            <div className="text-center">
                                                <div className="text-lg font-bold text-blue-700">{new Date().toLocaleDateString('en-US', { month: 'short' })}</div>
                                                <div className="text-xs text-blue-600">Current Month</div>
                                            </div>
                                            <div className="w-px h-8 bg-blue-200"></div>
                                            <div className="text-center">
                                                <div className="text-lg font-bold text-blue-700">{selectedDate.getDate()}</div>
                                                <div className="text-xs text-blue-600">Selected</div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Enhanced Calendar Legend */}
                                    <div className="mb-8 p-6 bg-gradient-to-r from-slate-50 to-blue-50 rounded-2xl border border-slate-200 shadow-inner">
                                        <div className="flex items-center justify-between mb-4">
                                            <h4 className="text-lg font-bold text-slate-700 flex items-center gap-2">
                                                <Info className="w-5 h-5 text-blue-500" />
                                                Calendar Legend
                                            </h4>
                                            <div className="text-xs text-slate-500 bg-white px-3 py-1 rounded-full border">
                                                Click dates for details
                                            </div>
                                        </div>
                                        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
                                            <div className="flex items-center gap-3 p-3 bg-white rounded-xl shadow-sm border border-green-100 hover:shadow-md transition-shadow">
                                                <div className="w-6 h-6 bg-gradient-to-br from-green-200 to-green-300 border-2 border-green-400 rounded-lg shadow-sm"></div>
                                                <div>
                                                    <span className="font-semibold text-green-700">Present</span>
                                                    <div className="text-xs text-green-600">Attended class</div>
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-3 p-3 bg-white rounded-xl shadow-sm border border-red-100 hover:shadow-md transition-shadow">
                                                <div className="w-6 h-6 bg-gradient-to-br from-red-200 to-red-300 border-2 border-red-400 rounded-lg shadow-sm"></div>
                                                <div>
                                                    <span className="font-semibold text-red-700">Absent</span>
                                                    <div className="text-xs text-red-600">Missed class</div>
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-3 p-3 bg-white rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
                                                <div className="w-6 h-6 bg-gradient-to-br from-gray-200 to-gray-300 border-2 border-gray-400 rounded-lg shadow-sm"></div>
                                                <div>
                                                    <span className="font-semibold text-gray-700">Holiday</span>
                                                    <div className="text-xs text-gray-600">No classes</div>
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-3 p-3 bg-white rounded-xl shadow-sm border border-yellow-100 hover:shadow-md transition-shadow">
                                                <div className="w-6 h-6 bg-gradient-to-br from-yellow-200 to-yellow-300 border-2 border-yellow-400 rounded-lg shadow-sm"></div>
                                                <div>
                                                    <span className="font-semibold text-yellow-700">Mixed</span>
                                                    <div className="text-xs text-yellow-600">Partial attendance</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Revolutionary Calendar Container */}
                                    <div className="calendar-container relative">
                                        {/* Floating Background Elements */}
                                        <div className="absolute -top-4 -left-4 w-32 h-32 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-full blur-2xl"></div>
                                        <div className="absolute -bottom-4 -right-4 w-40 h-40 bg-gradient-to-br from-blue-400/20 to-cyan-400/20 rounded-full blur-2xl"></div>
                                        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-br from-indigo-300/10 to-purple-300/10 rounded-full blur-3xl -z-10"></div>

                                        {/* Main Calendar Card */}
                                        <div className="relative bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 overflow-hidden">
                                            {/* Decorative Header Strip */}
                                            <div className="h-2 bg-gradient-to-r from-purple-500 via-blue-500 via-cyan-500 to-green-500"></div>

                                            {/* Calendar Content */}
                                            <div className="p-6">
                                                <CalendarView
                                                    value={selectedDate}
                                                    onChange={handleDateChange}
                                                    tileContent={getTileContent}
                                                    tileClassName={getTileClassName}
                                                    className="futuristic-calendar w-full"
                                                />
                                            </div>

                                            {/* Bottom Accent */}
                                            <div className="h-1 bg-gradient-to-r from-transparent via-blue-400/50 to-transparent"></div>
                                        </div>

                                        {/* Floating Action Buttons */}
                                        <div className="absolute -right-4 top-1/2 transform -translate-y-1/2 space-y-3">
                                            <button className="group w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 flex items-center justify-center">
                                                <Calendar className="w-5 h-5 text-white group-hover:rotate-12 transition-transform" />
                                            </button>
                                            <button className="group w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 flex items-center justify-center">
                                                <TrendingUp className="w-5 h-5 text-white group-hover:rotate-12 transition-transform" />
                                            </button>
                                            <button className="group w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 flex items-center justify-center">
                                                <Download className="w-5 h-5 text-white group-hover:rotate-12 transition-transform" />
                                            </button>
                                        </div>

                                        {/* Interactive Status Indicators */}
                                        <div className="mt-8 grid grid-cols-2 lg:grid-cols-4 gap-4">
                                            <div className="group relative overflow-hidden bg-gradient-to-br from-green-50 to-emerald-100 rounded-2xl p-4 border border-green-200 hover:shadow-lg transition-all duration-300 cursor-pointer">
                                                <div className="absolute top-0 right-0 w-8 h-8 bg-green-400/20 rounded-bl-2xl"></div>
                                                <div className="flex items-center gap-3">
                                                    <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                                                    <span className="text-green-700 font-medium">Present Days</span>
                                                </div>
                                                <div className="mt-2 text-xs text-green-600">Click to highlight</div>
                                            </div>

                                            <div className="group relative overflow-hidden bg-gradient-to-br from-red-50 to-rose-100 rounded-2xl p-4 border border-red-200 hover:shadow-lg transition-all duration-300 cursor-pointer">
                                                <div className="absolute top-0 right-0 w-8 h-8 bg-red-400/20 rounded-bl-2xl"></div>
                                                <div className="flex items-center gap-3">
                                                    <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                                                    <span className="text-red-700 font-medium">Absent Days</span>
                                                </div>
                                                <div className="mt-2 text-xs text-red-600">Click to highlight</div>
                                            </div>

                                            <div className="group relative overflow-hidden bg-gradient-to-br from-gray-50 to-slate-100 rounded-2xl p-4 border border-gray-200 hover:shadow-lg transition-all duration-300 cursor-pointer">
                                                <div className="absolute top-0 right-0 w-8 h-8 bg-gray-400/20 rounded-bl-2xl"></div>
                                                <div className="flex items-center gap-3">
                                                    <div className="w-3 h-3 bg-gray-500 rounded-full animate-pulse"></div>
                                                    <span className="text-gray-700 font-medium">Holidays</span>
                                                </div>
                                                <div className="mt-2 text-xs text-gray-600">Click to highlight</div>
                                            </div>

                                            <div className="group relative overflow-hidden bg-gradient-to-br from-yellow-50 to-amber-100 rounded-2xl p-4 border border-yellow-200 hover:shadow-lg transition-all duration-300 cursor-pointer">
                                                <div className="absolute top-0 right-0 w-8 h-8 bg-yellow-400/20 rounded-bl-2xl"></div>
                                                <div className="flex items-center gap-3">
                                                    <div className="w-3 h-3 bg-yellow-500 rounded-full animate-pulse"></div>
                                                    <span className="text-yellow-700 font-medium">Mixed Days</span>
                                                </div>
                                                <div className="mt-2 text-xs text-yellow-600">Click to highlight</div>
                                            </div>
                                        </div>

                                        {/* Animated Progress Ring */}
                                        <div className="absolute -left-8 top-8 w-16 h-16">
                                            <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 64 64">
                                                <circle cx="32" cy="32" r="28" fill="none" stroke="#e5e7eb" strokeWidth="4"/>
                                                <circle
                                                    cx="32"
                                                    cy="32"
                                                    r="28"
                                                    fill="none"
                                                    stroke="url(#gradient)"
                                                    strokeWidth="4"
                                                    strokeDasharray="175.93"
                                                    strokeDashoffset="35"
                                                    className="animate-pulse"
                                                />
                                                <defs>
                                                    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                                        <stop offset="0%" stopColor="#3b82f6"/>
                                                        <stop offset="100%" stopColor="#8b5cf6"/>
                                                    </linearGradient>
                                                </defs>
                                            </svg>
                                            <div className="absolute inset-0 flex items-center justify-center">
                                                <span className="text-xs font-bold text-blue-600">80%</span>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Enhanced Selected Date Details */}
                                    {selectedDate && (() => {
                                        const attendance = getAttendanceForDate(selectedDate);
                                        const today = new Date();
                                        const isToday = selectedDate.toDateString() === today.toDateString();
                                        const isFuture = selectedDate > today;

                                        return (
                                            <div className="mt-8 space-y-4">
                                                {/* Date Header */}
                                                <div className="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl p-6 text-white shadow-xl">
                                                    <div className="flex items-center justify-between">
                                                        <div>
                                                            <h4 className="text-2xl font-bold mb-1">
                                                                {selectedDate.toLocaleDateString('en-US', {
                                                                    weekday: 'long',
                                                                    year: 'numeric',
                                                                    month: 'long',
                                                                    day: 'numeric'
                                                                })}
                                                            </h4>
                                                            <div className="flex items-center gap-2 text-indigo-100">
                                                                {isToday && (
                                                                    <span className="px-2 py-1 bg-white/20 rounded-full text-xs font-medium">
                                                                        Today
                                                                    </span>
                                                                )}
                                                                {isFuture && (
                                                                    <span className="px-2 py-1 bg-white/20 rounded-full text-xs font-medium">
                                                                        Future Date
                                                                    </span>
                                                                )}
                                                                {attendance && (
                                                                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                                                        attendance.status === 'present' ? 'bg-green-400/30 text-green-100' :
                                                                        attendance.status === 'absent' ? 'bg-red-400/30 text-red-100' :
                                                                        attendance.status === 'holiday' ? 'bg-gray-400/30 text-gray-100' :
                                                                        'bg-yellow-400/30 text-yellow-100'
                                                                    }`}>
                                                                        {attendance.status === 'present' ? 'Present Day' :
                                                                         attendance.status === 'absent' ? 'Absent Day' :
                                                                         attendance.status === 'holiday' ? 'Holiday' :
                                                                         'Mixed Attendance'}
                                                                    </span>
                                                                )}
                                                            </div>
                                                        </div>
                                                        <div className="text-right">
                                                            <div className="text-3xl font-bold">{selectedDate.getDate()}</div>
                                                            <div className="text-sm text-indigo-200">
                                                                {selectedDate.toLocaleDateString('en-US', { month: 'short' })}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                {/* Attendance Details */}
                                                {attendance && attendance.records.length > 0 ? (
                                                    <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
                                                        <div className="flex items-center justify-between mb-6">
                                                            <h5 className="text-lg font-bold text-gray-900">Class Details</h5>
                                                            <div className="text-sm text-gray-500">
                                                                {attendance.records.length} {attendance.records.length === 1 ? 'class' : 'classes'}
                                                            </div>
                                                        </div>
                                                        <div className="space-y-3">
                                                            {attendance.records.map((record, index) => (
                                                                <div key={index} className={`group relative overflow-hidden rounded-xl border-2 transition-all duration-300 hover:shadow-lg ${
                                                                    record.status === 'Present' ? 'border-green-200 bg-gradient-to-r from-green-50 to-green-100/50 hover:border-green-300' :
                                                                    record.status === 'Absent' ? 'border-red-200 bg-gradient-to-r from-red-50 to-red-100/50 hover:border-red-300' :
                                                                    'border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100/50 hover:border-gray-300'
                                                                }`}>
                                                                    <div className="p-4">
                                                                        <div className="flex items-center justify-between">
                                                                            <div className="flex items-center gap-4">
                                                                                <div className={`p-3 rounded-xl shadow-sm ${
                                                                                    record.status === 'Present' ? 'bg-green-100' :
                                                                                    record.status === 'Absent' ? 'bg-red-100' :
                                                                                    'bg-gray-100'
                                                                                }`}>
                                                                                    {getStatusIcon(record.status.toLowerCase())}
                                                                                </div>
                                                                                <div>
                                                                                    <h6 className="font-bold text-gray-900 text-lg">{record.subject}</h6>
                                                                                    <div className="flex items-center gap-2 text-sm text-gray-600 mt-1">
                                                                                        <span className="px-2 py-1 bg-white/70 rounded-lg border">
                                                                                            {record.course}
                                                                                        </span>
                                                                                        <span className="px-2 py-1 bg-white/70 rounded-lg border">
                                                                                            {record.batch}
                                                                                        </span>
                                                                                        {record.type === 'holiday' && (
                                                                                            <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded-lg border border-blue-200">
                                                                                                Holiday
                                                                                            </span>
                                                                                        )}
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <div className="text-right">
                                                                                <span className={`inline-flex items-center px-4 py-2 rounded-xl font-semibold text-sm border-2 ${
                                                                                    record.status === 'Present' ? 'bg-green-100 text-green-800 border-green-300' :
                                                                                    record.status === 'Absent' ? 'bg-red-100 text-red-800 border-red-300' :
                                                                                    'bg-gray-100 text-gray-800 border-gray-300'
                                                                                }`}>
                                                                                    {record.status}
                                                                                </span>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    {/* Decorative gradient overlay */}
                                                                    <div className={`absolute top-0 right-0 w-1 h-full ${
                                                                        record.status === 'Present' ? 'bg-gradient-to-b from-green-400 to-green-600' :
                                                                        record.status === 'Absent' ? 'bg-gradient-to-b from-red-400 to-red-600' :
                                                                        'bg-gradient-to-b from-gray-400 to-gray-600'
                                                                    }`}></div>
                                                                </div>
                                                            ))}
                                                        </div>
                                                    </div>
                                                ) : isFuture ? (
                                                    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8 text-center border border-blue-100">
                                                        <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-2xl mx-auto mb-4 flex items-center justify-center">
                                                            <Calendar className="w-8 h-8 text-white" />
                                                        </div>
                                                        <h5 className="text-lg font-bold text-blue-900 mb-2">Future Date</h5>
                                                        <p className="text-blue-600">No attendance data available for future dates.</p>
                                                    </div>
                                                ) : (
                                                    <div className="bg-gradient-to-r from-gray-50 to-slate-50 rounded-2xl p-8 text-center border border-gray-200">
                                                        <div className="w-16 h-16 bg-gradient-to-br from-gray-400 to-slate-500 rounded-2xl mx-auto mb-4 flex items-center justify-center">
                                                            <Calendar className="w-8 h-8 text-white" />
                                                        </div>
                                                        <h5 className="text-lg font-bold text-gray-700 mb-2">No Classes</h5>
                                                        <p className="text-gray-600">No classes were scheduled for this date.</p>
                                                    </div>
                                                )}
                                            </div>
                                        );
                                    })()}
                                </div>
                            </div>
                        )}
                    </div>

                    {/* Right Column - Recent Activity */}
                    <div className="bg-white rounded-2xl p-8 shadow-md border border-blue-100">
                        <h3 className="text-xl font-bold text-blue-900 mb-6 flex items-center gap-2">
                            <Clock className="w-5 h-5 text-blue-700" />
                            <span>Recent Activity</span>
                        </h3>
                        <div className="space-y-5">
                            {attendanceData.recentActivity.map((activity, index) => (
                                <div key={index} className="flex items-center gap-3 p-4 bg-blue-50 rounded-xl border hover:bg-blue-100 transition-colors">
                                    {getStatusIcon(activity.status)}
                                    <div className="flex-1">
                                        <div className="font-semibold text-blue-900">{activity.subject}</div>
                                        <div className="text-sm text-blue-700">{activity.date} • {activity.time}</div>
                                    </div>
                                    <div className={`text-xs px-3 py-1 rounded-full capitalize border
                    ${activity.status === 'present' ? 'bg-green-100 text-green-800 border-green-300' :
                                            activity.status === 'absent' ? 'bg-red-100 text-red-800 border-red-300' :
                                                'bg-yellow-100 text-yellow-800 border-yellow-300'
                                        }`}>
                                        {activity.status}
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AttendancePage;

// Enhanced CSS styles for modern calendar design
const calendarStyles = `
.enhanced-calendar {
    width: 100%;
    border: none;
    font-family: inherit;
    background: transparent;
}

.enhanced-calendar .react-calendar__navigation {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 1rem 1rem 0 0;
    padding: 1rem;
    margin-bottom: 0;
}

.enhanced-calendar .react-calendar__navigation button {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.enhanced-calendar .react-calendar__navigation button:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.enhanced-calendar .react-calendar__navigation button:disabled {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.5);
    transform: none;
    box-shadow: none;
}

.enhanced-calendar .react-calendar__navigation__label {
    font-size: 1.1rem;
    font-weight: 700;
}

.enhanced-calendar .react-calendar__month-view__weekdays {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    font-weight: 700;
    color: #475569;
    padding: 1rem 0;
    border-bottom: 2px solid #e2e8f0;
}

.enhanced-calendar .react-calendar__month-view__weekdays__weekday {
    padding: 0.75rem;
    text-align: center;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.enhanced-calendar .react-calendar__month-view__days {
    background: white;
}

.enhanced-calendar .react-calendar__tile {
    position: relative;
    padding: 1rem 0.5rem;
    background: white;
    border: 1px solid #f1f5f9;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 500;
    min-height: 60px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.enhanced-calendar .react-calendar__tile:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #cbd5e1;
}

.enhanced-calendar .react-calendar__tile--active {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
    color: white !important;
    border-color: #1d4ed8 !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
    font-weight: 700;
}

.enhanced-calendar .react-calendar__tile.present-day {
    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
    border: 2px solid #16a34a;
    color: #15803d;
    font-weight: 600;
}

.enhanced-calendar .react-calendar__tile.present-day:hover {
    background: linear-gradient(135deg, #bbf7d0 0%, #86efac 100%);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(34, 197, 94, 0.3);
}

.enhanced-calendar .react-calendar__tile.absent-day {
    background: linear-gradient(135deg, #fecaca 0%, #fca5a5 100%);
    border: 2px solid #dc2626;
    color: #b91c1c;
    font-weight: 600;
}

.enhanced-calendar .react-calendar__tile.absent-day:hover {
    background: linear-gradient(135deg, #fca5a5 0%, #f87171 100%);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(220, 38, 38, 0.3);
}

.enhanced-calendar .react-calendar__tile.holiday-day {
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
    border: 2px solid #6b7280;
    color: #4b5563;
    font-weight: 600;
}

.enhanced-calendar .react-calendar__tile.holiday-day:hover {
    background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(107, 114, 128, 0.3);
}

.enhanced-calendar .react-calendar__tile.mixed-day {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    border: 2px solid #d97706;
    color: #92400e;
    font-weight: 600;
}

.enhanced-calendar .react-calendar__tile.mixed-day:hover {
    background: linear-gradient(135deg, #fde68a 0%, #fcd34d 100%);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(217, 119, 6, 0.3);
}

.enhanced-calendar .react-calendar__tile.future-date {
    background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
    color: #9ca3af;
    border-color: #e5e7eb;
    cursor: not-allowed;
}

.enhanced-calendar .react-calendar__tile.future-date:hover {
    transform: none;
    box-shadow: none;
    background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
}

.enhanced-calendar .react-calendar__tile.no-class {
    background: white;
    color: #9ca3af;
    border-color: #f1f5f9;
}

.enhanced-calendar .react-calendar__tile.no-class:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

/* Add subtle animations */
@keyframes pulse-dot {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.1); }
}

.enhanced-calendar .react-calendar__tile div div {
    animation: pulse-dot 2s infinite;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .enhanced-calendar .react-calendar__tile {
        min-height: 50px;
        padding: 0.75rem 0.25rem;
        font-size: 0.9rem;
    }

    .enhanced-calendar .react-calendar__navigation button {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }
}
`;

// Inject styles into the document
if (typeof document !== 'undefined') {
    const existingStyle = document.getElementById('calendar-styles');
    if (!existingStyle) {
        const styleElement = document.createElement('style');
        styleElement.id = 'calendar-styles';
        styleElement.textContent = calendarStyles;
        document.head.appendChild(styleElement);
    }
}
{"ast": null, "code": "var _jsxFileName = \"D:\\\\techvritti\\\\Collegemanagement\\\\frontend\\\\src\\\\Screens\\\\Student\\\\AttendancePage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Calendar, Users, BookOpen, GraduationCap, Filter, Download, TrendingUp, Clock, CheckCircle, XCircle, AlertCircle } from 'lucide-react';\nimport 'react-calendar/dist/Calendar.css';\nimport CalendarView from 'react-calendar';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AttendancePage = () => {\n  _s();\n  const [activeView, setActiveView] = useState('overall');\n  const [selectedBatch, setSelectedBatch] = useState('all');\n  const [selectedCourse, setSelectedCourse] = useState('all');\n  const [selectedSubject, setSelectedSubject] = useState('all');\n  const [dateRange, setDateRange] = useState('thisMonth');\n  const [selectedDate, setSelectedDate] = useState(new Date());\n  const attendanceData = {\n    overall: {\n      present: 85,\n      absent: 15,\n      total: 100\n    },\n    subjects: [{\n      name: 'Mathematics',\n      present: 18,\n      total: 20,\n      percentage: 90\n    }, {\n      name: 'Physics',\n      present: 16,\n      total: 18,\n      percentage: 89\n    }, {\n      name: 'Chemistry',\n      present: 14,\n      total: 16,\n      percentage: 88\n    }, {\n      name: 'Computer Science',\n      present: 19,\n      total: 22,\n      percentage: 86\n    }, {\n      name: 'English',\n      present: 15,\n      total: 18,\n      percentage: 83\n    }],\n    batches: [{\n      name: 'Batch A',\n      students: 45,\n      avgAttendance: 87\n    }, {\n      name: 'Batch B',\n      students: 42,\n      avgAttendance: 85\n    }, {\n      name: 'Batch C',\n      students: 48,\n      avgAttendance: 89\n    }],\n    courses: [{\n      name: 'Engineering',\n      students: 135,\n      avgAttendance: 87\n    }, {\n      name: 'Science',\n      students: 98,\n      avgAttendance: 85\n    }, {\n      name: 'Commerce',\n      students: 76,\n      avgAttendance: 91\n    }],\n    recentActivity: [{\n      date: '2024-03-15',\n      subject: 'Mathematics',\n      status: 'present',\n      time: '09:00 AM'\n    }, {\n      date: '2024-03-15',\n      subject: 'Physics',\n      status: 'present',\n      time: '10:30 AM'\n    }, {\n      date: '2024-03-14',\n      subject: 'Chemistry',\n      status: 'absent',\n      time: '02:00 PM'\n    }, {\n      date: '2024-03-14',\n      subject: 'Computer Science',\n      status: 'present',\n      time: '03:30 PM'\n    }, {\n      date: '2024-03-13',\n      subject: 'English',\n      status: 'present',\n      time: '11:00 AM'\n    }]\n  };\n  const getStatusIcon = status => {\n    switch (status) {\n      case 'present':\n        return /*#__PURE__*/_jsxDEV(CheckCircle, {\n          className: \"w-4 h-4 text-green-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 36\n        }, this);\n      case 'absent':\n        return /*#__PURE__*/_jsxDEV(XCircle, {\n          className: \"w-4 h-4 text-red-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 35\n        }, this);\n      case 'late':\n        return /*#__PURE__*/_jsxDEV(AlertCircle, {\n          className: \"w-4 h-4 text-yellow-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 33\n        }, this);\n      default:\n        return null;\n    }\n  };\n  const getAttendanceColor = percentage => {\n    if (percentage >= 90) return 'text-green-600 bg-green-50';\n    if (percentage >= 75) return 'text-yellow-600 bg-yellow-50';\n    return 'text-red-600 bg-red-50';\n  };\n  const StatCard = ({\n    title,\n    value,\n    subtitle,\n    icon: Icon,\n    trend\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-xl p-6 shadow hover:shadow-lg transition-all border border-gray-100\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-3 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg\",\n          children: /*#__PURE__*/_jsxDEV(Icon, {\n            className: \"w-5 h-5 text-blue-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-gray-700 font-semibold\",\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 17\n      }, this), trend && /*#__PURE__*/_jsxDEV(TrendingUp, {\n        className: \"w-4 h-4 text-green-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 27\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-3xl font-bold text-gray-900\",\n        children: value\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-gray-500\",\n        children: subtitle\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 9\n  }, this);\n  const ProgressBar = ({\n    percentage,\n    label,\n    total,\n    present\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-2\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-sm font-medium text-gray-700\",\n        children: label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: `text-xs px-2 py-1 rounded-full ${getAttendanceColor(percentage)}`,\n        children: [percentage, \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-full bg-gray-200 rounded-full h-2\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `h-2 rounded-full ${percentage >= 90 ? 'bg-green-500' : percentage >= 75 ? 'bg-yellow-500' : 'bg-red-500'}`,\n        style: {\n          width: `${percentage}%`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between text-xs text-gray-500\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: [\"Present: \", present]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: [\"Total: \", total]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 79,\n    columnNumber: 9\n  }, this);\n  const handleDateChange = date => {\n    setSelectedDate(date);\n    console.log('Selected Date:', date);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 p-6\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-100\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-3xl font-bold text-gray-900\",\n              children: \"\\uD83D\\uDCCA Attendance Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 mt-1\",\n              children: \"Monitor and analyze attendance across different categories\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition\",\n              children: [/*#__PURE__*/_jsxDEV(Download, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Export\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"flex items-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition\",\n              children: [/*#__PURE__*/_jsxDEV(Filter, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Filter\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl p-2 shadow-sm border border-gray-100 flex flex-wrap gap-2\",\n        children: [{\n          id: 'overall',\n          label: 'Overall',\n          icon: TrendingUp\n        }, {\n          id: 'subjects',\n          label: 'By Subject',\n          icon: BookOpen\n        }, {\n          id: 'batches',\n          label: 'By Batch',\n          icon: Users\n        }, {\n          id: 'courses',\n          label: 'By Course',\n          icon: GraduationCap\n        }, {\n          id: 'calendar',\n          label: 'Calendar',\n          icon: Calendar\n        }].map(({\n          id,\n          label,\n          icon: Icon\n        }) => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveView(id),\n          className: `flex items-center gap-2 px-4 py-2 rounded-lg transition-all ${activeView === id ? 'bg-blue-600 text-white shadow' : 'text-gray-600 hover:bg-gray-100'}`,\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            className: \"w-4 h-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 29\n          }, this)]\n        }, id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 25\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-100 grid grid-cols-1 md:grid-cols-4 gap-4\",\n        children: [{\n          label: 'Date Range',\n          value: dateRange,\n          setter: setDateRange,\n          options: ['today', 'thisWeek', 'thisMonth', 'lastMonth', 'thisYear']\n        }, {\n          label: 'Batch',\n          value: selectedBatch,\n          setter: setSelectedBatch,\n          options: ['all', 'batchA', 'batchB', 'batchC']\n        }, {\n          label: 'Course',\n          value: selectedCourse,\n          setter: setSelectedCourse,\n          options: ['all', 'engineering', 'science', 'commerce']\n        }, {\n          label: 'Subject',\n          value: selectedSubject,\n          setter: setSelectedSubject,\n          options: ['all', 'mathematics', 'physics', 'chemistry', 'computerScience', 'english']\n        }].map(({\n          label,\n          value,\n          setter,\n          options\n        }, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: value,\n            onChange: e => setter(e.target.value),\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\",\n            children: options.map(opt => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: opt,\n              children: opt.charAt(0).toUpperCase() + opt.slice(1)\n            }, opt, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 37\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 29\n          }, this)]\n        }, idx, true, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 25\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Overall Attendance\",\n          value: \"85%\",\n          subtitle: \"This month\",\n          icon: TrendingUp,\n          trend: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Total Classes\",\n          value: \"124\",\n          subtitle: \"Conducted\",\n          icon: Calendar\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Present Days\",\n          value: \"105\",\n          subtitle: \"Out of 124\",\n          icon: CheckCircle\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 xl:grid-cols-3 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"xl:col-span-2 space-y-6\",\n          children: [activeView === 'overall' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900 mb-6\",\n              children: \"Overall Summary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center p-6 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-4xl font-bold text-blue-600 mb-2\",\n                  children: \"85%\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-blue-700 font-medium\",\n                  children: \"Overall Attendance\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center p-4 bg-green-50 rounded-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                      className: \"w-8 h-8 text-green-600\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 196,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"font-semibold text-green-900\",\n                        children: \"Present\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 198,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-green-700\",\n                        children: \"Days attended\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 199,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 197,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 195,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-bold text-green-600\",\n                    children: \"105\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 202,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between items-center p-4 bg-red-50 rounded-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [/*#__PURE__*/_jsxDEV(XCircle, {\n                      className: \"w-8 h-8 text-red-600\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 206,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"font-semibold text-red-900\",\n                        children: \"Absent\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 208,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-red-700\",\n                        children: \"Days missed\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 209,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 207,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 205,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-bold text-red-600\",\n                    children: \"19\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 212,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 29\n          }, this), activeView === 'subjects' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-100 space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900\",\n              children: \"Subject-wise Attendance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 33\n            }, this), attendanceData.subjects.map((s, i) => /*#__PURE__*/_jsxDEV(ProgressBar, {\n              percentage: s.percentage,\n              label: s.name,\n              total: s.total,\n              present: s.present\n            }, i, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 37\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 29\n          }, this), activeView === 'batches' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900 mb-6\",\n              children: \"Batch-wise Attendance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n              children: attendanceData.batches.map((b, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-semibold text-gray-900\",\n                    children: b.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(Users, {\n                    className: \"w-5 h-5 text-gray-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 236,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-2xl font-bold\",\n                  children: [b.avgAttendance, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600\",\n                  children: [b.students, \" students\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-full bg-gray-200 rounded-full h-2 mt-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"h-2 bg-blue-500 rounded-full\",\n                    style: {\n                      width: `${b.avgAttendance}%`\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 49\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 45\n                }, this)]\n              }, i, true, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 41\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 29\n          }, this), activeView === 'courses' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-100 space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900 mb-6\",\n              children: \"Course-wise Attendance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 33\n            }, this), attendanceData.courses.map((c, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-2 bg-blue-50 rounded-lg\",\n                  children: /*#__PURE__*/_jsxDEV(GraduationCap, {\n                    className: \"w-6 h-6 text-blue-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 49\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-semibold text-gray-900\",\n                    children: c.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 259,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: [c.students, \" students enrolled\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 260,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `text-lg font-bold px-3 py-1 rounded-full ${getAttendanceColor(c.avgAttendance)}`,\n                children: [c.avgAttendance, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 41\n              }, this)]\n            }, i, true, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 37\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 29\n          }, this), activeView === 'calendar' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900 mb-6\",\n              children: \"Calendar View\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(CalendarView, {\n              value: selectedDate,\n              onChange: handleDateChange,\n              className: \"p-4 bg-white rounded-lg shadow\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-100\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-semibold text-gray-900 mb-6 flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(Clock, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 29\n            }, this), \" Recent Activity\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: attendanceData.recentActivity.map((a, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition\",\n              children: [getStatusIcon(a.status), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-medium text-gray-900\",\n                  children: a.subject\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600\",\n                  children: [a.date, \" \\u2022 \", a.time]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `text-xs px-2 py-1 rounded-full capitalize ${a.status === 'present' ? 'bg-green-100 text-green-800' : a.status === 'absent' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'}`,\n                children: a.status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 37\n              }, this)]\n            }, i, true, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 105,\n    columnNumber: 9\n  }, this);\n};\n_s(AttendancePage, \"Jx1DPYnPOwpB+hFnpQTOTVpjDv4=\");\n_c = AttendancePage;\nexport default AttendancePage;\nvar _c;\n$RefreshReg$(_c, \"AttendancePage\");", "map": {"version": 3, "names": ["React", "useState", "Calendar", "Users", "BookOpen", "GraduationCap", "Filter", "Download", "TrendingUp", "Clock", "CheckCircle", "XCircle", "AlertCircle", "CalendarView", "jsxDEV", "_jsxDEV", "AttendancePage", "_s", "activeView", "setActiveView", "selected<PERSON><PERSON>", "setSelectedBatch", "selectedCourse", "setSelectedCourse", "selectedSubject", "setSelectedSubject", "date<PERSON><PERSON><PERSON>", "setDateRange", "selectedDate", "setSelectedDate", "Date", "attendanceData", "overall", "present", "absent", "total", "subjects", "name", "percentage", "batches", "students", "avgAttendance", "courses", "recentActivity", "date", "subject", "status", "time", "getStatusIcon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getAttendanceColor", "StatCard", "title", "value", "subtitle", "icon", "Icon", "trend", "children", "ProgressBar", "label", "style", "width", "handleDateChange", "console", "log", "id", "map", "onClick", "setter", "options", "idx", "onChange", "e", "target", "opt", "char<PERSON>t", "toUpperCase", "slice", "s", "i", "b", "c", "a", "_c", "$RefreshReg$"], "sources": ["D:/techvritti/Collegemanagement/frontend/src/Screens/Student/AttendancePage.jsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport {\r\n    Calendar, Users, BookOpen, GraduationCap, Filter, Download,\r\n    TrendingUp, Clock, CheckCircle, XCircle, AlertCircle\r\n} from 'lucide-react';\r\nimport 'react-calendar/dist/Calendar.css';\r\nimport CalendarView from 'react-calendar';\r\n\r\nconst AttendancePage = () => {\r\n    const [activeView, setActiveView] = useState('overall');\r\n    const [selectedBatch, setSelectedBatch] = useState('all');\r\n    const [selectedCourse, setSelectedCourse] = useState('all');\r\n    const [selectedSubject, setSelectedSubject] = useState('all');\r\n    const [dateRange, setDateRange] = useState('thisMonth');\r\n    const [selectedDate, setSelectedDate] = useState(new Date());\r\n\r\n    const attendanceData = {\r\n        overall: { present: 85, absent: 15, total: 100 },\r\n        subjects: [\r\n            { name: 'Mathematics', present: 18, total: 20, percentage: 90 },\r\n            { name: 'Physics', present: 16, total: 18, percentage: 89 },\r\n            { name: 'Chemistry', present: 14, total: 16, percentage: 88 },\r\n            { name: 'Computer Science', present: 19, total: 22, percentage: 86 },\r\n            { name: 'English', present: 15, total: 18, percentage: 83 }\r\n        ],\r\n        batches: [\r\n            { name: 'Batch A', students: 45, avgAttendance: 87 },\r\n            { name: 'Batch B', students: 42, avgAttendance: 85 },\r\n            { name: 'Batch C', students: 48, avgAttendance: 89 }\r\n        ],\r\n        courses: [\r\n            { name: 'Engineering', students: 135, avgAttendance: 87 },\r\n            { name: 'Science', students: 98, avgAttendance: 85 },\r\n            { name: 'Commerce', students: 76, avgAttendance: 91 }\r\n        ],\r\n        recentActivity: [\r\n            { date: '2024-03-15', subject: 'Mathematics', status: 'present', time: '09:00 AM' },\r\n            { date: '2024-03-15', subject: 'Physics', status: 'present', time: '10:30 AM' },\r\n            { date: '2024-03-14', subject: 'Chemistry', status: 'absent', time: '02:00 PM' },\r\n            { date: '2024-03-14', subject: 'Computer Science', status: 'present', time: '03:30 PM' },\r\n            { date: '2024-03-13', subject: 'English', status: 'present', time: '11:00 AM' }\r\n        ]\r\n    };\r\n\r\n    const getStatusIcon = (status) => {\r\n        switch (status) {\r\n            case 'present': return <CheckCircle className=\"w-4 h-4 text-green-500\" />;\r\n            case 'absent': return <XCircle className=\"w-4 h-4 text-red-500\" />;\r\n            case 'late': return <AlertCircle className=\"w-4 h-4 text-yellow-500\" />;\r\n            default: return null;\r\n        }\r\n    };\r\n\r\n    const getAttendanceColor = (percentage) => {\r\n        if (percentage >= 90) return 'text-green-600 bg-green-50';\r\n        if (percentage >= 75) return 'text-yellow-600 bg-yellow-50';\r\n        return 'text-red-600 bg-red-50';\r\n    };\r\n\r\n    const StatCard = ({ title, value, subtitle, icon: Icon, trend }) => (\r\n        <div className=\"bg-white rounded-xl p-6 shadow hover:shadow-lg transition-all border border-gray-100\">\r\n            <div className=\"flex items-center justify-between mb-4\">\r\n                <div className=\"flex items-center space-x-3\">\r\n                    <div className=\"p-3 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg\">\r\n                        <Icon className=\"w-5 h-5 text-blue-600\" />\r\n                    </div>\r\n                    <h3 className=\"text-gray-700 font-semibold\">{title}</h3>\r\n                </div>\r\n                {trend && <TrendingUp className=\"w-4 h-4 text-green-500\" />}\r\n            </div>\r\n            <div>\r\n                <p className=\"text-3xl font-bold text-gray-900\">{value}</p>\r\n                <p className=\"text-sm text-gray-500\">{subtitle}</p>\r\n            </div>\r\n        </div>\r\n    );\r\n\r\n    const ProgressBar = ({ percentage, label, total, present }) => (\r\n        <div className=\"space-y-2\">\r\n            <div className=\"flex justify-between items-center\">\r\n                <span className=\"text-sm font-medium text-gray-700\">{label}</span>\r\n                <span className={`text-xs px-2 py-1 rounded-full ${getAttendanceColor(percentage)}`}>\r\n                    {percentage}%\r\n                </span>\r\n            </div>\r\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\r\n                <div\r\n                    className={`h-2 rounded-full ${percentage >= 90 ? 'bg-green-500' : percentage >= 75 ? 'bg-yellow-500' : 'bg-red-500'}`}\r\n                    style={{ width: `${percentage}%` }}\r\n                ></div>\r\n            </div>\r\n            <div className=\"flex justify-between text-xs text-gray-500\">\r\n                <span>Present: {present}</span>\r\n                <span>Total: {total}</span>\r\n            </div>\r\n        </div>\r\n    );\r\n\r\n    const handleDateChange = (date) => {\r\n        setSelectedDate(date);\r\n        console.log('Selected Date:', date);\r\n    };\r\n\r\n    return (\r\n        <div className=\"min-h-screen bg-gray-50 p-6\">\r\n            <div className=\"max-w-7xl mx-auto space-y-6\">\r\n\r\n                {/* Header */}\r\n                <div className=\"bg-white rounded-xl p-6 shadow-sm border border-gray-100\">\r\n                    <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\">\r\n                        <div>\r\n                            <h1 className=\"text-3xl font-bold text-gray-900\">📊 Attendance Dashboard</h1>\r\n                            <p className=\"text-gray-600 mt-1\">Monitor and analyze attendance across different categories</p>\r\n                        </div>\r\n                        <div className=\"flex items-center gap-3\">\r\n                            <button className=\"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition\">\r\n                                <Download className=\"w-4 h-4\" />\r\n                                <span>Export</span>\r\n                            </button>\r\n                            <button className=\"flex items-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition\">\r\n                                <Filter className=\"w-4 h-4\" />\r\n                                <span>Filter</span>\r\n                            </button>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* View Toggle */}\r\n                <div className=\"bg-white rounded-xl p-2 shadow-sm border border-gray-100 flex flex-wrap gap-2\">\r\n                    {[\r\n                        { id: 'overall', label: 'Overall', icon: TrendingUp },\r\n                        { id: 'subjects', label: 'By Subject', icon: BookOpen },\r\n                        { id: 'batches', label: 'By Batch', icon: Users },\r\n                        { id: 'courses', label: 'By Course', icon: GraduationCap },\r\n                        { id: 'calendar', label: 'Calendar', icon: Calendar }\r\n                    ].map(({ id, label, icon: Icon }) => (\r\n                        <button\r\n                            key={id}\r\n                            onClick={() => setActiveView(id)}\r\n                            className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all ${activeView === id\r\n                                ? 'bg-blue-600 text-white shadow'\r\n                                : 'text-gray-600 hover:bg-gray-100'\r\n                                }`}\r\n                        >\r\n                            <Icon className=\"w-4 h-4\" />\r\n                            <span className=\"font-medium\">{label}</span>\r\n                        </button>\r\n                    ))}\r\n                </div>\r\n\r\n                {/* Filters */}\r\n                <div className=\"bg-white rounded-xl p-6 shadow-sm border border-gray-100 grid grid-cols-1 md:grid-cols-4 gap-4\">\r\n                    {[\r\n                        { label: 'Date Range', value: dateRange, setter: setDateRange, options: ['today', 'thisWeek', 'thisMonth', 'lastMonth', 'thisYear'] },\r\n                        { label: 'Batch', value: selectedBatch, setter: setSelectedBatch, options: ['all', 'batchA', 'batchB', 'batchC'] },\r\n                        { label: 'Course', value: selectedCourse, setter: setSelectedCourse, options: ['all', 'engineering', 'science', 'commerce'] },\r\n                        { label: 'Subject', value: selectedSubject, setter: setSelectedSubject, options: ['all', 'mathematics', 'physics', 'chemistry', 'computerScience', 'english'] }\r\n                    ].map(({ label, value, setter, options }, idx) => (\r\n                        <div key={idx}>\r\n                            <label className=\"block text-sm font-medium text-gray-700 mb-2\">{label}</label>\r\n                            <select\r\n                                value={value}\r\n                                onChange={(e) => setter(e.target.value)}\r\n                                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500\"\r\n                            >\r\n                                {options.map(opt => (\r\n                                    <option key={opt} value={opt}>{opt.charAt(0).toUpperCase() + opt.slice(1)}</option>\r\n                                ))}\r\n                            </select>\r\n                        </div>\r\n                    ))}\r\n                </div>\r\n\r\n                {/* Stats Cards */}\r\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\r\n                    <StatCard title=\"Overall Attendance\" value=\"85%\" subtitle=\"This month\" icon={TrendingUp} trend />\r\n                    <StatCard title=\"Total Classes\" value=\"124\" subtitle=\"Conducted\" icon={Calendar} />\r\n                    <StatCard title=\"Present Days\" value=\"105\" subtitle=\"Out of 124\" icon={CheckCircle} />\r\n                </div>\r\n\r\n                {/* Main Content */}\r\n                <div className=\"grid grid-cols-1 xl:grid-cols-3 gap-6\">\r\n                    {/* Left */}\r\n                    <div className=\"xl:col-span-2 space-y-6\">\r\n                        {activeView === 'overall' && (\r\n                            <div className=\"bg-white rounded-xl p-6 shadow-sm border border-gray-100\">\r\n                                <h3 className=\"text-xl font-semibold text-gray-900 mb-6\">Overall Summary</h3>\r\n                                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                                    <div className=\"text-center p-6 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl\">\r\n                                        <div className=\"text-4xl font-bold text-blue-600 mb-2\">85%</div>\r\n                                        <div className=\"text-blue-700 font-medium\">Overall Attendance</div>\r\n                                    </div>\r\n                                    <div className=\"space-y-4\">\r\n                                        <div className=\"flex justify-between items-center p-4 bg-green-50 rounded-lg\">\r\n                                            <div className=\"flex items-center gap-3\">\r\n                                                <CheckCircle className=\"w-8 h-8 text-green-600\" />\r\n                                                <div>\r\n                                                    <div className=\"font-semibold text-green-900\">Present</div>\r\n                                                    <div className=\"text-sm text-green-700\">Days attended</div>\r\n                                                </div>\r\n                                            </div>\r\n                                            <div className=\"text-2xl font-bold text-green-600\">105</div>\r\n                                        </div>\r\n                                        <div className=\"flex justify-between items-center p-4 bg-red-50 rounded-lg\">\r\n                                            <div className=\"flex items-center gap-3\">\r\n                                                <XCircle className=\"w-8 h-8 text-red-600\" />\r\n                                                <div>\r\n                                                    <div className=\"font-semibold text-red-900\">Absent</div>\r\n                                                    <div className=\"text-sm text-red-700\">Days missed</div>\r\n                                                </div>\r\n                                            </div>\r\n                                            <div className=\"text-2xl font-bold text-red-600\">19</div>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        )}\r\n\r\n                        {activeView === 'subjects' && (\r\n                            <div className=\"bg-white rounded-xl p-6 shadow-sm border border-gray-100 space-y-6\">\r\n                                <h3 className=\"text-xl font-semibold text-gray-900\">Subject-wise Attendance</h3>\r\n                                {attendanceData.subjects.map((s, i) => (\r\n                                    <ProgressBar key={i} percentage={s.percentage} label={s.name} total={s.total} present={s.present} />\r\n                                ))}\r\n                            </div>\r\n                        )}\r\n\r\n                        {activeView === 'batches' && (\r\n                            <div className=\"bg-white rounded-xl p-6 shadow-sm border border-gray-100\">\r\n                                <h3 className=\"text-xl font-semibold text-gray-900 mb-6\">Batch-wise Attendance</h3>\r\n                                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n                                    {attendanceData.batches.map((b, i) => (\r\n                                        <div key={i} className=\"p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition\">\r\n                                            <div className=\"flex justify-between mb-3\">\r\n                                                <h4 className=\"font-semibold text-gray-900\">{b.name}</h4>\r\n                                                <Users className=\"w-5 h-5 text-gray-400\" />\r\n                                            </div>\r\n                                            <div className=\"text-2xl font-bold\">{b.avgAttendance}%</div>\r\n                                            <div className=\"text-sm text-gray-600\">{b.students} students</div>\r\n                                            <div className=\"w-full bg-gray-200 rounded-full h-2 mt-2\">\r\n                                                <div className=\"h-2 bg-blue-500 rounded-full\" style={{ width: `${b.avgAttendance}%` }}></div>\r\n                                            </div>\r\n                                        </div>\r\n                                    ))}\r\n                                </div>\r\n                            </div>\r\n                        )}\r\n\r\n                        {activeView === 'courses' && (\r\n                            <div className=\"bg-white rounded-xl p-6 shadow-sm border border-gray-100 space-y-4\">\r\n                                <h3 className=\"text-xl font-semibold text-gray-900 mb-6\">Course-wise Attendance</h3>\r\n                                {attendanceData.courses.map((c, i) => (\r\n                                    <div key={i} className=\"flex justify-between items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition\">\r\n                                        <div className=\"flex items-center gap-4\">\r\n                                            <div className=\"p-2 bg-blue-50 rounded-lg\">\r\n                                                <GraduationCap className=\"w-6 h-6 text-blue-600\" />\r\n                                            </div>\r\n                                            <div>\r\n                                                <h4 className=\"font-semibold text-gray-900\">{c.name}</h4>\r\n                                                <p className=\"text-sm text-gray-600\">{c.students} students enrolled</p>\r\n                                            </div>\r\n                                        </div>\r\n                                        <div className={`text-lg font-bold px-3 py-1 rounded-full ${getAttendanceColor(c.avgAttendance)}`}>\r\n                                            {c.avgAttendance}%\r\n                                        </div>\r\n                                    </div>\r\n                                ))}\r\n                            </div>\r\n                        )}\r\n\r\n                        {activeView === 'calendar' && (\r\n                            <div className=\"bg-white rounded-xl p-6 shadow-sm border border-gray-100\">\r\n                                <h3 className=\"text-xl font-semibold text-gray-900 mb-6\">Calendar View</h3>\r\n                                <CalendarView value={selectedDate} onChange={handleDateChange} className=\"p-4 bg-white rounded-lg shadow\" />\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n\r\n                    {/* Right */}\r\n                    <div className=\"bg-white rounded-xl p-6 shadow-sm border border-gray-100\">\r\n                        <h3 className=\"text-xl font-semibold text-gray-900 mb-6 flex items-center gap-2\">\r\n                            <Clock className=\"w-5 h-5\" /> Recent Activity\r\n                        </h3>\r\n                        <div className=\"space-y-4\">\r\n                            {attendanceData.recentActivity.map((a, i) => (\r\n                                <div key={i} className=\"flex items-center gap-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition\">\r\n                                    {getStatusIcon(a.status)}\r\n                                    <div className=\"flex-1\">\r\n                                        <div className=\"font-medium text-gray-900\">{a.subject}</div>\r\n                                        <div className=\"text-sm text-gray-600\">{a.date} • {a.time}</div>\r\n                                    </div>\r\n                                    <div className={`text-xs px-2 py-1 rounded-full capitalize ${a.status === 'present'\r\n                                        ? 'bg-green-100 text-green-800'\r\n                                        : a.status === 'absent'\r\n                                            ? 'bg-red-100 text-red-800'\r\n                                            : 'bg-yellow-100 text-yellow-800'\r\n                                        }`}>\r\n                                        {a.status}\r\n                                    </div>\r\n                                </div>\r\n                            ))}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default AttendancePage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACIC,QAAQ,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,MAAM,EAAEC,QAAQ,EAC1DC,UAAU,EAAEC,KAAK,EAAEC,WAAW,EAAEC,OAAO,EAAEC,WAAW,QACjD,cAAc;AACrB,OAAO,kCAAkC;AACzC,OAAOC,YAAY,MAAM,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,SAAS,CAAC;EACvD,MAAM,CAACmB,aAAa,EAAEC,gBAAgB,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACqB,cAAc,EAAEC,iBAAiB,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACuB,eAAe,EAAEC,kBAAkB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC,WAAW,CAAC;EACvD,MAAM,CAAC2B,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,IAAI6B,IAAI,CAAC,CAAC,CAAC;EAE5D,MAAMC,cAAc,GAAG;IACnBC,OAAO,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAEC,MAAM,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAI,CAAC;IAChDC,QAAQ,EAAE,CACN;MAAEC,IAAI,EAAE,aAAa;MAAEJ,OAAO,EAAE,EAAE;MAAEE,KAAK,EAAE,EAAE;MAAEG,UAAU,EAAE;IAAG,CAAC,EAC/D;MAAED,IAAI,EAAE,SAAS;MAAEJ,OAAO,EAAE,EAAE;MAAEE,KAAK,EAAE,EAAE;MAAEG,UAAU,EAAE;IAAG,CAAC,EAC3D;MAAED,IAAI,EAAE,WAAW;MAAEJ,OAAO,EAAE,EAAE;MAAEE,KAAK,EAAE,EAAE;MAAEG,UAAU,EAAE;IAAG,CAAC,EAC7D;MAAED,IAAI,EAAE,kBAAkB;MAAEJ,OAAO,EAAE,EAAE;MAAEE,KAAK,EAAE,EAAE;MAAEG,UAAU,EAAE;IAAG,CAAC,EACpE;MAAED,IAAI,EAAE,SAAS;MAAEJ,OAAO,EAAE,EAAE;MAAEE,KAAK,EAAE,EAAE;MAAEG,UAAU,EAAE;IAAG,CAAC,CAC9D;IACDC,OAAO,EAAE,CACL;MAAEF,IAAI,EAAE,SAAS;MAAEG,QAAQ,EAAE,EAAE;MAAEC,aAAa,EAAE;IAAG,CAAC,EACpD;MAAEJ,IAAI,EAAE,SAAS;MAAEG,QAAQ,EAAE,EAAE;MAAEC,aAAa,EAAE;IAAG,CAAC,EACpD;MAAEJ,IAAI,EAAE,SAAS;MAAEG,QAAQ,EAAE,EAAE;MAAEC,aAAa,EAAE;IAAG,CAAC,CACvD;IACDC,OAAO,EAAE,CACL;MAAEL,IAAI,EAAE,aAAa;MAAEG,QAAQ,EAAE,GAAG;MAAEC,aAAa,EAAE;IAAG,CAAC,EACzD;MAAEJ,IAAI,EAAE,SAAS;MAAEG,QAAQ,EAAE,EAAE;MAAEC,aAAa,EAAE;IAAG,CAAC,EACpD;MAAEJ,IAAI,EAAE,UAAU;MAAEG,QAAQ,EAAE,EAAE;MAAEC,aAAa,EAAE;IAAG,CAAC,CACxD;IACDE,cAAc,EAAE,CACZ;MAAEC,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,aAAa;MAAEC,MAAM,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAW,CAAC,EACnF;MAAEH,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAW,CAAC,EAC/E;MAAEH,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,WAAW;MAAEC,MAAM,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAW,CAAC,EAChF;MAAEH,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,kBAAkB;MAAEC,MAAM,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAW,CAAC,EACxF;MAAEH,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAW,CAAC;EAEvF,CAAC;EAED,MAAMC,aAAa,GAAIF,MAAM,IAAK;IAC9B,QAAQA,MAAM;MACV,KAAK,SAAS;QAAE,oBAAO/B,OAAA,CAACL,WAAW;UAACuC,SAAS,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzE,KAAK,QAAQ;QAAE,oBAAOtC,OAAA,CAACJ,OAAO;UAACsC,SAAS,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAClE,KAAK,MAAM;QAAE,oBAAOtC,OAAA,CAACH,WAAW;UAACqC,SAAS,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvE;QAAS,OAAO,IAAI;IACxB;EACJ,CAAC;EAED,MAAMC,kBAAkB,GAAIhB,UAAU,IAAK;IACvC,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO,4BAA4B;IACzD,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO,8BAA8B;IAC3D,OAAO,wBAAwB;EACnC,CAAC;EAED,MAAMiB,QAAQ,GAAGA,CAAC;IAAEC,KAAK;IAAEC,KAAK;IAAEC,QAAQ;IAAEC,IAAI,EAAEC,IAAI;IAAEC;EAAM,CAAC,kBAC3D9C,OAAA;IAAKkC,SAAS,EAAC,sFAAsF;IAAAa,QAAA,gBACjG/C,OAAA;MAAKkC,SAAS,EAAC,wCAAwC;MAAAa,QAAA,gBACnD/C,OAAA;QAAKkC,SAAS,EAAC,6BAA6B;QAAAa,QAAA,gBACxC/C,OAAA;UAAKkC,SAAS,EAAC,2DAA2D;UAAAa,QAAA,eACtE/C,OAAA,CAAC6C,IAAI;YAACX,SAAS,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACNtC,OAAA;UAAIkC,SAAS,EAAC,6BAA6B;UAAAa,QAAA,EAAEN;QAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,EACLQ,KAAK,iBAAI9C,OAAA,CAACP,UAAU;QAACyC,SAAS,EAAC;MAAwB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CAAC,eACNtC,OAAA;MAAA+C,QAAA,gBACI/C,OAAA;QAAGkC,SAAS,EAAC,kCAAkC;QAAAa,QAAA,EAAEL;MAAK;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3DtC,OAAA;QAAGkC,SAAS,EAAC,uBAAuB;QAAAa,QAAA,EAAEJ;MAAQ;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACR;EAED,MAAMU,WAAW,GAAGA,CAAC;IAAEzB,UAAU;IAAE0B,KAAK;IAAE7B,KAAK;IAAEF;EAAQ,CAAC,kBACtDlB,OAAA;IAAKkC,SAAS,EAAC,WAAW;IAAAa,QAAA,gBACtB/C,OAAA;MAAKkC,SAAS,EAAC,mCAAmC;MAAAa,QAAA,gBAC9C/C,OAAA;QAAMkC,SAAS,EAAC,mCAAmC;QAAAa,QAAA,EAAEE;MAAK;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAClEtC,OAAA;QAAMkC,SAAS,EAAG,kCAAiCK,kBAAkB,CAAChB,UAAU,CAAE,EAAE;QAAAwB,QAAA,GAC/ExB,UAAU,EAAC,GAChB;MAAA;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eACNtC,OAAA;MAAKkC,SAAS,EAAC,qCAAqC;MAAAa,QAAA,eAChD/C,OAAA;QACIkC,SAAS,EAAG,oBAAmBX,UAAU,IAAI,EAAE,GAAG,cAAc,GAAGA,UAAU,IAAI,EAAE,GAAG,eAAe,GAAG,YAAa,EAAE;QACvH2B,KAAK,EAAE;UAAEC,KAAK,EAAG,GAAE5B,UAAW;QAAG;MAAE;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eACNtC,OAAA;MAAKkC,SAAS,EAAC,4CAA4C;MAAAa,QAAA,gBACvD/C,OAAA;QAAA+C,QAAA,GAAM,WAAS,EAAC7B,OAAO;MAAA;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC/BtC,OAAA;QAAA+C,QAAA,GAAM,SAAO,EAAC3B,KAAK;MAAA;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACR;EAED,MAAMc,gBAAgB,GAAIvB,IAAI,IAAK;IAC/Bf,eAAe,CAACe,IAAI,CAAC;IACrBwB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEzB,IAAI,CAAC;EACvC,CAAC;EAED,oBACI7B,OAAA;IAAKkC,SAAS,EAAC,6BAA6B;IAAAa,QAAA,eACxC/C,OAAA;MAAKkC,SAAS,EAAC,6BAA6B;MAAAa,QAAA,gBAGxC/C,OAAA;QAAKkC,SAAS,EAAC,0DAA0D;QAAAa,QAAA,eACrE/C,OAAA;UAAKkC,SAAS,EAAC,oEAAoE;UAAAa,QAAA,gBAC/E/C,OAAA;YAAA+C,QAAA,gBACI/C,OAAA;cAAIkC,SAAS,EAAC,kCAAkC;cAAAa,QAAA,EAAC;YAAuB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7EtC,OAAA;cAAGkC,SAAS,EAAC,oBAAoB;cAAAa,QAAA,EAAC;YAA0D;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/F,CAAC,eACNtC,OAAA;YAAKkC,SAAS,EAAC,yBAAyB;YAAAa,QAAA,gBACpC/C,OAAA;cAAQkC,SAAS,EAAC,kGAAkG;cAAAa,QAAA,gBAChH/C,OAAA,CAACR,QAAQ;gBAAC0C,SAAS,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChCtC,OAAA;gBAAA+C,QAAA,EAAM;cAAM;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACTtC,OAAA;cAAQkC,SAAS,EAAC,+GAA+G;cAAAa,QAAA,gBAC7H/C,OAAA,CAACT,MAAM;gBAAC2C,SAAS,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9BtC,OAAA;gBAAA+C,QAAA,EAAM;cAAM;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNtC,OAAA;QAAKkC,SAAS,EAAC,+EAA+E;QAAAa,QAAA,EACzF,CACG;UAAEQ,EAAE,EAAE,SAAS;UAAEN,KAAK,EAAE,SAAS;UAAEL,IAAI,EAAEnD;QAAW,CAAC,EACrD;UAAE8D,EAAE,EAAE,UAAU;UAAEN,KAAK,EAAE,YAAY;UAAEL,IAAI,EAAEvD;QAAS,CAAC,EACvD;UAAEkE,EAAE,EAAE,SAAS;UAAEN,KAAK,EAAE,UAAU;UAAEL,IAAI,EAAExD;QAAM,CAAC,EACjD;UAAEmE,EAAE,EAAE,SAAS;UAAEN,KAAK,EAAE,WAAW;UAAEL,IAAI,EAAEtD;QAAc,CAAC,EAC1D;UAAEiE,EAAE,EAAE,UAAU;UAAEN,KAAK,EAAE,UAAU;UAAEL,IAAI,EAAEzD;QAAS,CAAC,CACxD,CAACqE,GAAG,CAAC,CAAC;UAAED,EAAE;UAAEN,KAAK;UAAEL,IAAI,EAAEC;QAAK,CAAC,kBAC5B7C,OAAA;UAEIyD,OAAO,EAAEA,CAAA,KAAMrD,aAAa,CAACmD,EAAE,CAAE;UACjCrB,SAAS,EAAG,+DAA8D/B,UAAU,KAAKoD,EAAE,GACrF,+BAA+B,GAC/B,iCACD,EAAE;UAAAR,QAAA,gBAEP/C,OAAA,CAAC6C,IAAI;YAACX,SAAS,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5BtC,OAAA;YAAMkC,SAAS,EAAC,aAAa;YAAAa,QAAA,EAAEE;UAAK;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GARvCiB,EAAE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASH,CACX;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNtC,OAAA;QAAKkC,SAAS,EAAC,gGAAgG;QAAAa,QAAA,EAC1G,CACG;UAAEE,KAAK,EAAE,YAAY;UAAEP,KAAK,EAAE/B,SAAS;UAAE+C,MAAM,EAAE9C,YAAY;UAAE+C,OAAO,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU;QAAE,CAAC,EACrI;UAAEV,KAAK,EAAE,OAAO;UAAEP,KAAK,EAAErC,aAAa;UAAEqD,MAAM,EAAEpD,gBAAgB;UAAEqD,OAAO,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ;QAAE,CAAC,EAClH;UAAEV,KAAK,EAAE,QAAQ;UAAEP,KAAK,EAAEnC,cAAc;UAAEmD,MAAM,EAAElD,iBAAiB;UAAEmD,OAAO,EAAE,CAAC,KAAK,EAAE,aAAa,EAAE,SAAS,EAAE,UAAU;QAAE,CAAC,EAC7H;UAAEV,KAAK,EAAE,SAAS;UAAEP,KAAK,EAAEjC,eAAe;UAAEiD,MAAM,EAAEhD,kBAAkB;UAAEiD,OAAO,EAAE,CAAC,KAAK,EAAE,aAAa,EAAE,SAAS,EAAE,WAAW,EAAE,iBAAiB,EAAE,SAAS;QAAE,CAAC,CAClK,CAACH,GAAG,CAAC,CAAC;UAAEP,KAAK;UAAEP,KAAK;UAAEgB,MAAM;UAAEC;QAAQ,CAAC,EAAEC,GAAG,kBACzC5D,OAAA;UAAA+C,QAAA,gBACI/C,OAAA;YAAOkC,SAAS,EAAC,8CAA8C;YAAAa,QAAA,EAAEE;UAAK;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC/EtC,OAAA;YACI0C,KAAK,EAAEA,KAAM;YACbmB,QAAQ,EAAGC,CAAC,IAAKJ,MAAM,CAACI,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;YACxCR,SAAS,EAAC,qFAAqF;YAAAa,QAAA,EAE9FY,OAAO,CAACH,GAAG,CAACQ,GAAG,iBACZhE,OAAA;cAAkB0C,KAAK,EAAEsB,GAAI;cAAAjB,QAAA,EAAEiB,GAAG,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,GAAG,CAACG,KAAK,CAAC,CAAC;YAAC,GAA5DH,GAAG;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAkE,CACrF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,GAVHsB,GAAG;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAWR,CACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNtC,OAAA;QAAKkC,SAAS,EAAC,uCAAuC;QAAAa,QAAA,gBAClD/C,OAAA,CAACwC,QAAQ;UAACC,KAAK,EAAC,oBAAoB;UAACC,KAAK,EAAC,KAAK;UAACC,QAAQ,EAAC,YAAY;UAACC,IAAI,EAAEnD,UAAW;UAACqD,KAAK;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjGtC,OAAA,CAACwC,QAAQ;UAACC,KAAK,EAAC,eAAe;UAACC,KAAK,EAAC,KAAK;UAACC,QAAQ,EAAC,WAAW;UAACC,IAAI,EAAEzD;QAAS;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnFtC,OAAA,CAACwC,QAAQ;UAACC,KAAK,EAAC,cAAc;UAACC,KAAK,EAAC,KAAK;UAACC,QAAQ,EAAC,YAAY;UAACC,IAAI,EAAEjD;QAAY;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrF,CAAC,eAGNtC,OAAA;QAAKkC,SAAS,EAAC,uCAAuC;QAAAa,QAAA,gBAElD/C,OAAA;UAAKkC,SAAS,EAAC,yBAAyB;UAAAa,QAAA,GACnC5C,UAAU,KAAK,SAAS,iBACrBH,OAAA;YAAKkC,SAAS,EAAC,0DAA0D;YAAAa,QAAA,gBACrE/C,OAAA;cAAIkC,SAAS,EAAC,0CAA0C;cAAAa,QAAA,EAAC;YAAe;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7EtC,OAAA;cAAKkC,SAAS,EAAC,uCAAuC;cAAAa,QAAA,gBAClD/C,OAAA;gBAAKkC,SAAS,EAAC,uEAAuE;gBAAAa,QAAA,gBAClF/C,OAAA;kBAAKkC,SAAS,EAAC,uCAAuC;kBAAAa,QAAA,EAAC;gBAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAChEtC,OAAA;kBAAKkC,SAAS,EAAC,2BAA2B;kBAAAa,QAAA,EAAC;gBAAkB;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CAAC,eACNtC,OAAA;gBAAKkC,SAAS,EAAC,WAAW;gBAAAa,QAAA,gBACtB/C,OAAA;kBAAKkC,SAAS,EAAC,8DAA8D;kBAAAa,QAAA,gBACzE/C,OAAA;oBAAKkC,SAAS,EAAC,yBAAyB;oBAAAa,QAAA,gBACpC/C,OAAA,CAACL,WAAW;sBAACuC,SAAS,EAAC;oBAAwB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAClDtC,OAAA;sBAAA+C,QAAA,gBACI/C,OAAA;wBAAKkC,SAAS,EAAC,8BAA8B;wBAAAa,QAAA,EAAC;sBAAO;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC3DtC,OAAA;wBAAKkC,SAAS,EAAC,wBAAwB;wBAAAa,QAAA,EAAC;sBAAa;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACNtC,OAAA;oBAAKkC,SAAS,EAAC,mCAAmC;oBAAAa,QAAA,EAAC;kBAAG;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC,eACNtC,OAAA;kBAAKkC,SAAS,EAAC,4DAA4D;kBAAAa,QAAA,gBACvE/C,OAAA;oBAAKkC,SAAS,EAAC,yBAAyB;oBAAAa,QAAA,gBACpC/C,OAAA,CAACJ,OAAO;sBAACsC,SAAS,EAAC;oBAAsB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC5CtC,OAAA;sBAAA+C,QAAA,gBACI/C,OAAA;wBAAKkC,SAAS,EAAC,4BAA4B;wBAAAa,QAAA,EAAC;sBAAM;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACxDtC,OAAA;wBAAKkC,SAAS,EAAC,sBAAsB;wBAAAa,QAAA,EAAC;sBAAW;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACNtC,OAAA;oBAAKkC,SAAS,EAAC,iCAAiC;oBAAAa,QAAA,EAAC;kBAAE;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACR,EAEAnC,UAAU,KAAK,UAAU,iBACtBH,OAAA;YAAKkC,SAAS,EAAC,oEAAoE;YAAAa,QAAA,gBAC/E/C,OAAA;cAAIkC,SAAS,EAAC,qCAAqC;cAAAa,QAAA,EAAC;YAAuB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAC/EtB,cAAc,CAACK,QAAQ,CAACmC,GAAG,CAAC,CAACY,CAAC,EAAEC,CAAC,kBAC9BrE,OAAA,CAACgD,WAAW;cAASzB,UAAU,EAAE6C,CAAC,CAAC7C,UAAW;cAAC0B,KAAK,EAAEmB,CAAC,CAAC9C,IAAK;cAACF,KAAK,EAAEgD,CAAC,CAAChD,KAAM;cAACF,OAAO,EAAEkD,CAAC,CAAClD;YAAQ,GAA/EmD,CAAC;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAgF,CACtG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR,EAEAnC,UAAU,KAAK,SAAS,iBACrBH,OAAA;YAAKkC,SAAS,EAAC,0DAA0D;YAAAa,QAAA,gBACrE/C,OAAA;cAAIkC,SAAS,EAAC,0CAA0C;cAAAa,QAAA,EAAC;YAAqB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnFtC,OAAA;cAAKkC,SAAS,EAAC,uCAAuC;cAAAa,QAAA,EACjD/B,cAAc,CAACQ,OAAO,CAACgC,GAAG,CAAC,CAACc,CAAC,EAAED,CAAC,kBAC7BrE,OAAA;gBAAakC,SAAS,EAAC,wDAAwD;gBAAAa,QAAA,gBAC3E/C,OAAA;kBAAKkC,SAAS,EAAC,2BAA2B;kBAAAa,QAAA,gBACtC/C,OAAA;oBAAIkC,SAAS,EAAC,6BAA6B;oBAAAa,QAAA,EAAEuB,CAAC,CAAChD;kBAAI;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzDtC,OAAA,CAACZ,KAAK;oBAAC8C,SAAS,EAAC;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC,eACNtC,OAAA;kBAAKkC,SAAS,EAAC,oBAAoB;kBAAAa,QAAA,GAAEuB,CAAC,CAAC5C,aAAa,EAAC,GAAC;gBAAA;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC5DtC,OAAA;kBAAKkC,SAAS,EAAC,uBAAuB;kBAAAa,QAAA,GAAEuB,CAAC,CAAC7C,QAAQ,EAAC,WAAS;gBAAA;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAClEtC,OAAA;kBAAKkC,SAAS,EAAC,0CAA0C;kBAAAa,QAAA,eACrD/C,OAAA;oBAAKkC,SAAS,EAAC,8BAA8B;oBAACgB,KAAK,EAAE;sBAAEC,KAAK,EAAG,GAAEmB,CAAC,CAAC5C,aAAc;oBAAG;kBAAE;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5F,CAAC;cAAA,GATA+B,CAAC;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUN,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACR,EAEAnC,UAAU,KAAK,SAAS,iBACrBH,OAAA;YAAKkC,SAAS,EAAC,oEAAoE;YAAAa,QAAA,gBAC/E/C,OAAA;cAAIkC,SAAS,EAAC,0CAA0C;cAAAa,QAAA,EAAC;YAAsB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACnFtB,cAAc,CAACW,OAAO,CAAC6B,GAAG,CAAC,CAACe,CAAC,EAAEF,CAAC,kBAC7BrE,OAAA;cAAakC,SAAS,EAAC,qGAAqG;cAAAa,QAAA,gBACxH/C,OAAA;gBAAKkC,SAAS,EAAC,yBAAyB;gBAAAa,QAAA,gBACpC/C,OAAA;kBAAKkC,SAAS,EAAC,2BAA2B;kBAAAa,QAAA,eACtC/C,OAAA,CAACV,aAAa;oBAAC4C,SAAS,EAAC;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC,eACNtC,OAAA;kBAAA+C,QAAA,gBACI/C,OAAA;oBAAIkC,SAAS,EAAC,6BAA6B;oBAAAa,QAAA,EAAEwB,CAAC,CAACjD;kBAAI;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzDtC,OAAA;oBAAGkC,SAAS,EAAC,uBAAuB;oBAAAa,QAAA,GAAEwB,CAAC,CAAC9C,QAAQ,EAAC,oBAAkB;kBAAA;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNtC,OAAA;gBAAKkC,SAAS,EAAG,4CAA2CK,kBAAkB,CAACgC,CAAC,CAAC7C,aAAa,CAAE,EAAE;gBAAAqB,QAAA,GAC7FwB,CAAC,CAAC7C,aAAa,EAAC,GACrB;cAAA;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA,GAZA+B,CAAC;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaN,CACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR,EAEAnC,UAAU,KAAK,UAAU,iBACtBH,OAAA;YAAKkC,SAAS,EAAC,0DAA0D;YAAAa,QAAA,gBACrE/C,OAAA;cAAIkC,SAAS,EAAC,0CAA0C;cAAAa,QAAA,EAAC;YAAa;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3EtC,OAAA,CAACF,YAAY;cAAC4C,KAAK,EAAE7B,YAAa;cAACgD,QAAQ,EAAET,gBAAiB;cAAClB,SAAS,EAAC;YAAgC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3G,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAGNtC,OAAA;UAAKkC,SAAS,EAAC,0DAA0D;UAAAa,QAAA,gBACrE/C,OAAA;YAAIkC,SAAS,EAAC,kEAAkE;YAAAa,QAAA,gBAC5E/C,OAAA,CAACN,KAAK;cAACwC,SAAS,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,oBACjC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLtC,OAAA;YAAKkC,SAAS,EAAC,WAAW;YAAAa,QAAA,EACrB/B,cAAc,CAACY,cAAc,CAAC4B,GAAG,CAAC,CAACgB,CAAC,EAAEH,CAAC,kBACpCrE,OAAA;cAAakC,SAAS,EAAC,gFAAgF;cAAAa,QAAA,GAClGd,aAAa,CAACuC,CAAC,CAACzC,MAAM,CAAC,eACxB/B,OAAA;gBAAKkC,SAAS,EAAC,QAAQ;gBAAAa,QAAA,gBACnB/C,OAAA;kBAAKkC,SAAS,EAAC,2BAA2B;kBAAAa,QAAA,EAAEyB,CAAC,CAAC1C;gBAAO;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5DtC,OAAA;kBAAKkC,SAAS,EAAC,uBAAuB;kBAAAa,QAAA,GAAEyB,CAAC,CAAC3C,IAAI,EAAC,UAAG,EAAC2C,CAAC,CAACxC,IAAI;gBAAA;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,eACNtC,OAAA;gBAAKkC,SAAS,EAAG,6CAA4CsC,CAAC,CAACzC,MAAM,KAAK,SAAS,GAC7E,6BAA6B,GAC7ByC,CAAC,CAACzC,MAAM,KAAK,QAAQ,GACjB,yBAAyB,GACzB,+BACL,EAAE;gBAAAgB,QAAA,EACFyB,CAAC,CAACzC;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA,GAbA+B,CAAC;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAcN,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACpC,EAAA,CA5SID,cAAc;AAAAwE,EAAA,GAAdxE,cAAc;AA8SpB,eAAeA,cAAc;AAAC,IAAAwE,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
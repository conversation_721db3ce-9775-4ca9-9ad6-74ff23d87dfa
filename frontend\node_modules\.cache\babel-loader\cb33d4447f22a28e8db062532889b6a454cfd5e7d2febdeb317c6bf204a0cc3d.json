{"ast": null, "code": "/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M19 19H6.828a2 2 0 0 0-1.414.586l-2.202 2.202A.7.7 0 0 1 2 21.286V5a2 2 0 0 1 1.184-1.826\",\n  key: \"1wyg69\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"M8.656 3H20a2 2 0 0 1 2 2v11.344\",\n  key: \"mhl4k6\"\n}]];\nconst MessageSquareOff = createLucideIcon(\"message-square-off\", __iconNode);\nexport { __iconNode, MessageSquareOff as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "MessageSquareOff", "createLucideIcon"], "sources": ["D:\\techvritti\\Collegemanagement\\frontend\\node_modules\\lucide-react\\src\\icons\\message-square-off.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M19 19H6.828a2 2 0 0 0-1.414.586l-2.202 2.202A.7.7 0 0 1 2 21.286V5a2 2 0 0 1 1.184-1.826',\n      key: '1wyg69',\n    },\n  ],\n  ['path', { d: 'm2 2 20 20', key: '1ooewy' }],\n  ['path', { d: 'M8.656 3H20a2 2 0 0 1 2 2v11.344', key: 'mhl4k6' }],\n];\n\n/**\n * @component @name MessageSquareOff\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTkgMTlINi44MjhhMiAyIDAgMCAwLTEuNDE0LjU4NmwtMi4yMDIgMi4yMDJBLjcuNyAwIDAgMSAyIDIxLjI4NlY1YTIgMiAwIDAgMSAxLjE4NC0xLjgyNiIgLz4KICA8cGF0aCBkPSJtMiAyIDIwIDIwIiAvPgogIDxwYXRoIGQ9Ik04LjY1NiAzSDIwYTIgMiAwIDAgMSAyIDJ2MTEuMzQ0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/message-square-off\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MessageSquareOff = createLucideIcon('message-square-off', __iconNode);\n\nexport default MessageSquareOff;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CACE,QACA;EACEC,CAAA,EAAG;EACHC,GAAA,EAAK;AAAA,EAET,EACA,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAcC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAoCC,GAAA,EAAK;AAAA,CAAU,EACnE;AAaA,MAAMC,gBAAA,GAAmBC,gBAAA,CAAiB,sBAAsBJ,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
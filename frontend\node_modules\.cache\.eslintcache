[{"D:\\techvritti\\Collegemanagement\\frontend\\src\\index.js": "1", "D:\\techvritti\\Collegemanagement\\frontend\\src\\App.js": "2", "D:\\techvritti\\Collegemanagement\\frontend\\src\\theme.js": "3", "D:\\techvritti\\Collegemanagement\\frontend\\src\\redux\\store.js": "4", "D:\\techvritti\\Collegemanagement\\frontend\\src\\routes\\CollegeRoutes.jsx": "5", "D:\\techvritti\\Collegemanagement\\frontend\\src\\components\\Login.jsx": "6", "D:\\techvritti\\Collegemanagement\\frontend\\src\\routes\\StudentRoutes.jsx": "7", "D:\\techvritti\\Collegemanagement\\frontend\\src\\components\\TailwindTest.jsx": "8", "D:\\techvritti\\Collegemanagement\\frontend\\src\\routes\\AdminRoutes.jsx": "9", "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Faculty\\Home.jsx": "10", "D:\\techvritti\\Collegemanagement\\frontend\\src\\redux\\reducers.js": "11", "D:\\techvritti\\Collegemanagement\\frontend\\src\\services\\mockData.js": "12", "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\College\\CSE.jsx": "13", "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\College\\IOT.jsx": "14", "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\College\\Department.jsx": "15", "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\College\\ISE.jsx": "16", "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\College\\CSD.jsx": "17", "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\College\\CollegeID.jsx": "18", "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\College\\Mech.jsx": "19", "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\College\\Home.jsx": "20", "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\College\\AIML.jsx": "21", "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\College\\Civil.jsx": "22", "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Student\\Profile.jsx": "23", "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Student\\Schedule.jsx": "24", "D:\\techvritti\\Collegemanagement\\frontend\\src\\components\\Layout\\StudentLayout.jsx": "25", "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Student\\Home.jsx": "26", "D:\\techvritti\\Collegemanagement\\frontend\\src\\components\\Layout\\AdminLayout.jsx": "27", "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Student\\Certificate.jsx": "28", "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Student\\MyCourses.jsx": "29", "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Admin\\AssignBatchToTrainers.jsx": "30", "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Admin\\College.jsx": "31", "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Admin\\ViewUserReports.jsx": "32", "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Admin\\Dashboard.jsx": "33", "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Admin\\UploadCourseWithTrainer.jsx": "34", "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Admin\\Trainer.jsx": "35", "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Admin\\CreateBatch.jsx": "36", "D:\\techvritti\\Collegemanagement\\frontend\\src\\components\\Notice.jsx": "37", "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Admin\\ViewBatchWiseReports.jsx": "38", "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Admin\\UploadCertificate.jsx": "39", "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Faculty\\Material.jsx": "40", "D:\\techvritti\\Collegemanagement\\frontend\\src\\components\\Navbar.jsx": "41", "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Faculty\\Marks.jsx": "42", "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Faculty\\Timetable.jsx": "43", "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Faculty\\Profile.jsx": "44", "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Faculty\\Student.jsx": "45", "D:\\techvritti\\Collegemanagement\\frontend\\src\\redux\\action.js": "46", "D:\\techvritti\\Collegemanagement\\frontend\\src\\components\\Layout\\CollegeLayout.jsx": "47", "D:\\techvritti\\Collegemanagement\\frontend\\src\\baseUrl.js": "48", "D:\\techvritti\\Collegemanagement\\frontend\\src\\redux\\actions.js": "49", "D:\\techvritti\\Collegemanagement\\frontend\\src\\components\\Heading.jsx": "50", "D:\\techvritti\\Collegemanagement\\frontend\\src\\api\\utils\\errorHandler.js": "51", "D:\\techvritti\\Collegemanagement\\frontend\\src\\api\\services\\authService.js": "52", "D:\\techvritti\\Collegemanagement\\frontend\\src\\api\\config\\endpoints.js": "53", "D:\\techvritti\\Collegemanagement\\frontend\\src\\api\\config\\apiConfig.js": "54", "D:\\techvritti\\Collegemanagement\\frontend\\src\\api\\config\\interceptors.js": "55", "D:\\techvritti\\Collegemanagement\\frontend\\src\\store\\store.js": "56", "D:\\techvritti\\Collegemanagement\\frontend\\src\\store\\slices\\uiSlice.js": "57", "D:\\techvritti\\Collegemanagement\\frontend\\src\\store\\slices\\authSlice.js": "58", "D:\\techvritti\\Collegemanagement\\frontend\\src\\store\\slices\\productSlice.js": "59", "D:\\techvritti\\Collegemanagement\\frontend\\src\\store\\slices\\userSlice.js": "60", "D:\\techvritti\\Collegemanagement\\frontend\\src\\store\\middleware\\apiMiddleware.js": "61", "D:\\techvritti\\Collegemanagement\\frontend\\src\\store\\middleware\\errorMiddleware.js": "62", "D:\\techvritti\\Collegemanagement\\frontend\\src\\routes\\ProtectedRoute.jsx": "63", "D:\\techvritti\\Collegemanagement\\frontend\\src\\routes\\RequireRole.jsx": "64", "D:\\techvritti\\Collegemanagement\\frontend\\src\\routes\\RoleRedirect.jsx": "65", "D:\\techvritti\\Collegemanagement\\frontend\\src\\hooks\\useStudentApi.js": "66", "D:\\techvritti\\Collegemanagement\\frontend\\src\\api\\services\\studentService.js": "67", "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Student\\AttendancePage.jsx": "68"}, {"size": 210, "mtime": 1754544120873, "results": "69", "hashOfConfig": "70"}, {"size": 2781, "mtime": 1754976073082, "results": "71", "hashOfConfig": "70"}, {"size": 557, "mtime": 1754544120879, "results": "72", "hashOfConfig": "70"}, {"size": 256, "mtime": 1754544120875, "results": "73", "hashOfConfig": "70"}, {"size": 1169, "mtime": 1754544120877, "results": "74", "hashOfConfig": "70"}, {"size": 8405, "mtime": 1754976251656, "results": "75", "hashOfConfig": "70"}, {"size": 1108, "mtime": 1754997134323, "results": "76", "hashOfConfig": "70"}, {"size": 1340, "mtime": 1754556672153, "results": "77", "hashOfConfig": "70"}, {"size": 1851, "mtime": 1754544120877, "results": "78", "hashOfConfig": "70"}, {"size": 4933, "mtime": 1754976676918, "results": "79", "hashOfConfig": "70"}, {"size": 409, "mtime": 1754544120875, "results": "80", "hashOfConfig": "70"}, {"size": 10897, "mtime": 1754571244291, "results": "81", "hashOfConfig": "70"}, {"size": 22899, "mtime": 1754544120831, "results": "82", "hashOfConfig": "70"}, {"size": 20019, "mtime": 1754544120834, "results": "83", "hashOfConfig": "70"}, {"size": 2122, "mtime": 1754544120833, "results": "84", "hashOfConfig": "70"}, {"size": 20080, "mtime": 1754544120834, "results": "85", "hashOfConfig": "70"}, {"size": 19530, "mtime": 1754544120830, "results": "86", "hashOfConfig": "70"}, {"size": 1181, "mtime": 1754544120832, "results": "87", "hashOfConfig": "70"}, {"size": 19490, "mtime": 1754544120835, "results": "88", "hashOfConfig": "70"}, {"size": 7437, "mtime": 1754544120834, "results": "89", "hashOfConfig": "70"}, {"size": 20606, "mtime": 1754544120828, "results": "90", "hashOfConfig": "70"}, {"size": 19546, "mtime": 1754544120831, "results": "91", "hashOfConfig": "70"}, {"size": 28143, "mtime": 1754983813527, "results": "92", "hashOfConfig": "70"}, {"size": 16228, "mtime": 1754544120845, "results": "93", "hashOfConfig": "70"}, {"size": 6372, "mtime": 1754996909874, "results": "94", "hashOfConfig": "70"}, {"size": 22241, "mtime": 1754996596193, "results": "95", "hashOfConfig": "70"}, {"size": 9213, "mtime": 1754976087640, "results": "96", "hashOfConfig": "70"}, {"size": 2286, "mtime": 1754544120840, "results": "97", "hashOfConfig": "70"}, {"size": 58570, "mtime": 1754544120842, "results": "98", "hashOfConfig": "70"}, {"size": 16792, "mtime": 1754544120821, "results": "99", "hashOfConfig": "70"}, {"size": 12527, "mtime": 1754544120822, "results": "100", "hashOfConfig": "70"}, {"size": 18959, "mtime": 1754544120828, "results": "101", "hashOfConfig": "70"}, {"size": 28172, "mtime": 1754974684752, "results": "102", "hashOfConfig": "70"}, {"size": 15607, "mtime": 1754544120827, "results": "103", "hashOfConfig": "70"}, {"size": 15954, "mtime": 1754544120826, "results": "104", "hashOfConfig": "70"}, {"size": 16173, "mtime": 1754544120823, "results": "105", "hashOfConfig": "70"}, {"size": 9543, "mtime": 1754544120851, "results": "106", "hashOfConfig": "70"}, {"size": 18996, "mtime": 1754544120828, "results": "107", "hashOfConfig": "70"}, {"size": 18018, "mtime": 1754544120827, "results": "108", "hashOfConfig": "70"}, {"size": 5175, "mtime": 1754544120838, "results": "109", "hashOfConfig": "70"}, {"size": 1076, "mtime": 1754544120851, "results": "110", "hashOfConfig": "70"}, {"size": 9058, "mtime": 1754544120837, "results": "111", "hashOfConfig": "70"}, {"size": 5670, "mtime": 1754544120840, "results": "112", "hashOfConfig": "70"}, {"size": 5309, "mtime": 1754544120838, "results": "113", "hashOfConfig": "70"}, {"size": 4421, "mtime": 1754544120838, "results": "114", "hashOfConfig": "70"}, {"size": 86, "mtime": 1754544120874, "results": "115", "hashOfConfig": "70"}, {"size": 8618, "mtime": 1754544120849, "results": "116", "hashOfConfig": "70"}, {"size": 6539, "mtime": 1754980136700, "results": "117", "hashOfConfig": "70"}, {"size": 228, "mtime": 1754544120874, "results": "118", "hashOfConfig": "70"}, {"size": 295, "mtime": 1754544120847, "results": "119", "hashOfConfig": "70"}, {"size": 6309, "mtime": 1754912224234, "results": "120", "hashOfConfig": "70"}, {"size": 13623, "mtime": 1754976209575, "results": "121", "hashOfConfig": "70"}, {"size": 272, "mtime": 1754911915362, "results": "122", "hashOfConfig": "70"}, {"size": 2705, "mtime": 1754915480639, "results": "123", "hashOfConfig": "70"}, {"size": 14055, "mtime": 1754916649354, "results": "124", "hashOfConfig": "70"}, {"size": 980, "mtime": 1754913932093, "results": "125", "hashOfConfig": "70"}, {"size": 0, "mtime": 1754910402645, "results": "126", "hashOfConfig": "70"}, {"size": 4875, "mtime": 1754913808636, "results": "127", "hashOfConfig": "70"}, {"size": 0, "mtime": 1754910392359, "results": "128", "hashOfConfig": "70"}, {"size": 0, "mtime": 1754910383455, "results": "129", "hashOfConfig": "70"}, {"size": 0, "mtime": 1754910423912, "results": "130", "hashOfConfig": "70"}, {"size": 0, "mtime": 1754910433209, "results": "131", "hashOfConfig": "70"}, {"size": 424, "mtime": 1754976053802, "results": "132", "hashOfConfig": "70"}, {"size": 1144, "mtime": 1754976058917, "results": "133", "hashOfConfig": "70"}, {"size": 558, "mtime": 1754976064026, "results": "134", "hashOfConfig": "70"}, {"size": 11358, "mtime": 1754981806802, "results": "135", "hashOfConfig": "70"}, {"size": 17572, "mtime": 1754981770426, "results": "136", "hashOfConfig": "70"}, {"size": 44784, "mtime": 1755001012467, "results": "137", "hashOfConfig": "70"}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1k9y7yd", {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\techvritti\\Collegemanagement\\frontend\\src\\index.js", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\App.js", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\theme.js", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\redux\\store.js", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\routes\\CollegeRoutes.jsx", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\components\\Login.jsx", ["342"], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\routes\\StudentRoutes.jsx", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\components\\TailwindTest.jsx", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\routes\\AdminRoutes.jsx", ["343", "344", "345", "346", "347", "348"], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Faculty\\Home.jsx", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\redux\\reducers.js", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\services\\mockData.js", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\College\\CSE.jsx", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\College\\IOT.jsx", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\College\\Department.jsx", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\College\\ISE.jsx", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\College\\CSD.jsx", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\College\\CollegeID.jsx", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\College\\Mech.jsx", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\College\\Home.jsx", ["349"], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\College\\AIML.jsx", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\College\\Civil.jsx", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Student\\Profile.jsx", ["350"], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Student\\Schedule.jsx", ["351"], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\components\\Layout\\StudentLayout.jsx", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Student\\Home.jsx", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\components\\Layout\\AdminLayout.jsx", ["352", "353", "354", "355", "356"], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Student\\Certificate.jsx", ["357"], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Student\\MyCourses.jsx", ["358"], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Admin\\AssignBatchToTrainers.jsx", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Admin\\College.jsx", ["359", "360", "361"], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Admin\\ViewUserReports.jsx", ["362"], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Admin\\Dashboard.jsx", ["363", "364", "365", "366", "367", "368", "369", "370", "371", "372"], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Admin\\UploadCourseWithTrainer.jsx", ["373", "374"], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Admin\\Trainer.jsx", ["375", "376", "377"], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Admin\\CreateBatch.jsx", ["378"], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\components\\Notice.jsx", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Admin\\ViewBatchWiseReports.jsx", ["379"], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Admin\\UploadCertificate.jsx", ["380", "381", "382"], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Faculty\\Material.jsx", ["383", "384"], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\components\\Navbar.jsx", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Faculty\\Marks.jsx", ["385", "386", "387", "388", "389"], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Faculty\\Timetable.jsx", ["390", "391", "392", "393"], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Faculty\\Profile.jsx", ["394", "395", "396", "397"], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Faculty\\Student.jsx", ["398", "399", "400"], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\redux\\action.js", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\components\\Layout\\CollegeLayout.jsx", ["401"], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\baseUrl.js", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\redux\\actions.js", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\components\\Heading.jsx", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\api\\utils\\errorHandler.js", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\api\\services\\authService.js", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\api\\config\\endpoints.js", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\api\\config\\apiConfig.js", ["402"], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\api\\config\\interceptors.js", ["403", "404", "405", "406", "407"], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\store\\store.js", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\store\\slices\\uiSlice.js", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\store\\slices\\authSlice.js", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\store\\slices\\productSlice.js", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\store\\slices\\userSlice.js", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\store\\middleware\\apiMiddleware.js", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\store\\middleware\\errorMiddleware.js", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\routes\\ProtectedRoute.jsx", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\routes\\RequireRole.jsx", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\routes\\RoleRedirect.jsx", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\hooks\\useStudentApi.js", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\api\\services\\studentService.js", [], [], "D:\\techvritti\\Collegemanagement\\frontend\\src\\Screens\\Student\\AttendancePage.jsx", [], [], {"ruleId": "408", "severity": 1, "message": "409", "line": 171, "column": 13, "nodeType": "410", "endLine": 171, "endColumn": 79}, {"ruleId": "411", "severity": 1, "message": "412", "line": 14, "column": 7, "nodeType": "413", "messageId": "414", "endLine": 14, "endColumn": 15}, {"ruleId": "411", "severity": 1, "message": "415", "line": 15, "column": 7, "nodeType": "413", "messageId": "414", "endLine": 15, "endColumn": 14}, {"ruleId": "411", "severity": 1, "message": "416", "line": 16, "column": 7, "nodeType": "413", "messageId": "414", "endLine": 16, "endColumn": 14}, {"ruleId": "411", "severity": 1, "message": "417", "line": 17, "column": 7, "nodeType": "413", "messageId": "414", "endLine": 17, "endColumn": 15}, {"ruleId": "411", "severity": 1, "message": "418", "line": 18, "column": 7, "nodeType": "413", "messageId": "414", "endLine": 18, "endColumn": 17}, {"ruleId": "411", "severity": 1, "message": "419", "line": 19, "column": 7, "nodeType": "413", "messageId": "414", "endLine": 19, "endColumn": 15}, {"ruleId": "411", "severity": 1, "message": "420", "line": 3, "column": 68, "nodeType": "413", "messageId": "414", "endLine": 3, "endColumn": 78}, {"ruleId": "411", "severity": 1, "message": "421", "line": 39, "column": 10, "nodeType": "413", "messageId": "414", "endLine": 39, "endColumn": 20}, {"ruleId": "411", "severity": 1, "message": "422", "line": 91, "column": 20, "nodeType": "413", "messageId": "414", "endLine": 91, "endColumn": 31}, {"ruleId": "411", "severity": 1, "message": "423", "line": 2, "column": 40, "nodeType": "413", "messageId": "414", "endLine": 2, "endColumn": 46}, {"ruleId": "411", "severity": 1, "message": "424", "line": 2, "column": 48, "nodeType": "413", "messageId": "414", "endLine": 2, "endColumn": 58}, {"ruleId": "411", "severity": 1, "message": "425", "line": 2, "column": 60, "nodeType": "413", "messageId": "414", "endLine": 2, "endColumn": 70}, {"ruleId": "411", "severity": 1, "message": "426", "line": 2, "column": 90, "nodeType": "413", "messageId": "414", "endLine": 2, "endColumn": 100}, {"ruleId": "411", "severity": 1, "message": "427", "line": 2, "column": 102, "nodeType": "413", "messageId": "414", "endLine": 2, "endColumn": 109}, {"ruleId": "411", "severity": 1, "message": "428", "line": 2, "column": 10, "nodeType": "413", "messageId": "414", "endLine": 2, "endColumn": 17}, {"ruleId": "411", "severity": 1, "message": "429", "line": 374, "column": 9, "nodeType": "413", "messageId": "414", "endLine": 374, "endColumn": 22}, {"ruleId": "411", "severity": 1, "message": "430", "line": 1, "column": 27, "nodeType": "413", "messageId": "414", "endLine": 1, "endColumn": 36}, {"ruleId": "411", "severity": 1, "message": "431", "line": 3, "column": 3, "nodeType": "413", "messageId": "414", "endLine": 3, "endColumn": 9}, {"ruleId": "411", "severity": 1, "message": "432", "line": 5, "column": 3, "nodeType": "413", "messageId": "414", "endLine": 5, "endColumn": 9}, {"ruleId": "411", "severity": 1, "message": "433", "line": 12, "column": 3, "nodeType": "413", "messageId": "414", "endLine": 12, "endColumn": 9}, {"ruleId": "411", "severity": 1, "message": "430", "line": 1, "column": 27, "nodeType": "413", "messageId": "414", "endLine": 1, "endColumn": 36}, {"ruleId": "411", "severity": 1, "message": "434", "line": 3, "column": 3, "nodeType": "413", "messageId": "414", "endLine": 3, "endColumn": 10}, {"ruleId": "411", "severity": 1, "message": "423", "line": 4, "column": 3, "nodeType": "413", "messageId": "414", "endLine": 4, "endColumn": 9}, {"ruleId": "411", "severity": 1, "message": "435", "line": 6, "column": 3, "nodeType": "413", "messageId": "414", "endLine": 6, "endColumn": 11}, {"ruleId": "411", "severity": 1, "message": "436", "line": 7, "column": 3, "nodeType": "413", "messageId": "414", "endLine": 7, "endColumn": 14}, {"ruleId": "411", "severity": 1, "message": "428", "line": 8, "column": 3, "nodeType": "413", "messageId": "414", "endLine": 8, "endColumn": 10}, {"ruleId": "411", "severity": 1, "message": "433", "line": 14, "column": 3, "nodeType": "413", "messageId": "414", "endLine": 14, "endColumn": 9}, {"ruleId": "411", "severity": 1, "message": "424", "line": 20, "column": 3, "nodeType": "413", "messageId": "414", "endLine": 20, "endColumn": 13}, {"ruleId": "411", "severity": 1, "message": "437", "line": 22, "column": 3, "nodeType": "413", "messageId": "414", "endLine": 22, "endColumn": 16}, {"ruleId": "411", "severity": 1, "message": "438", "line": 25, "column": 3, "nodeType": "413", "messageId": "414", "endLine": 25, "endColumn": 15}, {"ruleId": "411", "severity": 1, "message": "439", "line": 5, "column": 3, "nodeType": "413", "messageId": "414", "endLine": 5, "endColumn": 11}, {"ruleId": "411", "severity": 1, "message": "433", "line": 13, "column": 3, "nodeType": "413", "messageId": "414", "endLine": 13, "endColumn": 9}, {"ruleId": "411", "severity": 1, "message": "430", "line": 1, "column": 35, "nodeType": "413", "messageId": "414", "endLine": 1, "endColumn": 44}, {"ruleId": "411", "severity": 1, "message": "440", "line": 5, "column": 3, "nodeType": "413", "messageId": "414", "endLine": 5, "endColumn": 10}, {"ruleId": "411", "severity": 1, "message": "428", "line": 7, "column": 3, "nodeType": "413", "messageId": "414", "endLine": 7, "endColumn": 10}, {"ruleId": "411", "severity": 1, "message": "430", "line": 1, "column": 27, "nodeType": "413", "messageId": "414", "endLine": 1, "endColumn": 36}, {"ruleId": "411", "severity": 1, "message": "433", "line": 14, "column": 3, "nodeType": "413", "messageId": "414", "endLine": 14, "endColumn": 9}, {"ruleId": "411", "severity": 1, "message": "433", "line": 14, "column": 3, "nodeType": "413", "messageId": "414", "endLine": 14, "endColumn": 9}, {"ruleId": "411", "severity": 1, "message": "420", "line": 17, "column": 3, "nodeType": "413", "messageId": "414", "endLine": 17, "endColumn": 13}, {"ruleId": "411", "severity": 1, "message": "441", "line": 19, "column": 3, "nodeType": "413", "messageId": "414", "endLine": 19, "endColumn": 11}, {"ruleId": "411", "severity": 1, "message": "442", "line": 8, "column": 10, "nodeType": "413", "messageId": "414", "endLine": 8, "endColumn": 20}, {"ruleId": "411", "severity": 1, "message": "443", "line": 9, "column": 23, "nodeType": "413", "messageId": "414", "endLine": 9, "endColumn": 34}, {"ruleId": "411", "severity": 1, "message": "442", "line": 5, "column": 10, "nodeType": "413", "messageId": "414", "endLine": 5, "endColumn": 20}, {"ruleId": "411", "severity": 1, "message": "443", "line": 6, "column": 23, "nodeType": "413", "messageId": "414", "endLine": 6, "endColumn": 34}, {"ruleId": "411", "severity": 1, "message": "444", "line": 19, "column": 11, "nodeType": "413", "messageId": "414", "endLine": 19, "endColumn": 18}, {"ruleId": "411", "severity": 1, "message": "444", "line": 48, "column": 11, "nodeType": "413", "messageId": "414", "endLine": 48, "endColumn": 18}, {"ruleId": "411", "severity": 1, "message": "444", "line": 69, "column": 11, "nodeType": "413", "messageId": "414", "endLine": 69, "endColumn": 18}, {"ruleId": "411", "severity": 1, "message": "442", "line": 6, "column": 10, "nodeType": "413", "messageId": "414", "endLine": 6, "endColumn": 20}, {"ruleId": "411", "severity": 1, "message": "443", "line": 7, "column": 23, "nodeType": "413", "messageId": "414", "endLine": 7, "endColumn": 34}, {"ruleId": "411", "severity": 1, "message": "444", "line": 22, "column": 11, "nodeType": "413", "messageId": "414", "endLine": 22, "endColumn": 18}, {"ruleId": "411", "severity": 1, "message": "444", "line": 49, "column": 11, "nodeType": "413", "messageId": "414", "endLine": 49, "endColumn": 18}, {"ruleId": "411", "severity": 1, "message": "442", "line": 3, "column": 10, "nodeType": "413", "messageId": "414", "endLine": 3, "endColumn": 20}, {"ruleId": "411", "severity": 1, "message": "444", "line": 19, "column": 11, "nodeType": "413", "messageId": "414", "endLine": 19, "endColumn": 18}, {"ruleId": "411", "severity": 1, "message": "444", "line": 44, "column": 11, "nodeType": "413", "messageId": "414", "endLine": 44, "endColumn": 18}, {"ruleId": "411", "severity": 1, "message": "444", "line": 63, "column": 11, "nodeType": "413", "messageId": "414", "endLine": 63, "endColumn": 18}, {"ruleId": "411", "severity": 1, "message": "442", "line": 4, "column": 10, "nodeType": "413", "messageId": "414", "endLine": 4, "endColumn": 20}, {"ruleId": "411", "severity": 1, "message": "443", "line": 6, "column": 23, "nodeType": "413", "messageId": "414", "endLine": 6, "endColumn": 34}, {"ruleId": "411", "severity": 1, "message": "444", "line": 40, "column": 11, "nodeType": "413", "messageId": "414", "endLine": 40, "endColumn": 18}, {"ruleId": "411", "severity": 1, "message": "420", "line": 2, "column": 53, "nodeType": "413", "messageId": "414", "endLine": 2, "endColumn": 63}, {"ruleId": "411", "severity": 1, "message": "442", "line": 5, "column": 10, "nodeType": "413", "messageId": "414", "endLine": 5, "endColumn": 20}, {"ruleId": "411", "severity": 1, "message": "445", "line": 3, "column": 8, "nodeType": "413", "messageId": "414", "endLine": 3, "endColumn": 13}, {"ruleId": "411", "severity": 1, "message": "446", "line": 105, "column": 33, "nodeType": "413", "messageId": "414", "endLine": 105, "endColumn": 37}, {"ruleId": "447", "severity": 1, "message": "448", "line": 276, "column": 5, "nodeType": "449", "messageId": "450", "endLine": 289, "endColumn": 6}, {"ruleId": "411", "severity": 1, "message": "451", "line": 370, "column": 7, "nodeType": "413", "messageId": "414", "endLine": 370, "endColumn": 24}, {"ruleId": "452", "severity": 1, "message": "453", "line": 453, "column": 1, "nodeType": "454", "endLine": 459, "endColumn": 3}, "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "no-unused-vars", "'Students' is assigned a value but never used.", "Identifier", "unusedVar", "'Faculty' is assigned a value but never used.", "'Courses' is assigned a value but never used.", "'Schedule' is assigned a value but never used.", "'Attendance' is assigned a value but never used.", "'Settings' is assigned a value but never used.", "'FiDownload' is defined but never used.", "'profilePic' is assigned a value but never used.", "'setSchedule' is assigned a value but never used.", "'FiBook' is defined but never used.", "'FiCalendar' is defined but never used.", "'FiSettings' is defined but never used.", "'FiBookOpen' is defined but never used.", "'FiClock' is defined but never used.", "'FiAward' is defined but never used.", "'handleViewPdf' is assigned a value but never used.", "'useEffect' is defined but never used.", "'FiHome' is defined but never used.", "'FiHash' is defined but never used.", "'FiSave' is defined but never used.", "'FiUsers' is defined but never used.", "'FiUpload' is defined but never used.", "'FiBarChart2' is defined but never used.", "'FiCheckCircle' is defined but never used.", "'FiTrendingUp' is defined but never used.", "'FiLayers' is defined but never used.", "'FiPhone' is defined but never used.", "'FiMapPin' is defined but never used.", "'baseApiURL' is defined but never used.", "'authService' is defined but never used.", "'headers' is assigned a value but never used.", "'axios' is defined but never used.", "'data' is assigned a value but never used.", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "'generateRequestId' is assigned a value but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration"]
{"ast": null, "code": "/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"20\",\n  height: \"8\",\n  x: \"2\",\n  y: \"14\",\n  rx: \"2\",\n  key: \"w68u3i\"\n}], [\"path\", {\n  d: \"M6.01 18H6\",\n  key: \"19vcac\"\n}], [\"path\", {\n  d: \"M10.01 18H10\",\n  key: \"uamcmx\"\n}], [\"path\", {\n  d: \"M15 10v4\",\n  key: \"qjz1xs\"\n}], [\"path\", {\n  d: \"M17.84 7.17a4 4 0 0 0-5.66 0\",\n  key: \"1rif40\"\n}], [\"path\", {\n  d: \"M20.66 4.34a8 8 0 0 0-11.31 0\",\n  key: \"6a5xfq\"\n}]];\nconst Router = createLucideIcon(\"router\", __iconNode);\nexport { __iconNode, Router as default };", "map": {"version": 3, "names": ["__iconNode", "width", "height", "x", "y", "rx", "key", "d", "Router", "createLucideIcon"], "sources": ["D:\\techvritti\\Collegemanagement\\frontend\\node_modules\\lucide-react\\src\\icons\\router.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '20', height: '8', x: '2', y: '14', rx: '2', key: 'w68u3i' }],\n  ['path', { d: 'M6.01 18H6', key: '19vcac' }],\n  ['path', { d: 'M10.01 18H10', key: 'uamcmx' }],\n  ['path', { d: 'M15 10v4', key: 'qjz1xs' }],\n  ['path', { d: 'M17.84 7.17a4 4 0 0 0-5.66 0', key: '1rif40' }],\n  ['path', { d: 'M20.66 4.34a8 8 0 0 0-11.31 0', key: '6a5xfq' }],\n];\n\n/**\n * @component @name Router\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iOCIgeD0iMiIgeT0iMTQiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik02LjAxIDE4SDYiIC8+CiAgPHBhdGggZD0iTTEwLjAxIDE4SDEwIiAvPgogIDxwYXRoIGQ9Ik0xNSAxMHY0IiAvPgogIDxwYXRoIGQ9Ik0xNy44NCA3LjE3YTQgNCAwIDAgMC01LjY2IDAiIC8+CiAgPHBhdGggZD0iTTIwLjY2IDQuMzRhOCA4IDAgMCAwLTExLjMxIDAiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/router\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Router = createLucideIcon('router', __iconNode);\n\nexport default Router;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAMC,MAAA,EAAQ;EAAKC,CAAA,EAAG;EAAKC,CAAA,EAAG;EAAMC,EAAA,EAAI;EAAKC,GAAA,EAAK;AAAA,CAAU,GAC9E,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAcD,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAgBD,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAYD,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAgCD,GAAA,EAAK;AAAA,CAAU,GAC7D,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAiCD,GAAA,EAAK;AAAA,CAAU,EAChE;AAaA,MAAME,MAAA,GAASC,gBAAA,CAAiB,UAAUT,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
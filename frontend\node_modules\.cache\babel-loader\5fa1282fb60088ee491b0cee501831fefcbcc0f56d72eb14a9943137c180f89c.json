{"ast": null, "code": "/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"9\",\n  cy: \"12\",\n  r: \"3\",\n  key: \"u3jwor\"\n}], [\"rect\", {\n  width: \"20\",\n  height: \"14\",\n  x: \"2\",\n  y: \"5\",\n  rx: \"7\",\n  key: \"g7kal2\"\n}]];\nconst ToggleLeft = createLucideIcon(\"toggle-left\", __iconNode);\nexport { __iconNode, ToggleLeft as default };", "map": {"version": 3, "names": ["__iconNode", "cx", "cy", "r", "key", "width", "height", "x", "y", "rx", "ToggleLeft", "createLucideIcon"], "sources": ["D:\\techvritti\\Collegemanagement\\frontend\\node_modules\\lucide-react\\src\\icons\\toggle-left.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '9', cy: '12', r: '3', key: 'u3jwor' }],\n  ['rect', { width: '20', height: '14', x: '2', y: '5', rx: '7', key: 'g7kal2' }],\n];\n\n/**\n * @component @name ToggleLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSI5IiBjeT0iMTIiIHI9IjMiIC8+CiAgPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjE0IiB4PSIyIiB5PSI1IiByeD0iNyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/toggle-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ToggleLeft = createLucideIcon('toggle-left', __iconNode);\n\nexport default ToggleLeft;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,UAAU;EAAEC,EAAA,EAAI;EAAKC,EAAA,EAAI;EAAMC,CAAA,EAAG;EAAKC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAMC,MAAA,EAAQ;EAAMC,CAAA,EAAG;EAAKC,CAAA,EAAG;EAAKC,EAAA,EAAI;EAAKL,GAAA,EAAK;AAAA,CAAU,EAChF;AAaA,MAAMM,UAAA,GAAaC,gBAAA,CAAiB,eAAeX,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
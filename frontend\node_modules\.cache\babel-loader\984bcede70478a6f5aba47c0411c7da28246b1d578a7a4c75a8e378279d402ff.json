{"ast": null, "code": "/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"20\",\n  height: \"6\",\n  x: \"2\",\n  y: \"4\",\n  rx: \"2\",\n  key: \"qdearl\"\n}], [\"rect\", {\n  width: \"20\",\n  height: \"6\",\n  x: \"2\",\n  y: \"14\",\n  rx: \"2\",\n  key: \"1xrn6j\"\n}]];\nconst StretchHorizontal = createLucideIcon(\"stretch-horizontal\", __iconNode);\nexport { __iconNode, StretchHorizontal as default };", "map": {"version": 3, "names": ["__iconNode", "width", "height", "x", "y", "rx", "key", "StretchHorizontal", "createLucideIcon"], "sources": ["D:\\techvritti\\Collegemanagement\\frontend\\node_modules\\lucide-react\\src\\icons\\stretch-horizontal.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '20', height: '6', x: '2', y: '4', rx: '2', key: 'qdearl' }],\n  ['rect', { width: '20', height: '6', x: '2', y: '14', rx: '2', key: '1xrn6j' }],\n];\n\n/**\n * @component @name StretchHorizontal\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iNiIgeD0iMiIgeT0iNCIgcng9IjIiIC8+CiAgPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjYiIHg9IjIiIHk9IjE0IiByeD0iMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/stretch-horizontal\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst StretchHorizontal = createLucideIcon('stretch-horizontal', __iconNode);\n\nexport default StretchHorizontal;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAMC,MAAA,EAAQ;EAAKC,CAAA,EAAG;EAAKC,CAAA,EAAG;EAAKC,EAAA,EAAI;EAAKC,GAAA,EAAK;AAAA,CAAU,GAC7E,CAAC,QAAQ;EAAEL,KAAA,EAAO;EAAMC,MAAA,EAAQ;EAAKC,CAAA,EAAG;EAAKC,CAAA,EAAG;EAAMC,EAAA,EAAI;EAAKC,GAAA,EAAK;AAAA,CAAU,EAChF;AAaA,MAAMC,iBAAA,GAAoBC,gBAAA,CAAiB,sBAAsBR,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
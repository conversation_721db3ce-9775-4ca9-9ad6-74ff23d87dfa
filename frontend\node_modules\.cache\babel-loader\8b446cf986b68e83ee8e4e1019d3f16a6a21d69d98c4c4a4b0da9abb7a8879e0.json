{"ast": null, "code": "/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12.8 19.6A2 2 0 1 0 14 16H2\",\n  key: \"148xed\"\n}], [\"path\", {\n  d: \"M17.5 8a2.5 2.5 0 1 1 2 4H2\",\n  key: \"1u4tom\"\n}], [\"path\", {\n  d: \"M9.8 4.4A2 2 0 1 1 11 8H2\",\n  key: \"75valh\"\n}]];\nconst Wind = createLucideIcon(\"wind\", __iconNode);\nexport { __iconNode, Wind as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Wind", "createLucideIcon"], "sources": ["D:\\techvritti\\Collegemanagement\\frontend\\node_modules\\lucide-react\\src\\icons\\wind.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12.8 19.6A2 2 0 1 0 14 16H2', key: '148xed' }],\n  ['path', { d: 'M17.5 8a2.5 2.5 0 1 1 2 4H2', key: '1u4tom' }],\n  ['path', { d: 'M9.8 4.4A2 2 0 1 1 11 8H2', key: '75valh' }],\n];\n\n/**\n * @component @name Wind\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIuOCAxOS42QTIgMiAwIDEgMCAxNCAxNkgyIiAvPgogIDxwYXRoIGQ9Ik0xNy41IDhhMi41IDIuNSAwIDEgMSAyIDRIMiIgLz4KICA8cGF0aCBkPSJNOS44IDQuNEEyIDIgMCAxIDEgMTEgOEgyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/wind\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Wind = createLucideIcon('wind', __iconNode);\n\nexport default Wind;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAgCC,GAAA,EAAK;AAAA,CAAU,GAC7D,CAAC,QAAQ;EAAED,CAAA,EAAG;EAA+BC,GAAA,EAAK;AAAA,CAAU,GAC5D,CAAC,QAAQ;EAAED,CAAA,EAAG;EAA6BC,GAAA,EAAK;AAAA,CAAU,EAC5D;AAaA,MAAMC,IAAA,GAAOC,gBAAA,CAAiB,QAAQJ,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M8 3v3a2 2 0 0 1-2 2H3\",\n  key: \"hohbtr\"\n}], [\"path\", {\n  d: \"M21 8h-3a2 2 0 0 1-2-2V3\",\n  key: \"5jw1f3\"\n}], [\"path\", {\n  d: \"M3 16h3a2 2 0 0 1 2 2v3\",\n  key: \"198tvr\"\n}], [\"path\", {\n  d: \"M16 21v-3a2 2 0 0 1 2-2h3\",\n  key: \"ph8mxp\"\n}]];\nconst Minimize = createLucideIcon(\"minimize\", __iconNode);\nexport { __iconNode, Minimize as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Minimize", "createLucideIcon"], "sources": ["D:\\techvritti\\Collegemanagement\\frontend\\node_modules\\lucide-react\\src\\icons\\minimize.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M8 3v3a2 2 0 0 1-2 2H3', key: 'hohbtr' }],\n  ['path', { d: 'M21 8h-3a2 2 0 0 1-2-2V3', key: '5jw1f3' }],\n  ['path', { d: 'M3 16h3a2 2 0 0 1 2 2v3', key: '198tvr' }],\n  ['path', { d: 'M16 21v-3a2 2 0 0 1 2-2h3', key: 'ph8mxp' }],\n];\n\n/**\n * @component @name Minimize\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOCAzdjNhMiAyIDAgMCAxLTIgMkgzIiAvPgogIDxwYXRoIGQ9Ik0yMSA4aC0zYTIgMiAwIDAgMS0yLTJWMyIgLz4KICA8cGF0aCBkPSJNMyAxNmgzYTIgMiAwIDAgMSAyIDJ2MyIgLz4KICA8cGF0aCBkPSJNMTYgMjF2LTNhMiAyIDAgMCAxIDItMmgzIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/minimize\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Minimize = createLucideIcon('minimize', __iconNode);\n\nexport default Minimize;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAA0BC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,QAAQ;EAAED,CAAA,EAAG;EAA4BC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,QAAQ;EAAED,CAAA,EAAG;EAA2BC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,QAAQ;EAAED,CAAA,EAAG;EAA6BC,GAAA,EAAK;AAAA,CAAU,EAC5D;AAaA,MAAMC,QAAA,GAAWC,gBAAA,CAAiB,YAAYJ,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
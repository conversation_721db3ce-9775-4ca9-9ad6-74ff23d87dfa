{"ast": null, "code": "/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 6.5v11a5.5 5.5 0 0 0 5.5-5.5\",\n  key: \"nw10mp\"\n}], [\"path\", {\n  d: \"m14 8-6 3\",\n  key: \"2tb98i\"\n}], [\"path\", {\n  d: \"M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1z\",\n  key: \"io9ry0\"\n}]];\nconst ReceiptTurkishLira = createLucideIcon(\"receipt-turkish-lira\", __iconNode);\nexport { __iconNode, ReceiptTurkishLira as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "ReceiptTurkishLira", "createLucideIcon"], "sources": ["D:\\techvritti\\Collegemanagement\\frontend\\node_modules\\lucide-react\\src\\icons\\receipt-turkish-lira.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M10 6.5v11a5.5 5.5 0 0 0 5.5-5.5', key: 'nw10mp' }],\n  ['path', { d: 'm14 8-6 3', key: '2tb98i' }],\n  [\n    'path',\n    { d: 'M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1z', key: 'io9ry0' },\n  ],\n];\n\n/**\n * @component @name ReceiptTurkishLira\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgNi41djExYTUuNSA1LjUgMCAwIDAgNS41LTUuNSIgLz4KICA8cGF0aCBkPSJtMTQgOC02IDMiIC8+CiAgPHBhdGggZD0iTTQgMnYyMGwyLTEgMiAxIDItMSAyIDEgMi0xIDIgMSAyLTEgMiAxVjJsLTIgMS0yLTEtMiAxLTItMS0yIDEtMi0xLTIgMXoiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/receipt-turkish-lira\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ReceiptTurkishLira = createLucideIcon('receipt-turkish-lira', __iconNode);\n\nexport default ReceiptTurkishLira;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAoCC,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAaC,GAAA,EAAK;AAAA,CAAU,GAC1C,CACE,QACA;EAAED,CAAA,EAAG;EAA2EC,GAAA,EAAK;AAAA,EAAS,CAElG;AAaA,MAAMC,kBAAA,GAAqBC,gBAAA,CAAiB,wBAAwBJ,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
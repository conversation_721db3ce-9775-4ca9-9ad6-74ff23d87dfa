{"ast": null, "code": "/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 3h5v5\",\n  key: \"1806ms\"\n}], [\"path\", {\n  d: \"M8 3H3v5\",\n  key: \"15dfkv\"\n}], [\"path\", {\n  d: \"M12 22v-8.3a4 4 0 0 0-1.172-2.872L3 3\",\n  key: \"1qrqzj\"\n}], [\"path\", {\n  d: \"m15 9 6-6\",\n  key: \"ko1vev\"\n}]];\nconst Split = createLucideIcon(\"split\", __iconNode);\nexport { __iconNode, Split as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Split", "createLucideIcon"], "sources": ["D:\\techvritti\\Collegemanagement\\frontend\\node_modules\\lucide-react\\src\\icons\\split.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 3h5v5', key: '1806ms' }],\n  ['path', { d: 'M8 3H3v5', key: '15dfkv' }],\n  ['path', { d: 'M12 22v-8.3a4 4 0 0 0-1.172-2.872L3 3', key: '1qrqzj' }],\n  ['path', { d: 'm15 9 6-6', key: 'ko1vev' }],\n];\n\n/**\n * @component @name Split\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgM2g1djUiIC8+CiAgPHBhdGggZD0iTTggM0gzdjUiIC8+CiAgPHBhdGggZD0iTTEyIDIydi04LjNhNCA0IDAgMCAwLTEuMTcyLTIuODcyTDMgMyIgLz4KICA8cGF0aCBkPSJtMTUgOSA2LTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/split\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Split = createLucideIcon('split', __iconNode);\n\nexport default Split;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAaC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAYC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAyCC,GAAA,EAAK;AAAA,CAAU,GACtE,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAaC,GAAA,EAAK;AAAA,CAAU,EAC5C;AAaA,MAAMC,KAAA,GAAQC,gBAAA,CAAiB,SAASJ,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 2v3a1 1 0 0 0 1 1h5\",\n  key: \"1xspal\"\n}], [\"path\", {\n  d: \"M18 18v-6a1 1 0 0 0-1-1h-6a1 1 0 0 0-1 1v6\",\n  key: \"1ra60u\"\n}], [\"path\", {\n  d: \"M18 22H4a2 2 0 0 1-2-2V6\",\n  key: \"pblm9e\"\n}], [\"path\", {\n  d: \"M8 18a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9.172a2 2 0 0 1 1.414.586l2.828 2.828A2 2 0 0 1 22 6.828V16a2 2 0 0 1-2.01 2z\",\n  key: \"1yve0x\"\n}]];\nconst SaveAll = createLucideIcon(\"save-all\", __iconNode);\nexport { __iconNode, SaveAll as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "SaveAll", "createLucideIcon"], "sources": ["D:\\techvritti\\Collegemanagement\\frontend\\node_modules\\lucide-react\\src\\icons\\save-all.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M10 2v3a1 1 0 0 0 1 1h5', key: '1xspal' }],\n  ['path', { d: 'M18 18v-6a1 1 0 0 0-1-1h-6a1 1 0 0 0-1 1v6', key: '1ra60u' }],\n  ['path', { d: 'M18 22H4a2 2 0 0 1-2-2V6', key: 'pblm9e' }],\n  [\n    'path',\n    {\n      d: 'M8 18a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9.172a2 2 0 0 1 1.414.586l2.828 2.828A2 2 0 0 1 22 6.828V16a2 2 0 0 1-2.01 2z',\n      key: '1yve0x',\n    },\n  ],\n];\n\n/**\n * @component @name SaveAll\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMnYzYTEgMSAwIDAgMCAxIDFoNSIgLz4KICA8cGF0aCBkPSJNMTggMTh2LTZhMSAxIDAgMCAwLTEtMWgtNmExIDEgMCAwIDAtMSAxdjYiIC8+CiAgPHBhdGggZD0iTTE4IDIySDRhMiAyIDAgMCAxLTItMlY2IiAvPgogIDxwYXRoIGQ9Ik04IDE4YTIgMiAwIDAgMS0yLTJWNGEyIDIgMCAwIDEgMi0yaDkuMTcyYTIgMiAwIDAgMSAxLjQxNC41ODZsMi44MjggMi44MjhBMiAyIDAgMCAxIDIyIDYuODI4VjE2YTIgMiAwIDAgMS0yLjAxIDJ6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/save-all\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SaveAll = createLucideIcon('save-all', __iconNode);\n\nexport default SaveAll;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAA2BC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,QAAQ;EAAED,CAAA,EAAG;EAA8CC,GAAA,EAAK;AAAA,CAAU,GAC3E,CAAC,QAAQ;EAAED,CAAA,EAAG;EAA4BC,GAAA,EAAK;AAAA,CAAU,GACzD,CACE,QACA;EACED,CAAA,EAAG;EACHC,GAAA,EAAK;AAAA,EACP,CAEJ;AAaA,MAAMC,OAAA,GAAUC,gBAAA,CAAiB,YAAYJ,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
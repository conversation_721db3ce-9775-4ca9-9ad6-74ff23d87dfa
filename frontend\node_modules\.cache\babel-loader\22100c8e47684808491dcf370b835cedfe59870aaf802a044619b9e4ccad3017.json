{"ast": null, "code": "var _jsxFileName = \"D:\\\\techvritti\\\\Collegemanagement\\\\frontend\\\\src\\\\Screens\\\\Student\\\\AttendancePage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Calendar, Users, BookOpen, GraduationCap, Filter, Download, TrendingUp, Clock, CheckCircle, XCircle, AlertCircle, Info } from 'lucide-react';\nimport 'react-calendar/dist/Calendar.css';\nimport CalendarView from 'react-calendar';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AttendancePage = () => {\n  _s();\n  const [activeView, setActiveView] = useState('overall');\n  const [selectedBatch, setSelectedBatch] = useState('all');\n  const [selectedCourse, setSelectedCourse] = useState('all');\n  const [selectedSubject, setSelectedSubject] = useState('all');\n  const [dateRange, setDateRange] = useState('thisMonth');\n  const [selectedDate, setSelectedDate] = useState(new Date());\n  const [attendanceData] = useState({\n    overall: {\n      present: 85,\n      absent: 15,\n      total: 100\n    },\n    subjects: [{\n      name: 'Mathematics',\n      present: 18,\n      total: 20,\n      percentage: 90\n    }, {\n      name: 'Physics',\n      present: 16,\n      total: 18,\n      percentage: 89\n    }, {\n      name: 'Chemistry',\n      present: 14,\n      total: 16,\n      percentage: 88\n    }, {\n      name: 'Computer Science',\n      present: 19,\n      total: 22,\n      percentage: 86\n    }, {\n      name: 'English',\n      present: 15,\n      total: 18,\n      percentage: 83\n    }],\n    batches: [{\n      name: 'Batch A',\n      students: 45,\n      avgAttendance: 87\n    }, {\n      name: 'Batch B',\n      students: 42,\n      avgAttendance: 85\n    }, {\n      name: 'Batch C',\n      students: 48,\n      avgAttendance: 89\n    }],\n    courses: [{\n      name: 'Engineering',\n      students: 135,\n      avgAttendance: 87\n    }, {\n      name: 'Science',\n      students: 98,\n      avgAttendance: 85\n    }, {\n      name: 'Commerce',\n      students: 76,\n      avgAttendance: 91\n    }],\n    recentActivity: [{\n      date: '2024-03-15',\n      subject: 'Mathematics',\n      status: 'present',\n      time: '09:00 AM'\n    }, {\n      date: '2024-03-15',\n      subject: 'Physics',\n      status: 'present',\n      time: '10:30 AM'\n    }, {\n      date: '2024-03-14',\n      subject: 'Chemistry',\n      status: 'absent',\n      time: '02:00 PM'\n    }, {\n      date: '2024-03-14',\n      subject: 'Computer Science',\n      status: 'present',\n      time: '03:30 PM'\n    }, {\n      date: '2024-03-13',\n      subject: 'English',\n      status: 'present',\n      time: '11:00 AM'\n    }],\n    // Enhanced daily attendance records for calendar view\n    dailyRecords: [\n    // January 2024 - Engineering Course\n    {\n      date: '2024-01-15',\n      subject: 'Mathematics',\n      status: 'Present',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-15',\n      subject: 'Physics',\n      status: 'Present',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-16',\n      subject: 'Chemistry',\n      status: 'Absent',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-16',\n      subject: 'Mathematics',\n      status: 'Present',\n      course: 'Engineering',\n      batch: 'Batch B',\n      type: 'class'\n    }, {\n      date: '2024-01-17',\n      subject: 'English',\n      status: 'Present',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-18',\n      subject: 'Computer Science',\n      status: 'Absent',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-19',\n      subject: 'Physics',\n      status: 'Present',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-20',\n      subject: 'Holiday',\n      status: 'Holiday',\n      course: 'all',\n      batch: 'all',\n      type: 'holiday'\n    }, {\n      date: '2024-01-21',\n      subject: 'Holiday',\n      status: 'Holiday',\n      course: 'all',\n      batch: 'all',\n      type: 'holiday'\n    }, {\n      date: '2024-01-22',\n      subject: 'Mathematics',\n      status: 'Present',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-23',\n      subject: 'Chemistry',\n      status: 'Present',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-24',\n      subject: 'English',\n      status: 'Absent',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-25',\n      subject: 'Computer Science',\n      status: 'Present',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-26',\n      subject: 'Physics',\n      status: 'Present',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-29',\n      subject: 'Mathematics',\n      status: 'Present',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-30',\n      subject: 'Chemistry',\n      status: 'Absent',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-31',\n      subject: 'English',\n      status: 'Present',\n      course: 'Engineering',\n      batch: 'Batch A',\n      type: 'class'\n    },\n    // Science course records\n    {\n      date: '2024-01-15',\n      subject: 'Biology',\n      status: 'Present',\n      course: 'Science',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-16',\n      subject: 'Chemistry',\n      status: 'Present',\n      course: 'Science',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-17',\n      subject: 'Physics',\n      status: 'Absent',\n      course: 'Science',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-18',\n      subject: 'Mathematics',\n      status: 'Present',\n      course: 'Science',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-19',\n      subject: 'Biology',\n      status: 'Present',\n      course: 'Science',\n      batch: 'Batch A',\n      type: 'class'\n    },\n    // Commerce course records\n    {\n      date: '2024-01-15',\n      subject: 'Accounting',\n      status: 'Present',\n      course: 'Commerce',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-16',\n      subject: 'Economics',\n      status: 'Present',\n      course: 'Commerce',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-17',\n      subject: 'Business Studies',\n      status: 'Present',\n      course: 'Commerce',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-18',\n      subject: 'Accounting',\n      status: 'Absent',\n      course: 'Commerce',\n      batch: 'Batch A',\n      type: 'class'\n    }, {\n      date: '2024-01-19',\n      subject: 'Economics',\n      status: 'Present',\n      course: 'Commerce',\n      batch: 'Batch A',\n      type: 'class'\n    }]\n  });\n  const getStatusIcon = status => {\n    switch (status) {\n      case 'present':\n        return /*#__PURE__*/_jsxDEV(CheckCircle, {\n          className: \"w-4 h-4 text-green-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 36\n        }, this);\n      case 'absent':\n        return /*#__PURE__*/_jsxDEV(XCircle, {\n          className: \"w-4 h-4 text-red-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 35\n        }, this);\n      case 'late':\n        return /*#__PURE__*/_jsxDEV(AlertCircle, {\n          className: \"w-4 h-4 text-yellow-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 33\n        }, this);\n      default:\n        return null;\n    }\n  };\n  const getAttendanceColor = percentage => {\n    if (percentage >= 90) return 'text-green-600 bg-green-50 border-green-200';\n    if (percentage >= 75) return 'text-yellow-600 bg-yellow-50 border-yellow-200';\n    return 'text-red-600 bg-red-50 border-red-200';\n  };\n  const StatCard = ({\n    title,\n    value,\n    subtitle,\n    icon: Icon,\n    trend\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-gradient-to-br from-white via-blue-50 to-blue-100 rounded-xl p-6 shadow-md border border-blue-100 hover:shadow-lg transition-shadow\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 bg-blue-600 rounded-lg\",\n          children: /*#__PURE__*/_jsxDEV(Icon, {\n            className: \"w-5 h-5 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-blue-700 font-semibold\",\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 17\n      }, this), trend && /*#__PURE__*/_jsxDEV(TrendingUp, {\n        className: \"w-4 h-4 text-green-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 27\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-1\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-3xl font-extrabold text-blue-900\",\n        children: value\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-blue-600\",\n        children: subtitle\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 95,\n    columnNumber: 9\n  }, this);\n  const ProgressBar = ({\n    percentage,\n    label,\n    total,\n    present\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-2\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-sm font-semibold text-gray-700\",\n        children: label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: `text-xs px-2 py-1 rounded-full border ${getAttendanceColor(percentage)}`,\n        children: [percentage, \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-full bg-gray-200 rounded-full h-2\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `h-2 rounded-full transition-all duration-300 ${percentage >= 90 ? 'bg-green-500' : percentage >= 75 ? 'bg-yellow-500' : 'bg-red-500'}`,\n        style: {\n          width: `${percentage}%`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between text-xs text-gray-500\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: [\"Present: \", present]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: [\"Total: \", total]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 113,\n    columnNumber: 9\n  }, this);\n\n  // Auto-switch to calendar view when filters are selected\n  useEffect(() => {\n    if (selectedCourse !== 'all' || selectedBatch !== 'all' || selectedSubject !== 'all') {\n      setActiveView('calendar');\n    }\n  }, [selectedCourse, selectedBatch, selectedSubject]);\n\n  // Filter attendance data based on selected filters\n  const getFilteredAttendanceData = () => {\n    return attendanceData.dailyRecords.filter(record => {\n      const courseMatch = selectedCourse === 'all' || record.course.toLowerCase() === selectedCourse.toLowerCase();\n      const batchMatch = selectedBatch === 'all' || record.batch.toLowerCase() === selectedBatch.toLowerCase();\n      const subjectMatch = selectedSubject === 'all' || record.subject.toLowerCase() === selectedSubject.toLowerCase();\n      return courseMatch && batchMatch && subjectMatch;\n    });\n  };\n\n  // Get attendance status for a specific date\n  const getAttendanceForDate = date => {\n    const dateStr = date.toISOString().split('T')[0];\n    const filteredData = getFilteredAttendanceData();\n    const dayRecords = filteredData.filter(record => record.date === dateStr);\n    if (dayRecords.length === 0) return null;\n\n    // Check if it's a holiday\n    if (dayRecords.some(record => record.type === 'holiday')) {\n      return {\n        status: 'holiday',\n        records: dayRecords\n      };\n    }\n\n    // Calculate overall status for the day\n    const presentCount = dayRecords.filter(record => record.status === 'Present').length;\n    const absentCount = dayRecords.filter(record => record.status === 'Absent').length;\n    if (presentCount > 0 && absentCount === 0) return {\n      status: 'present',\n      records: dayRecords\n    };\n    if (absentCount > 0 && presentCount === 0) return {\n      status: 'absent',\n      records: dayRecords\n    };\n    if (presentCount > 0 && absentCount > 0) return {\n      status: 'mixed',\n      records: dayRecords\n    };\n    return {\n      status: 'unknown',\n      records: dayRecords\n    };\n  };\n\n  // Custom tile content for calendar (small dots)\n  const getTileContent = ({\n    date,\n    view\n  }) => {\n    if (view !== 'month') return null;\n    const attendance = getAttendanceForDate(date);\n    if (!attendance) return null;\n    const today = new Date();\n    const isFuture = date > today;\n    if (isFuture) return null;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center mt-1\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `w-2 h-2 rounded-full ${attendance.status === 'present' ? 'bg-green-500' : attendance.status === 'absent' ? 'bg-red-500' : attendance.status === 'holiday' ? 'bg-gray-400' : attendance.status === 'mixed' ? 'bg-yellow-500' : 'bg-blue-400'}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 13\n    }, this);\n  };\n\n  // Custom tile class name for calendar styling\n  const getTileClassName = ({\n    date,\n    view\n  }) => {\n    if (view !== 'month') return null;\n    const attendance = getAttendanceForDate(date);\n    const today = new Date();\n    const isFuture = date > today;\n    if (isFuture) {\n      return 'future-date';\n    }\n    if (!attendance) return 'no-class';\n    switch (attendance.status) {\n      case 'present':\n        return 'present-day';\n      case 'absent':\n        return 'absent-day';\n      case 'holiday':\n        return 'holiday-day';\n      case 'mixed':\n        return 'mixed-day';\n      default:\n        return 'no-class';\n    }\n  };\n  const handleDateChange = date => {\n    setSelectedDate(date);\n\n    // Add a subtle animation effect when date is selected\n    const calendarElement = document.querySelector('.enhanced-calendar');\n    if (calendarElement) {\n      calendarElement.style.transform = 'scale(0.98)';\n      setTimeout(() => {\n        calendarElement.style.transform = 'scale(1)';\n      }, 150);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-tr from-blue-50 via-white to-pink-50 p-6\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto space-y-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl p-8 shadow-lg border border-blue-100\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-4xl font-extrabold text-blue-800 leading-tight\",\n              children: \"Attendance Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-blue-600 mt-2 text-lg\",\n              children: \"Monitor attendance by batch, course, and subject.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"flex items-center gap-2 px-5 py-2 bg-blue-700 text-white rounded-xl shadow hover:bg-blue-800 transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(Download, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Export\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"flex items-center gap-2 px-5 py-2 border border-blue-300 text-blue-700 rounded-xl hover:bg-blue-50 transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(Filter, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Filter\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl p-2 shadow-sm border border-blue-100\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap gap-2 justify-center\",\n          children: [{\n            id: 'overall',\n            label: 'Overall',\n            icon: TrendingUp\n          }, {\n            id: 'subjects',\n            label: 'By Subject',\n            icon: BookOpen\n          }, {\n            id: 'batches',\n            label: 'By Batch',\n            icon: Users\n          }, {\n            id: 'courses',\n            label: 'By Course',\n            icon: GraduationCap\n          }, {\n            id: 'calendar',\n            label: 'Calendar View',\n            icon: Calendar\n          }].map(({\n            id,\n            label,\n            icon: Icon\n          }) => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setActiveView(id),\n            className: `flex items-center gap-2 px-5 py-2 rounded-xl font-medium text-base transition-all\n                  ${activeView === id ? 'bg-blue-700 text-white shadow' : 'text-blue-700 hover:bg-blue-100'}`,\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 33\n            }, this)]\n          }, id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 29\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl p-6 shadow-md border border-blue-100\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-semibold text-blue-700 mb-2\",\n              children: \"Date Range\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: dateRange,\n              onChange: e => setDateRange(e.target.value),\n              className: \"w-full px-3 py-2 border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"today\",\n                children: \"Today\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"thisWeek\",\n                children: \"This Week\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"thisMonth\",\n                children: \"This Month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"lastMonth\",\n                children: \"Last Month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"thisYear\",\n                children: \"This Year\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-semibold text-blue-700 mb-2\",\n              children: \"Batch\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedBatch,\n              onChange: e => {\n                setSelectedBatch(e.target.value);\n                // Add smooth transition to calendar view\n                if (e.target.value !== 'all') {\n                  setTimeout(() => setActiveView('calendar'), 300);\n                }\n              },\n              className: \"w-full px-3 py-2 border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: \"All Batches\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Batch A\",\n                children: \"Batch A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Batch B\",\n                children: \"Batch B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Batch C\",\n                children: \"Batch C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-semibold text-blue-700 mb-2\",\n              children: \"Course\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedCourse,\n              onChange: e => {\n                setSelectedCourse(e.target.value);\n                // Add smooth transition to calendar view\n                if (e.target.value !== 'all') {\n                  setTimeout(() => setActiveView('calendar'), 300);\n                }\n              },\n              className: \"w-full px-3 py-2 border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: \"All Courses\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Engineering\",\n                children: \"Engineering\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Science\",\n                children: \"Science\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Commerce\",\n                children: \"Commerce\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-semibold text-blue-700 mb-2\",\n              children: \"Subject\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedSubject,\n              onChange: e => {\n                setSelectedSubject(e.target.value);\n                // Add smooth transition to calendar view\n                if (e.target.value !== 'all') {\n                  setTimeout(() => setActiveView('calendar'), 300);\n                }\n              },\n              className: \"w-full px-3 py-2 border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: \"All Subjects\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Mathematics\",\n                children: \"Mathematics\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Physics\",\n                children: \"Physics\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Chemistry\",\n                children: \"Chemistry\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Computer Science\",\n                children: \"Computer Science\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"English\",\n                children: \"English\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Biology\",\n                children: \"Biology\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Accounting\",\n                children: \"Accounting\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Economics\",\n                children: \"Economics\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Business Studies\",\n                children: \"Business Studies\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Overall Attendance\",\n          value: \"85%\",\n          subtitle: \"This month average\",\n          icon: TrendingUp,\n          trend: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Total Classes\",\n          value: \"124\",\n          subtitle: \"Classes conducted\",\n          icon: Calendar\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Present Days\",\n          value: \"105\",\n          subtitle: \"Out of 124 classes\",\n          icon: CheckCircle\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 374,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 xl:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"xl:col-span-2 space-y-8\",\n          children: [activeView === 'overall' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-2xl p-8 shadow-md border border-blue-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-blue-900 mb-6\",\n              children: \"Overall Attendance Summary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center p-8 bg-gradient-to-tr from-blue-50 via-blue-100 to-white rounded-xl shadow\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-5xl font-extrabold text-blue-700 mb-2 drop-shadow\",\n                  children: \"85%\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-blue-800 font-semibold\",\n                  children: \"Overall Attendance\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-5\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-xl shadow-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                      className: \"w-8 h-8 text-green-600\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 411,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"font-semibold text-green-900\",\n                        children: \"Present\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 413,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-green-700\",\n                        children: \"Days attended\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 414,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 412,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 410,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-extrabold text-green-600\",\n                    children: \"105\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 417,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between p-4 bg-red-50 border border-red-200 rounded-xl shadow-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [/*#__PURE__*/_jsxDEV(XCircle, {\n                      className: \"w-8 h-8 text-red-600\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 421,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"font-semibold text-red-900\",\n                        children: \"Absent\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 423,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-red-700\",\n                        children: \"Days missed\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 424,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 422,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 420,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-extrabold text-red-600\",\n                    children: \"19\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 427,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 29\n          }, this), activeView === 'subjects' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-2xl p-8 shadow-md border border-blue-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-blue-900 mb-6\",\n              children: \"Subject-wise Attendance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-6\",\n              children: attendanceData.subjects.map((subject, index) => /*#__PURE__*/_jsxDEV(ProgressBar, {\n                percentage: subject.percentage,\n                label: subject.name,\n                total: subject.total,\n                present: subject.present\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 41\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 29\n          }, this), activeView === 'batches' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-2xl p-8 shadow-md border border-blue-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-blue-900 mb-6\",\n              children: \"Batch-wise Attendance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n              children: attendanceData.batches.map((batch, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6 bg-blue-50 rounded-xl border border-blue-200 shadow hover:scale-105 transition-transform\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-semibold text-blue-900\",\n                    children: batch.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 458,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(Users, {\n                    className: \"w-5 h-5 text-blue-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 459,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 457,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl font-bold text-blue-900\",\n                    children: [batch.avgAttendance, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 462,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-blue-700\",\n                    children: [batch.students, \" students\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 463,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full bg-blue-200 rounded-full h-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"h-2 bg-blue-600 rounded-full transition-all duration-300\",\n                      style: {\n                        width: `${batch.avgAttendance}%`\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 465,\n                      columnNumber: 53\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 464,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 45\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 456,\n                columnNumber: 41\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 29\n          }, this), activeView === 'courses' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-2xl p-8 shadow-md border border-blue-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-blue-900 mb-6\",\n              children: \"Course-wise Attendance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-6\",\n              children: attendanceData.courses.map((course, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between p-5 border border-blue-200 rounded-xl bg-blue-50 hover:bg-blue-100 transition-colors shadow\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-blue-700 rounded-lg\",\n                    children: /*#__PURE__*/_jsxDEV(GraduationCap, {\n                      className: \"w-6 h-6 text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 485,\n                      columnNumber: 53\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 484,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-semibold text-blue-900\",\n                      children: course.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 488,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-blue-700\",\n                      children: [course.students, \" students enrolled\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 489,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 487,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 483,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `text-lg font-bold px-4 py-1 rounded-full border ${getAttendanceColor(course.avgAttendance)}`,\n                    children: [course.avgAttendance, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 493,\n                    columnNumber: 49\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 492,\n                  columnNumber: 45\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 41\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 29\n          }, this), activeView === 'calendar' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-8\",\n            children: [(selectedCourse !== 'all' || selectedBatch !== 'all' || selectedSubject !== 'all') && (() => {\n              const filteredData = getFilteredAttendanceData();\n              const classRecords = filteredData.filter(record => record.type === 'class');\n              const presentCount = classRecords.filter(record => record.status === 'Present').length;\n              const absentCount = classRecords.filter(record => record.status === 'Absent').length;\n              const totalClasses = classRecords.length;\n              const attendancePercentage = totalClasses > 0 ? Math.round(presentCount / totalClasses * 100) : 0;\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-yellow-50 rounded-3xl p-8 shadow-xl border border-yellow-200 backdrop-blur-sm\",\n                style: {\n                  backgroundColor: '#fcc250',\n                  borderColor: '#fcc250'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"text-2xl font-bold mb-2\",\n                      style: {\n                        color: '#29354d'\n                      },\n                      children: \"Filtered Attendance Summary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 518,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex flex-wrap gap-2 text-sm\",\n                      children: [selectedCourse !== 'all' && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"px-3 py-1 rounded-full border\",\n                        style: {\n                          backgroundColor: '#29354d',\n                          color: '#fcc250',\n                          borderColor: '#29354d'\n                        },\n                        children: [\"Course: \", selectedCourse]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 521,\n                        columnNumber: 61\n                      }, this), selectedBatch !== 'all' && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"px-3 py-1 rounded-full border\",\n                        style: {\n                          backgroundColor: '#29354d',\n                          color: '#fcc250',\n                          borderColor: '#29354d'\n                        },\n                        children: [\"Batch: \", selectedBatch]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 526,\n                        columnNumber: 61\n                      }, this), selectedSubject !== 'all' && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"px-3 py-1 rounded-full border\",\n                        style: {\n                          backgroundColor: '#29354d',\n                          color: '#fcc250',\n                          borderColor: '#29354d'\n                        },\n                        children: [\"Subject: \", selectedSubject]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 531,\n                        columnNumber: 61\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 519,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 517,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"hidden md:block\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-16 h-16 rounded-2xl flex items-center justify-center shadow-lg\",\n                      style: {\n                        backgroundColor: '#29354d'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(TrendingUp, {\n                        className: \"w-8 h-8\",\n                        style: {\n                          color: '#fcc250'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 539,\n                        columnNumber: 57\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 538,\n                      columnNumber: 53\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 537,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 516,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"group relative overflow-hidden bg-white rounded-2xl p-6 shadow-lg border hover:shadow-xl transition-all duration-300 hover:-translate-y-1\",\n                    style: {\n                      borderColor: '#29354d'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute top-0 right-0 w-20 h-20 rounded-bl-3xl opacity-10\",\n                      style: {\n                        backgroundColor: '#29354d'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 546,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"relative\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-between mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"p-2 rounded-xl\",\n                          style: {\n                            backgroundColor: '#fcc250'\n                          },\n                          children: /*#__PURE__*/_jsxDEV(TrendingUp, {\n                            className: \"w-5 h-5\",\n                            style: {\n                              color: '#29354d'\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 550,\n                            columnNumber: 65\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 549,\n                          columnNumber: 61\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `text-xs px-2 py-1 rounded-full ${attendancePercentage >= 90 ? 'bg-green-100 text-green-700' : attendancePercentage >= 75 ? 'text-white' : 'bg-red-100 text-red-700'}`,\n                          style: attendancePercentage >= 75 && attendancePercentage < 90 ? {\n                            backgroundColor: '#fcc250',\n                            color: '#29354d'\n                          } : {},\n                          children: attendancePercentage >= 90 ? 'Excellent' : attendancePercentage >= 75 ? 'Good' : 'Needs Improvement'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 552,\n                          columnNumber: 61\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 548,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-3xl font-bold mb-1\",\n                        style: {\n                          color: '#29354d'\n                        },\n                        children: [attendancePercentage, \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 560,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm font-medium\",\n                        style: {\n                          color: '#29354d'\n                        },\n                        children: \"Attendance Rate\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 561,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 547,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 545,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"group relative overflow-hidden bg-white rounded-2xl p-6 shadow-lg border hover:shadow-xl transition-all duration-300 hover:-translate-y-1\",\n                    style: {\n                      borderColor: '#29354d'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute top-0 right-0 w-20 h-20 rounded-bl-3xl opacity-10\",\n                      style: {\n                        backgroundColor: '#fcc250'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 566,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"relative\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-between mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"p-2 rounded-xl\",\n                          style: {\n                            backgroundColor: '#29354d'\n                          },\n                          children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                            className: \"w-5 h-5\",\n                            style: {\n                              color: '#fcc250'\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 570,\n                            columnNumber: 65\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 569,\n                          columnNumber: 61\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-xs px-2 py-1 rounded-full\",\n                          style: {\n                            backgroundColor: '#fcc250',\n                            color: '#29354d'\n                          },\n                          children: [totalClasses > 0 ? Math.round(presentCount / totalClasses * 100) : 0, \"%\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 572,\n                          columnNumber: 61\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 568,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-3xl font-bold mb-1\",\n                        style: {\n                          color: '#29354d'\n                        },\n                        children: presentCount\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 576,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm font-medium\",\n                        style: {\n                          color: '#29354d'\n                        },\n                        children: \"Present Days\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 577,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 567,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 565,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"group relative overflow-hidden bg-white rounded-2xl p-6 shadow-lg border hover:shadow-xl transition-all duration-300 hover:-translate-y-1\",\n                    style: {\n                      borderColor: '#29354d'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute top-0 right-0 w-20 h-20 rounded-bl-3xl opacity-10\",\n                      style: {\n                        backgroundColor: '#29354d'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 582,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"relative\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-between mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"p-2 rounded-xl\",\n                          style: {\n                            backgroundColor: '#fcc250'\n                          },\n                          children: /*#__PURE__*/_jsxDEV(XCircle, {\n                            className: \"w-5 h-5\",\n                            style: {\n                              color: '#29354d'\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 586,\n                            columnNumber: 65\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 585,\n                          columnNumber: 61\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-xs px-2 py-1 rounded-full\",\n                          style: {\n                            backgroundColor: '#29354d',\n                            color: '#fcc250'\n                          },\n                          children: [totalClasses > 0 ? Math.round(absentCount / totalClasses * 100) : 0, \"%\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 588,\n                          columnNumber: 61\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 584,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-3xl font-bold mb-1\",\n                        style: {\n                          color: '#29354d'\n                        },\n                        children: absentCount\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 592,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm font-medium\",\n                        style: {\n                          color: '#29354d'\n                        },\n                        children: \"Absent Days\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 593,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 583,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 581,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"group relative overflow-hidden bg-white rounded-2xl p-6 shadow-lg border hover:shadow-xl transition-all duration-300 hover:-translate-y-1\",\n                    style: {\n                      borderColor: '#29354d'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute top-0 right-0 w-20 h-20 rounded-bl-3xl opacity-10\",\n                      style: {\n                        backgroundColor: '#fcc250'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 598,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"relative\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-between mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"p-2 rounded-xl\",\n                          style: {\n                            backgroundColor: '#29354d'\n                          },\n                          children: /*#__PURE__*/_jsxDEV(Calendar, {\n                            className: \"w-5 h-5\",\n                            style: {\n                              color: '#fcc250'\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 602,\n                            columnNumber: 65\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 601,\n                          columnNumber: 61\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-xs px-2 py-1 rounded-full\",\n                          style: {\n                            backgroundColor: '#fcc250',\n                            color: '#29354d'\n                          },\n                          children: \"100%\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 604,\n                          columnNumber: 61\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 600,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-3xl font-bold mb-1\",\n                        style: {\n                          color: '#29354d'\n                        },\n                        children: totalClasses\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 608,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm font-medium\",\n                        style: {\n                          color: '#29354d'\n                        },\n                        children: \"Total Classes\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 609,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 599,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 597,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 544,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 515,\n                columnNumber: 41\n              }, this);\n            })(), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-br from-white via-blue-50/30 to-indigo-50/50 rounded-3xl p-8 shadow-2xl border border-blue-100/50 backdrop-blur-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl shadow-lg\",\n                    children: /*#__PURE__*/_jsxDEV(Calendar, {\n                      className: \"w-7 h-7 text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 622,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 621,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-2xl font-bold text-blue-900\",\n                      children: \"Interactive Calendar\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 625,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-blue-600 text-sm mt-1\",\n                      children: selectedCourse === 'all' && selectedBatch === 'all' && selectedSubject === 'all' ? 'Showing all attendance records' : 'Filtered attendance view'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 626,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 624,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 620,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-4 mt-4 lg:mt-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-lg font-bold text-blue-700\",\n                      children: new Date().toLocaleDateString('en-US', {\n                        month: 'short'\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 637,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-blue-600\",\n                      children: \"Current Month\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 638,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 636,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-px h-8 bg-blue-200\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 640,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-lg font-bold text-blue-700\",\n                      children: selectedDate.getDate()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 642,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-blue-600\",\n                      children: \"Selected\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 643,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 641,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 635,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 619,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-8 p-6 bg-gradient-to-r from-slate-50 to-blue-50 rounded-2xl border border-slate-200 shadow-inner\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-lg font-bold text-slate-700 flex items-center gap-2\",\n                    children: [/*#__PURE__*/_jsxDEV(Info, {\n                      className: \"w-5 h-5 text-blue-500\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 652,\n                      columnNumber: 49\n                    }, this), \"Calendar Legend\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 651,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-slate-500 bg-white px-3 py-1 rounded-full border\",\n                    children: \"Click dates for details\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 655,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 650,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-2 lg:grid-cols-4 gap-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-3 p-3 bg-white rounded-xl shadow-sm border border-green-100 hover:shadow-md transition-shadow\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-6 h-6 bg-gradient-to-br from-green-200 to-green-300 border-2 border-green-400 rounded-lg shadow-sm\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 661,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-semibold text-green-700\",\n                        children: \"Present\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 663,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-xs text-green-600\",\n                        children: \"Attended class\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 664,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 662,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 660,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-3 p-3 bg-white rounded-xl shadow-sm border border-red-100 hover:shadow-md transition-shadow\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-6 h-6 bg-gradient-to-br from-red-200 to-red-300 border-2 border-red-400 rounded-lg shadow-sm\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 668,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-semibold text-red-700\",\n                        children: \"Absent\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 670,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-xs text-red-600\",\n                        children: \"Missed class\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 671,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 669,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 667,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-3 p-3 bg-white rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-6 h-6 bg-gradient-to-br from-gray-200 to-gray-300 border-2 border-gray-400 rounded-lg shadow-sm\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 675,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-semibold text-gray-700\",\n                        children: \"Holiday\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 677,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-xs text-gray-600\",\n                        children: \"No classes\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 678,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 676,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 674,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-3 p-3 bg-white rounded-xl shadow-sm border border-yellow-100 hover:shadow-md transition-shadow\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-6 h-6 bg-gradient-to-br from-yellow-200 to-yellow-300 border-2 border-yellow-400 rounded-lg shadow-sm\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 682,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-semibold text-yellow-700\",\n                        children: \"Mixed\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 684,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-xs text-yellow-600\",\n                        children: \"Partial attendance\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 685,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 683,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 681,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 659,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 649,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"calendar-container relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute -top-4 -left-4 w-32 h-32 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-full blur-2xl\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 694,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute -bottom-4 -right-4 w-40 h-40 bg-gradient-to-br from-blue-400/20 to-cyan-400/20 rounded-full blur-2xl\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 695,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-br from-indigo-300/10 to-purple-300/10 rounded-full blur-3xl -z-10\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 696,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 overflow-hidden\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"h-2 bg-blue-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 701,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/_jsxDEV(CalendarView, {\n                      value: selectedDate,\n                      onChange: handleDateChange,\n                      tileContent: getTileContent,\n                      tileClassName: getTileClassName,\n                      className: \"futuristic-calendar w-full\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 705,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 704,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"h-1 bg-gradient-to-r from-transparent via-blue-400/50 to-transparent\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 715,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 699,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute -right-4 top-1/2 transform -translate-y-1/2 space-y-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"group w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(Calendar, {\n                      className: \"w-5 h-5 text-white group-hover:rotate-12 transition-transform\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 721,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 720,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"group w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(TrendingUp, {\n                      className: \"w-5 h-5 text-white group-hover:rotate-12 transition-transform\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 724,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 723,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"group w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(Download, {\n                      className: \"w-5 h-5 text-white group-hover:rotate-12 transition-transform\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 727,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 726,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 719,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-8 grid grid-cols-2 lg:grid-cols-4 gap-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"group relative overflow-hidden bg-gradient-to-br from-green-50 to-emerald-100 rounded-2xl p-4 border border-green-200 hover:shadow-lg transition-all duration-300 cursor-pointer\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute top-0 right-0 w-8 h-8 bg-green-400/20 rounded-bl-2xl\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 734,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-3 h-3 bg-green-500 rounded-full animate-pulse\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 736,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-green-700 font-medium\",\n                        children: \"Present Days\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 737,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 735,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-2 text-xs text-green-600\",\n                      children: \"Click to highlight\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 739,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 733,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"group relative overflow-hidden bg-gradient-to-br from-red-50 to-rose-100 rounded-2xl p-4 border border-red-200 hover:shadow-lg transition-all duration-300 cursor-pointer\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute top-0 right-0 w-8 h-8 bg-red-400/20 rounded-bl-2xl\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 743,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-3 h-3 bg-red-500 rounded-full animate-pulse\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 745,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-red-700 font-medium\",\n                        children: \"Absent Days\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 746,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 744,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-2 text-xs text-red-600\",\n                      children: \"Click to highlight\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 748,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 742,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"group relative overflow-hidden bg-gradient-to-br from-gray-50 to-slate-100 rounded-2xl p-4 border border-gray-200 hover:shadow-lg transition-all duration-300 cursor-pointer\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute top-0 right-0 w-8 h-8 bg-gray-400/20 rounded-bl-2xl\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 752,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-3 h-3 bg-gray-500 rounded-full animate-pulse\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 754,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-gray-700 font-medium\",\n                        children: \"Holidays\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 755,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 753,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-2 text-xs text-gray-600\",\n                      children: \"Click to highlight\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 757,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 751,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"group relative overflow-hidden bg-gradient-to-br from-yellow-50 to-amber-100 rounded-2xl p-4 border border-yellow-200 hover:shadow-lg transition-all duration-300 cursor-pointer\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"absolute top-0 right-0 w-8 h-8 bg-yellow-400/20 rounded-bl-2xl\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 761,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-3 h-3 bg-yellow-500 rounded-full animate-pulse\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 763,\n                        columnNumber: 53\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-yellow-700 font-medium\",\n                        children: \"Mixed Days\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 764,\n                        columnNumber: 53\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 762,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-2 text-xs text-yellow-600\",\n                      children: \"Click to highlight\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 766,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 760,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 732,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute -left-8 top-8 w-16 h-16\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-16 h-16 transform -rotate-90\",\n                    viewBox: \"0 0 64 64\",\n                    children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"32\",\n                      cy: \"32\",\n                      r: \"28\",\n                      fill: \"none\",\n                      stroke: \"#e5e7eb\",\n                      strokeWidth: \"4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 773,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"32\",\n                      cy: \"32\",\n                      r: \"28\",\n                      fill: \"none\",\n                      stroke: \"url(#gradient)\",\n                      strokeWidth: \"4\",\n                      strokeDasharray: \"175.93\",\n                      strokeDashoffset: \"35\",\n                      className: \"animate-pulse\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 774,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"defs\", {\n                      children: /*#__PURE__*/_jsxDEV(\"linearGradient\", {\n                        id: \"gradient\",\n                        x1: \"0%\",\n                        y1: \"0%\",\n                        x2: \"100%\",\n                        y2: \"100%\",\n                        children: [/*#__PURE__*/_jsxDEV(\"stop\", {\n                          offset: \"0%\",\n                          stopColor: \"#3b82f6\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 787,\n                          columnNumber: 57\n                        }, this), /*#__PURE__*/_jsxDEV(\"stop\", {\n                          offset: \"100%\",\n                          stopColor: \"#8b5cf6\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 788,\n                          columnNumber: 57\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 786,\n                        columnNumber: 53\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 785,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 772,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute inset-0 flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs font-bold text-blue-600\",\n                      children: \"80%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 793,\n                      columnNumber: 49\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 792,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 771,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 692,\n                columnNumber: 37\n              }, this), selectedDate && (() => {\n                const attendance = getAttendanceForDate(selectedDate);\n                const today = new Date();\n                const isToday = selectedDate.toDateString() === today.toDateString();\n                const isFuture = selectedDate > today;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-8 space-y-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl p-6 text-white shadow-xl\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-between\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                          className: \"text-2xl font-bold mb-1\",\n                          children: selectedDate.toLocaleDateString('en-US', {\n                            weekday: 'long',\n                            year: 'numeric',\n                            month: 'long',\n                            day: 'numeric'\n                          })\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 811,\n                          columnNumber: 61\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center gap-2 text-indigo-100\",\n                          children: [isToday && /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"px-2 py-1 bg-white/20 rounded-full text-xs font-medium\",\n                            children: \"Today\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 821,\n                            columnNumber: 69\n                          }, this), isFuture && /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"px-2 py-1 bg-white/20 rounded-full text-xs font-medium\",\n                            children: \"Future Date\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 826,\n                            columnNumber: 69\n                          }, this), attendance && /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: `px-2 py-1 rounded-full text-xs font-medium ${attendance.status === 'present' ? 'bg-green-400/30 text-green-100' : attendance.status === 'absent' ? 'bg-red-400/30 text-red-100' : attendance.status === 'holiday' ? 'bg-gray-400/30 text-gray-100' : 'bg-yellow-400/30 text-yellow-100'}`,\n                            children: attendance.status === 'present' ? 'Present Day' : attendance.status === 'absent' ? 'Absent Day' : attendance.status === 'holiday' ? 'Holiday' : 'Mixed Attendance'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 831,\n                            columnNumber: 69\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 819,\n                          columnNumber: 61\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 810,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-right\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-3xl font-bold\",\n                          children: selectedDate.getDate()\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 846,\n                          columnNumber: 61\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-sm text-indigo-200\",\n                          children: selectedDate.toLocaleDateString('en-US', {\n                            month: 'short'\n                          })\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 847,\n                          columnNumber: 61\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 845,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 809,\n                      columnNumber: 53\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 808,\n                    columnNumber: 49\n                  }, this), attendance && attendance.records.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white rounded-2xl p-6 shadow-lg border border-gray-100\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-between mb-6\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                        className: \"text-lg font-bold text-gray-900\",\n                        children: \"Class Details\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 858,\n                        columnNumber: 61\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [attendance.records.length, \" \", attendance.records.length === 1 ? 'class' : 'classes']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 859,\n                        columnNumber: 61\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 857,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"space-y-3\",\n                      children: attendance.records.map((record, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `group relative overflow-hidden rounded-xl border-2 transition-all duration-300 hover:shadow-lg ${record.status === 'Present' ? 'border-green-200 bg-gradient-to-r from-green-50 to-green-100/50 hover:border-green-300' : record.status === 'Absent' ? 'border-red-200 bg-gradient-to-r from-red-50 to-red-100/50 hover:border-red-300' : 'border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100/50 hover:border-gray-300'}`,\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"p-4\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center gap-4\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: `p-3 rounded-xl shadow-sm ${record.status === 'Present' ? 'bg-green-100' : record.status === 'Absent' ? 'bg-red-100' : 'bg-gray-100'}`,\n                                children: getStatusIcon(record.status.toLowerCase())\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 873,\n                                columnNumber: 81\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                                  className: \"font-bold text-gray-900 text-lg\",\n                                  children: record.subject\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 881,\n                                  columnNumber: 85\n                                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"flex items-center gap-2 text-sm text-gray-600 mt-1\",\n                                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                    className: \"px-2 py-1 bg-white/70 rounded-lg border\",\n                                    children: record.course\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 883,\n                                    columnNumber: 89\n                                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                    className: \"px-2 py-1 bg-white/70 rounded-lg border\",\n                                    children: record.batch\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 886,\n                                    columnNumber: 89\n                                  }, this), record.type === 'holiday' && /*#__PURE__*/_jsxDEV(\"span\", {\n                                    className: \"px-2 py-1 bg-blue-100 text-blue-700 rounded-lg border border-blue-200\",\n                                    children: \"Holiday\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 890,\n                                    columnNumber: 93\n                                  }, this)]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 882,\n                                  columnNumber: 85\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 880,\n                                columnNumber: 81\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 872,\n                              columnNumber: 77\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"text-right\",\n                              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: `inline-flex items-center px-4 py-2 rounded-xl font-semibold text-sm border-2 ${record.status === 'Present' ? 'bg-green-100 text-green-800 border-green-300' : record.status === 'Absent' ? 'bg-red-100 text-red-800 border-red-300' : 'bg-gray-100 text-gray-800 border-gray-300'}`,\n                                children: record.status\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 898,\n                                columnNumber: 81\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 897,\n                              columnNumber: 77\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 871,\n                            columnNumber: 73\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 870,\n                          columnNumber: 69\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `absolute top-0 right-0 w-1 h-full ${record.status === 'Present' ? 'bg-gradient-to-b from-green-400 to-green-600' : record.status === 'Absent' ? 'bg-gradient-to-b from-red-400 to-red-600' : 'bg-gradient-to-b from-gray-400 to-gray-600'}`\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 909,\n                          columnNumber: 69\n                        }, this)]\n                      }, index, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 865,\n                        columnNumber: 65\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 863,\n                      columnNumber: 57\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 856,\n                    columnNumber: 53\n                  }, this) : isFuture ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8 text-center border border-blue-100\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-16 h-16 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-2xl mx-auto mb-4 flex items-center justify-center\",\n                      children: /*#__PURE__*/_jsxDEV(Calendar, {\n                        className: \"w-8 h-8 text-white\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 921,\n                        columnNumber: 61\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 920,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"text-lg font-bold text-blue-900 mb-2\",\n                      children: \"Future Date\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 923,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-blue-600\",\n                      children: \"No attendance data available for future dates.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 924,\n                      columnNumber: 57\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 919,\n                    columnNumber: 53\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gradient-to-r from-gray-50 to-slate-50 rounded-2xl p-8 text-center border border-gray-200\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-16 h-16 bg-gradient-to-br from-gray-400 to-slate-500 rounded-2xl mx-auto mb-4 flex items-center justify-center\",\n                      children: /*#__PURE__*/_jsxDEV(Calendar, {\n                        className: \"w-8 h-8 text-white\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 929,\n                        columnNumber: 61\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 928,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"text-lg font-bold text-gray-700 mb-2\",\n                      children: \"No Classes\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 931,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600\",\n                      children: \"No classes were scheduled for this date.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 932,\n                      columnNumber: 57\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 927,\n                    columnNumber: 53\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 806,\n                  columnNumber: 45\n                }, this);\n              })()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 617,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 504,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl p-8 shadow-md border border-blue-100\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-bold text-blue-900 mb-6 flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(Clock, {\n              className: \"w-5 h-5 text-blue-700\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 946,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Recent Activity\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 947,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 945,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-5\",\n            children: attendanceData.recentActivity.map((activity, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-3 p-4 bg-blue-50 rounded-xl border hover:bg-blue-100 transition-colors\",\n              children: [getStatusIcon(activity.status), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold text-blue-900\",\n                  children: activity.subject\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 954,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-blue-700\",\n                  children: [activity.date, \" \\u2022 \", activity.time]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 955,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 953,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `text-xs px-3 py-1 rounded-full capitalize border\n                    ${activity.status === 'present' ? 'bg-green-100 text-green-800 border-green-300' : activity.status === 'absent' ? 'bg-red-100 text-red-800 border-red-300' : 'bg-yellow-100 text-yellow-800 border-yellow-300'}`,\n                children: activity.status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 957,\n                columnNumber: 37\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 951,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 949,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 944,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 397,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 242,\n    columnNumber: 9\n  }, this);\n};\n_s(AttendancePage, \"U/CV1TzuG0fvLkcqq9qrhG4KLhg=\");\n_c = AttendancePage;\nexport default AttendancePage;\n\n// Futuristic CSS styles for revolutionary calendar design\nconst calendarStyles = `\n.futuristic-calendar {\n    width: 100%;\n    border: none;\n    font-family: inherit;\n    background: transparent;\n}\n\n.enhanced-calendar {\n    width: 100%;\n    border: none;\n    font-family: inherit;\n    background: transparent;\n}\n\n.enhanced-calendar .react-calendar__navigation {\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    border-radius: 1rem 1rem 0 0;\n    padding: 1rem;\n    margin-bottom: 0;\n}\n\n.enhanced-calendar .react-calendar__navigation button {\n    background: rgba(255, 255, 255, 0.2);\n    color: white;\n    border: 2px solid rgba(255, 255, 255, 0.3);\n    padding: 0.75rem 1.5rem;\n    border-radius: 0.75rem;\n    font-weight: 600;\n    font-size: 0.9rem;\n    transition: all 0.3s ease;\n    backdrop-filter: blur(10px);\n}\n\n.enhanced-calendar .react-calendar__navigation button:hover {\n    background: rgba(255, 255, 255, 0.3);\n    border-color: rgba(255, 255, 255, 0.5);\n    transform: translateY(-1px);\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.enhanced-calendar .react-calendar__navigation button:disabled {\n    background: rgba(255, 255, 255, 0.1);\n    border-color: rgba(255, 255, 255, 0.2);\n    color: rgba(255, 255, 255, 0.5);\n    transform: none;\n    box-shadow: none;\n}\n\n.enhanced-calendar .react-calendar__navigation__label {\n    font-size: 1.1rem;\n    font-weight: 700;\n}\n\n.enhanced-calendar .react-calendar__month-view__weekdays {\n    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n    font-weight: 700;\n    color: #475569;\n    padding: 1rem 0;\n    border-bottom: 2px solid #e2e8f0;\n}\n\n.enhanced-calendar .react-calendar__month-view__weekdays__weekday {\n    padding: 0.75rem;\n    text-align: center;\n    font-size: 0.85rem;\n    text-transform: uppercase;\n    letter-spacing: 0.5px;\n}\n\n.enhanced-calendar .react-calendar__month-view__days {\n    background: white;\n}\n\n.enhanced-calendar .react-calendar__tile {\n    position: relative;\n    padding: 1rem 0.5rem;\n    background: white;\n    border: 1px solid #f1f5f9;\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    font-weight: 500;\n    min-height: 60px;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    cursor: pointer;\n}\n\n.enhanced-calendar .react-calendar__tile:hover {\n    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n    transform: translateY(-2px);\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n    border-color: #cbd5e1;\n}\n\n.enhanced-calendar .react-calendar__tile--active {\n    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;\n    color: white !important;\n    border-color: #1d4ed8 !important;\n    transform: translateY(-2px);\n    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);\n    font-weight: 700;\n}\n\n.enhanced-calendar .react-calendar__tile.present-day {\n    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);\n    border: 2px solid #16a34a;\n    color: #15803d;\n    font-weight: 600;\n}\n\n.enhanced-calendar .react-calendar__tile.present-day:hover {\n    background: linear-gradient(135deg, #bbf7d0 0%, #86efac 100%);\n    transform: translateY(-3px);\n    box-shadow: 0 6px 20px rgba(34, 197, 94, 0.3);\n}\n\n.enhanced-calendar .react-calendar__tile.absent-day {\n    background: linear-gradient(135deg, #fecaca 0%, #fca5a5 100%);\n    border: 2px solid #dc2626;\n    color: #b91c1c;\n    font-weight: 600;\n}\n\n.enhanced-calendar .react-calendar__tile.absent-day:hover {\n    background: linear-gradient(135deg, #fca5a5 0%, #f87171 100%);\n    transform: translateY(-3px);\n    box-shadow: 0 6px 20px rgba(220, 38, 38, 0.3);\n}\n\n.enhanced-calendar .react-calendar__tile.holiday-day {\n    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);\n    border: 2px solid #6b7280;\n    color: #4b5563;\n    font-weight: 600;\n}\n\n.enhanced-calendar .react-calendar__tile.holiday-day:hover {\n    background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%);\n    transform: translateY(-3px);\n    box-shadow: 0 6px 20px rgba(107, 114, 128, 0.3);\n}\n\n.enhanced-calendar .react-calendar__tile.mixed-day {\n    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);\n    border: 2px solid #d97706;\n    color: #92400e;\n    font-weight: 600;\n}\n\n.enhanced-calendar .react-calendar__tile.mixed-day:hover {\n    background: linear-gradient(135deg, #fde68a 0%, #fcd34d 100%);\n    transform: translateY(-3px);\n    box-shadow: 0 6px 20px rgba(217, 119, 6, 0.3);\n}\n\n.enhanced-calendar .react-calendar__tile.future-date {\n    background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);\n    color: #9ca3af;\n    border-color: #e5e7eb;\n    cursor: not-allowed;\n}\n\n.enhanced-calendar .react-calendar__tile.future-date:hover {\n    transform: none;\n    box-shadow: none;\n    background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);\n}\n\n.enhanced-calendar .react-calendar__tile.no-class {\n    background: white;\n    color: #9ca3af;\n    border-color: #f1f5f9;\n}\n\n.enhanced-calendar .react-calendar__tile.no-class:hover {\n    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\n}\n\n/* Add subtle animations */\n@keyframes pulse-dot {\n    0%, 100% { opacity: 1; transform: scale(1); }\n    50% { opacity: 0.7; transform: scale(1.1); }\n}\n\n.enhanced-calendar .react-calendar__tile div div {\n    animation: pulse-dot 2s infinite;\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n    .enhanced-calendar .react-calendar__tile {\n        min-height: 50px;\n        padding: 0.75rem 0.25rem;\n        font-size: 0.9rem;\n    }\n\n    .enhanced-calendar .react-calendar__navigation button {\n        padding: 0.5rem 1rem;\n        font-size: 0.8rem;\n    }\n}\n`;\n\n// Inject styles into the document\nif (typeof document !== 'undefined') {\n  const existingStyle = document.getElementById('calendar-styles');\n  if (!existingStyle) {\n    const styleElement = document.createElement('style');\n    styleElement.id = 'calendar-styles';\n    styleElement.textContent = calendarStyles;\n    document.head.appendChild(styleElement);\n  }\n}\nvar _c;\n$RefreshReg$(_c, \"AttendancePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Calendar", "Users", "BookOpen", "GraduationCap", "Filter", "Download", "TrendingUp", "Clock", "CheckCircle", "XCircle", "AlertCircle", "Info", "CalendarView", "jsxDEV", "_jsxDEV", "AttendancePage", "_s", "activeView", "setActiveView", "selected<PERSON><PERSON>", "setSelectedBatch", "selectedCourse", "setSelectedCourse", "selectedSubject", "setSelectedSubject", "date<PERSON><PERSON><PERSON>", "setDateRange", "selectedDate", "setSelectedDate", "Date", "attendanceData", "overall", "present", "absent", "total", "subjects", "name", "percentage", "batches", "students", "avgAttendance", "courses", "recentActivity", "date", "subject", "status", "time", "dailyRecords", "course", "batch", "type", "getStatusIcon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getAttendanceColor", "StatCard", "title", "value", "subtitle", "icon", "Icon", "trend", "children", "ProgressBar", "label", "style", "width", "getFilteredAttendanceData", "filter", "record", "courseMatch", "toLowerCase", "batchMatch", "subjectMatch", "getAttendanceForDate", "dateStr", "toISOString", "split", "filteredData", "dayRecords", "length", "some", "records", "presentCount", "absentCount", "getTileContent", "view", "attendance", "today", "isFuture", "getTileClassName", "handleDateChange", "calendarElement", "document", "querySelector", "transform", "setTimeout", "id", "map", "onClick", "onChange", "e", "target", "index", "classRecords", "totalClasses", "attendancePercentage", "Math", "round", "backgroundColor", "borderColor", "color", "toLocaleDateString", "month", "getDate", "tileContent", "tileClassName", "viewBox", "cx", "cy", "r", "fill", "stroke", "strokeWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeDashoffset", "x1", "y1", "x2", "y2", "offset", "stopColor", "isToday", "toDateString", "weekday", "year", "day", "activity", "_c", "calendarStyles", "existingStyle", "getElementById", "styleElement", "createElement", "textContent", "head", "append<PERSON><PERSON><PERSON>", "$RefreshReg$"], "sources": ["D:/techvritti/Collegemanagement/frontend/src/Screens/Student/AttendancePage.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport {\r\n    Calendar, Users, BookOpen, GraduationCap, Filter, Download,\r\n    TrendingUp, Clock, CheckCircle, XCircle, AlertCircle, Info\r\n} from 'lucide-react';\r\nimport 'react-calendar/dist/Calendar.css';\r\nimport CalendarView from 'react-calendar';\r\n\r\nconst AttendancePage = () => {\r\n    const [activeView, setActiveView] = useState('overall');\r\n    const [selectedBatch, setSelectedBatch] = useState('all');\r\n    const [selectedCourse, setSelectedCourse] = useState('all');\r\n    const [selectedSubject, setSelectedSubject] = useState('all');\r\n    const [dateRange, setDateRange] = useState('thisMonth');\r\n    const [selectedDate, setSelectedDate] = useState(new Date());\r\n    const [attendanceData] = useState({\r\n        overall: { present: 85, absent: 15, total: 100 },\r\n        subjects: [\r\n            { name: 'Mathematics', present: 18, total: 20, percentage: 90 },\r\n            { name: 'Physics', present: 16, total: 18, percentage: 89 },\r\n            { name: 'Chemistry', present: 14, total: 16, percentage: 88 },\r\n            { name: 'Computer Science', present: 19, total: 22, percentage: 86 },\r\n            { name: 'English', present: 15, total: 18, percentage: 83 }\r\n        ],\r\n        batches: [\r\n            { name: 'Batch A', students: 45, avgAttendance: 87 },\r\n            { name: 'Batch B', students: 42, avgAttendance: 85 },\r\n            { name: 'Batch C', students: 48, avgAttendance: 89 }\r\n        ],\r\n        courses: [\r\n            { name: 'Engineering', students: 135, avgAttendance: 87 },\r\n            { name: 'Science', students: 98, avgAttendance: 85 },\r\n            { name: 'Commerce', students: 76, avgAttendance: 91 }\r\n        ],\r\n        recentActivity: [\r\n            { date: '2024-03-15', subject: 'Mathematics', status: 'present', time: '09:00 AM' },\r\n            { date: '2024-03-15', subject: 'Physics', status: 'present', time: '10:30 AM' },\r\n            { date: '2024-03-14', subject: 'Chemistry', status: 'absent', time: '02:00 PM' },\r\n            { date: '2024-03-14', subject: 'Computer Science', status: 'present', time: '03:30 PM' },\r\n            { date: '2024-03-13', subject: 'English', status: 'present', time: '11:00 AM' }\r\n        ],\r\n        // Enhanced daily attendance records for calendar view\r\n        dailyRecords: [\r\n            // January 2024 - Engineering Course\r\n            { date: '2024-01-15', subject: 'Mathematics', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-15', subject: 'Physics', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-16', subject: 'Chemistry', status: 'Absent', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-16', subject: 'Mathematics', status: 'Present', course: 'Engineering', batch: 'Batch B', type: 'class' },\r\n            { date: '2024-01-17', subject: 'English', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-18', subject: 'Computer Science', status: 'Absent', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-19', subject: 'Physics', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-20', subject: 'Holiday', status: 'Holiday', course: 'all', batch: 'all', type: 'holiday' },\r\n            { date: '2024-01-21', subject: 'Holiday', status: 'Holiday', course: 'all', batch: 'all', type: 'holiday' },\r\n            { date: '2024-01-22', subject: 'Mathematics', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-23', subject: 'Chemistry', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-24', subject: 'English', status: 'Absent', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-25', subject: 'Computer Science', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-26', subject: 'Physics', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-29', subject: 'Mathematics', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-30', subject: 'Chemistry', status: 'Absent', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-31', subject: 'English', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },\r\n\r\n            // Science course records\r\n            { date: '2024-01-15', subject: 'Biology', status: 'Present', course: 'Science', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-16', subject: 'Chemistry', status: 'Present', course: 'Science', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-17', subject: 'Physics', status: 'Absent', course: 'Science', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-18', subject: 'Mathematics', status: 'Present', course: 'Science', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-19', subject: 'Biology', status: 'Present', course: 'Science', batch: 'Batch A', type: 'class' },\r\n\r\n            // Commerce course records\r\n            { date: '2024-01-15', subject: 'Accounting', status: 'Present', course: 'Commerce', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-16', subject: 'Economics', status: 'Present', course: 'Commerce', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-17', subject: 'Business Studies', status: 'Present', course: 'Commerce', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-18', subject: 'Accounting', status: 'Absent', course: 'Commerce', batch: 'Batch A', type: 'class' },\r\n            { date: '2024-01-19', subject: 'Economics', status: 'Present', course: 'Commerce', batch: 'Batch A', type: 'class' },\r\n        ]\r\n    });\r\n\r\n    const getStatusIcon = (status) => {\r\n        switch (status) {\r\n            case 'present': return <CheckCircle className=\"w-4 h-4 text-green-500\" />;\r\n            case 'absent': return <XCircle className=\"w-4 h-4 text-red-500\" />;\r\n            case 'late': return <AlertCircle className=\"w-4 h-4 text-yellow-500\" />;\r\n            default: return null;\r\n        }\r\n    };\r\n\r\n    const getAttendanceColor = (percentage) => {\r\n        if (percentage >= 90) return 'text-green-600 bg-green-50 border-green-200';\r\n        if (percentage >= 75) return 'text-yellow-600 bg-yellow-50 border-yellow-200';\r\n        return 'text-red-600 bg-red-50 border-red-200';\r\n    };\r\n\r\n    const StatCard = ({ title, value, subtitle, icon: Icon, trend }) => (\r\n        <div className=\"bg-gradient-to-br from-white via-blue-50 to-blue-100 rounded-xl p-6 shadow-md border border-blue-100 hover:shadow-lg transition-shadow\">\r\n            <div className=\"flex items-center justify-between mb-4\">\r\n                <div className=\"flex items-center space-x-3\">\r\n                    <div className=\"p-2 bg-blue-600 rounded-lg\">\r\n                        <Icon className=\"w-5 h-5 text-white\" />\r\n                    </div>\r\n                    <h3 className=\"text-blue-700 font-semibold\">{title}</h3>\r\n                </div>\r\n                {trend && <TrendingUp className=\"w-4 h-4 text-green-500\" />}\r\n            </div>\r\n            <div className=\"space-y-1\">\r\n                <p className=\"text-3xl font-extrabold text-blue-900\">{value}</p>\r\n                <p className=\"text-sm text-blue-600\">{subtitle}</p>\r\n            </div>\r\n        </div>\r\n    );\r\n\r\n    const ProgressBar = ({ percentage, label, total, present }) => (\r\n        <div className=\"space-y-2\">\r\n            <div className=\"flex justify-between items-center\">\r\n                <span className=\"text-sm font-semibold text-gray-700\">{label}</span>\r\n                <span className={`text-xs px-2 py-1 rounded-full border ${getAttendanceColor(percentage)}`}>\r\n                    {percentage}%\r\n                </span>\r\n            </div>\r\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\r\n                <div\r\n                    className={`h-2 rounded-full transition-all duration-300 ${percentage >= 90 ? 'bg-green-500' : percentage >= 75 ? 'bg-yellow-500' : 'bg-red-500'}`}\r\n                    style={{ width: `${percentage}%` }}\r\n                ></div>\r\n            </div>\r\n            <div className=\"flex justify-between text-xs text-gray-500\">\r\n                <span>Present: {present}</span>\r\n                <span>Total: {total}</span>\r\n            </div>\r\n        </div>\r\n    );\r\n\r\n    // Auto-switch to calendar view when filters are selected\r\n    useEffect(() => {\r\n        if (selectedCourse !== 'all' || selectedBatch !== 'all' || selectedSubject !== 'all') {\r\n            setActiveView('calendar');\r\n        }\r\n    }, [selectedCourse, selectedBatch, selectedSubject]);\r\n\r\n    // Filter attendance data based on selected filters\r\n    const getFilteredAttendanceData = () => {\r\n        return attendanceData.dailyRecords.filter(record => {\r\n            const courseMatch = selectedCourse === 'all' || record.course.toLowerCase() === selectedCourse.toLowerCase();\r\n            const batchMatch = selectedBatch === 'all' || record.batch.toLowerCase() === selectedBatch.toLowerCase();\r\n            const subjectMatch = selectedSubject === 'all' || record.subject.toLowerCase() === selectedSubject.toLowerCase();\r\n\r\n            return courseMatch && batchMatch && subjectMatch;\r\n        });\r\n    };\r\n\r\n    // Get attendance status for a specific date\r\n    const getAttendanceForDate = (date) => {\r\n        const dateStr = date.toISOString().split('T')[0];\r\n        const filteredData = getFilteredAttendanceData();\r\n        const dayRecords = filteredData.filter(record => record.date === dateStr);\r\n\r\n        if (dayRecords.length === 0) return null;\r\n\r\n        // Check if it's a holiday\r\n        if (dayRecords.some(record => record.type === 'holiday')) {\r\n            return { status: 'holiday', records: dayRecords };\r\n        }\r\n\r\n        // Calculate overall status for the day\r\n        const presentCount = dayRecords.filter(record => record.status === 'Present').length;\r\n        const absentCount = dayRecords.filter(record => record.status === 'Absent').length;\r\n\r\n        if (presentCount > 0 && absentCount === 0) return { status: 'present', records: dayRecords };\r\n        if (absentCount > 0 && presentCount === 0) return { status: 'absent', records: dayRecords };\r\n        if (presentCount > 0 && absentCount > 0) return { status: 'mixed', records: dayRecords };\r\n\r\n        return { status: 'unknown', records: dayRecords };\r\n    };\r\n\r\n    // Custom tile content for calendar (small dots)\r\n    const getTileContent = ({ date, view }) => {\r\n        if (view !== 'month') return null;\r\n\r\n        const attendance = getAttendanceForDate(date);\r\n        if (!attendance) return null;\r\n\r\n        const today = new Date();\r\n        const isFuture = date > today;\r\n\r\n        if (isFuture) return null;\r\n\r\n        return (\r\n            <div className=\"flex justify-center mt-1\">\r\n                <div className={`w-2 h-2 rounded-full ${\r\n                    attendance.status === 'present' ? 'bg-green-500' :\r\n                    attendance.status === 'absent' ? 'bg-red-500' :\r\n                    attendance.status === 'holiday' ? 'bg-gray-400' :\r\n                    attendance.status === 'mixed' ? 'bg-yellow-500' :\r\n                    'bg-blue-400'\r\n                }`} />\r\n            </div>\r\n        );\r\n    };\r\n\r\n    // Custom tile class name for calendar styling\r\n    const getTileClassName = ({ date, view }) => {\r\n        if (view !== 'month') return null;\r\n\r\n        const attendance = getAttendanceForDate(date);\r\n        const today = new Date();\r\n        const isFuture = date > today;\r\n\r\n        if (isFuture) {\r\n            return 'future-date';\r\n        }\r\n\r\n        if (!attendance) return 'no-class';\r\n\r\n        switch (attendance.status) {\r\n            case 'present':\r\n                return 'present-day';\r\n            case 'absent':\r\n                return 'absent-day';\r\n            case 'holiday':\r\n                return 'holiday-day';\r\n            case 'mixed':\r\n                return 'mixed-day';\r\n            default:\r\n                return 'no-class';\r\n        }\r\n    };\r\n\r\n    const handleDateChange = (date) => {\r\n        setSelectedDate(date);\r\n\r\n        // Add a subtle animation effect when date is selected\r\n        const calendarElement = document.querySelector('.enhanced-calendar');\r\n        if (calendarElement) {\r\n            calendarElement.style.transform = 'scale(0.98)';\r\n            setTimeout(() => {\r\n                calendarElement.style.transform = 'scale(1)';\r\n            }, 150);\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div className=\"min-h-screen bg-gradient-to-tr from-blue-50 via-white to-pink-50 p-6\">\r\n            <div className=\"max-w-7xl mx-auto space-y-8\">\r\n                {/* Header */}\r\n                <div className=\"bg-white rounded-2xl p-8 shadow-lg border border-blue-100\">\r\n                    <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6\">\r\n                        <div>\r\n                            <h1 className=\"text-4xl font-extrabold text-blue-800 leading-tight\">Attendance Dashboard</h1>\r\n                            <p className=\"text-blue-600 mt-2 text-lg\">Monitor attendance by batch, course, and subject.</p>\r\n                        </div>\r\n                        <div className=\"flex items-center gap-3\">\r\n                            <button className=\"flex items-center gap-2 px-5 py-2 bg-blue-700 text-white rounded-xl shadow hover:bg-blue-800 transition-colors\">\r\n                                <Download className=\"w-4 h-4\" />\r\n                                <span>Export</span>\r\n                            </button>\r\n                            <button className=\"flex items-center gap-2 px-5 py-2 border border-blue-300 text-blue-700 rounded-xl hover:bg-blue-50 transition-colors\">\r\n                                <Filter className=\"w-4 h-4\" />\r\n                                <span>Filter</span>\r\n                            </button>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* View Toggle */}\r\n                <div className=\"bg-white rounded-2xl p-2 shadow-sm border border-blue-100\">\r\n                    <div className=\"flex flex-wrap gap-2 justify-center\">\r\n                        {[\r\n                            { id: 'overall', label: 'Overall', icon: TrendingUp },\r\n                            { id: 'subjects', label: 'By Subject', icon: BookOpen },\r\n                            { id: 'batches', label: 'By Batch', icon: Users },\r\n                            { id: 'courses', label: 'By Course', icon: GraduationCap },\r\n                            { id: 'calendar', label: 'Calendar View', icon: Calendar }\r\n                        ].map(({ id, label, icon: Icon }) => (\r\n                            <button\r\n                                key={id}\r\n                                onClick={() => setActiveView(id)}\r\n                                className={`flex items-center gap-2 px-5 py-2 rounded-xl font-medium text-base transition-all\r\n                  ${activeView === id\r\n                                        ? 'bg-blue-700 text-white shadow'\r\n                                        : 'text-blue-700 hover:bg-blue-100'\r\n                                    }`}\r\n                            >\r\n                                <Icon className=\"w-4 h-4\" />\r\n                                <span>{label}</span>\r\n                            </button>\r\n                        ))}\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Filters */}\r\n                <div className=\"bg-white rounded-2xl p-6 shadow-md border border-blue-100\">\r\n                    <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\r\n                        <div>\r\n                            <label className=\"block text-sm font-semibold text-blue-700 mb-2\">Date Range</label>\r\n                            <select\r\n                                value={dateRange}\r\n                                onChange={(e) => setDateRange(e.target.value)}\r\n                                className=\"w-full px-3 py-2 border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                            >\r\n                                <option value=\"today\">Today</option>\r\n                                <option value=\"thisWeek\">This Week</option>\r\n                                <option value=\"thisMonth\">This Month</option>\r\n                                <option value=\"lastMonth\">Last Month</option>\r\n                                <option value=\"thisYear\">This Year</option>\r\n                            </select>\r\n                        </div>\r\n                        <div>\r\n                            <label className=\"block text-sm font-semibold text-blue-700 mb-2\">Batch</label>\r\n                            <select\r\n                                value={selectedBatch}\r\n                                onChange={(e) => {\r\n                                    setSelectedBatch(e.target.value);\r\n                                    // Add smooth transition to calendar view\r\n                                    if (e.target.value !== 'all') {\r\n                                        setTimeout(() => setActiveView('calendar'), 300);\r\n                                    }\r\n                                }}\r\n                                className=\"w-full px-3 py-2 border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\r\n                            >\r\n                                <option value=\"all\">All Batches</option>\r\n                                <option value=\"Batch A\">Batch A</option>\r\n                                <option value=\"Batch B\">Batch B</option>\r\n                                <option value=\"Batch C\">Batch C</option>\r\n                            </select>\r\n                        </div>\r\n                        <div>\r\n                            <label className=\"block text-sm font-semibold text-blue-700 mb-2\">Course</label>\r\n                            <select\r\n                                value={selectedCourse}\r\n                                onChange={(e) => {\r\n                                    setSelectedCourse(e.target.value);\r\n                                    // Add smooth transition to calendar view\r\n                                    if (e.target.value !== 'all') {\r\n                                        setTimeout(() => setActiveView('calendar'), 300);\r\n                                    }\r\n                                }}\r\n                                className=\"w-full px-3 py-2 border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\r\n                            >\r\n                                <option value=\"all\">All Courses</option>\r\n                                <option value=\"Engineering\">Engineering</option>\r\n                                <option value=\"Science\">Science</option>\r\n                                <option value=\"Commerce\">Commerce</option>\r\n                            </select>\r\n                        </div>\r\n                        <div>\r\n                            <label className=\"block text-sm font-semibold text-blue-700 mb-2\">Subject</label>\r\n                            <select\r\n                                value={selectedSubject}\r\n                                onChange={(e) => {\r\n                                    setSelectedSubject(e.target.value);\r\n                                    // Add smooth transition to calendar view\r\n                                    if (e.target.value !== 'all') {\r\n                                        setTimeout(() => setActiveView('calendar'), 300);\r\n                                    }\r\n                                }}\r\n                                className=\"w-full px-3 py-2 border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\r\n                            >\r\n                                <option value=\"all\">All Subjects</option>\r\n                                <option value=\"Mathematics\">Mathematics</option>\r\n                                <option value=\"Physics\">Physics</option>\r\n                                <option value=\"Chemistry\">Chemistry</option>\r\n                                <option value=\"Computer Science\">Computer Science</option>\r\n                                <option value=\"English\">English</option>\r\n                                <option value=\"Biology\">Biology</option>\r\n                                <option value=\"Accounting\">Accounting</option>\r\n                                <option value=\"Economics\">Economics</option>\r\n                                <option value=\"Business Studies\">Business Studies</option>\r\n                            </select>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Stats Cards */}\r\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\r\n                    <StatCard\r\n                        title=\"Overall Attendance\"\r\n                        value=\"85%\"\r\n                        subtitle=\"This month average\"\r\n                        icon={TrendingUp}\r\n                        trend={true}\r\n                    />\r\n                    <StatCard\r\n                        title=\"Total Classes\"\r\n                        value=\"124\"\r\n                        subtitle=\"Classes conducted\"\r\n                        icon={Calendar}\r\n                    />\r\n                    <StatCard\r\n                        title=\"Present Days\"\r\n                        value=\"105\"\r\n                        subtitle=\"Out of 124 classes\"\r\n                        icon={CheckCircle}\r\n                    />\r\n                </div>\r\n\r\n                {/* Main Content */}\r\n                <div className=\"grid grid-cols-1 xl:grid-cols-3 gap-8\">\r\n                    {/* Left Column - Main Data */}\r\n                    <div className=\"xl:col-span-2 space-y-8\">\r\n                        {activeView === 'overall' && (\r\n                            <div className=\"bg-white rounded-2xl p-8 shadow-md border border-blue-100\">\r\n                                <h3 className=\"text-xl font-bold text-blue-900 mb-6\">Overall Attendance Summary</h3>\r\n                                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\r\n                                    <div className=\"text-center p-8 bg-gradient-to-tr from-blue-50 via-blue-100 to-white rounded-xl shadow\">\r\n                                        <div className=\"text-5xl font-extrabold text-blue-700 mb-2 drop-shadow\">85%</div>\r\n                                        <div className=\"text-blue-800 font-semibold\">Overall Attendance</div>\r\n                                    </div>\r\n                                    <div className=\"space-y-5\">\r\n                                        <div className=\"flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-xl shadow-sm\">\r\n                                            <div className=\"flex items-center gap-3\">\r\n                                                <CheckCircle className=\"w-8 h-8 text-green-600\" />\r\n                                                <div>\r\n                                                    <div className=\"font-semibold text-green-900\">Present</div>\r\n                                                    <div className=\"text-sm text-green-700\">Days attended</div>\r\n                                                </div>\r\n                                            </div>\r\n                                            <div className=\"text-2xl font-extrabold text-green-600\">105</div>\r\n                                        </div>\r\n                                        <div className=\"flex items-center justify-between p-4 bg-red-50 border border-red-200 rounded-xl shadow-sm\">\r\n                                            <div className=\"flex items-center gap-3\">\r\n                                                <XCircle className=\"w-8 h-8 text-red-600\" />\r\n                                                <div>\r\n                                                    <div className=\"font-semibold text-red-900\">Absent</div>\r\n                                                    <div className=\"text-sm text-red-700\">Days missed</div>\r\n                                                </div>\r\n                                            </div>\r\n                                            <div className=\"text-2xl font-extrabold text-red-600\">19</div>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        )}\r\n\r\n                        {activeView === 'subjects' && (\r\n                            <div className=\"bg-white rounded-2xl p-8 shadow-md border border-blue-100\">\r\n                                <h3 className=\"text-xl font-bold text-blue-900 mb-6\">Subject-wise Attendance</h3>\r\n                                <div className=\"space-y-6\">\r\n                                    {attendanceData.subjects.map((subject, index) => (\r\n                                        <ProgressBar\r\n                                            key={index}\r\n                                            percentage={subject.percentage}\r\n                                            label={subject.name}\r\n                                            total={subject.total}\r\n                                            present={subject.present}\r\n                                        />\r\n                                    ))}\r\n                                </div>\r\n                            </div>\r\n                        )}\r\n\r\n                        {activeView === 'batches' && (\r\n                            <div className=\"bg-white rounded-2xl p-8 shadow-md border border-blue-100\">\r\n                                <h3 className=\"text-xl font-bold text-blue-900 mb-6\">Batch-wise Attendance</h3>\r\n                                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\r\n                                    {attendanceData.batches.map((batch, index) => (\r\n                                        <div key={index} className=\"p-6 bg-blue-50 rounded-xl border border-blue-200 shadow hover:scale-105 transition-transform\">\r\n                                            <div className=\"flex items-center justify-between mb-3\">\r\n                                                <h4 className=\"font-semibold text-blue-900\">{batch.name}</h4>\r\n                                                <Users className=\"w-5 h-5 text-blue-400\" />\r\n                                            </div>\r\n                                            <div className=\"space-y-2\">\r\n                                                <div className=\"text-2xl font-bold text-blue-900\">{batch.avgAttendance}%</div>\r\n                                                <div className=\"text-sm text-blue-700\">{batch.students} students</div>\r\n                                                <div className=\"w-full bg-blue-200 rounded-full h-2\">\r\n                                                    <div\r\n                                                        className=\"h-2 bg-blue-600 rounded-full transition-all duration-300\"\r\n                                                        style={{ width: `${batch.avgAttendance}%` }}\r\n                                                    ></div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                    ))}\r\n                                </div>\r\n                            </div>\r\n                        )}\r\n\r\n                        {activeView === 'courses' && (\r\n                            <div className=\"bg-white rounded-2xl p-8 shadow-md border border-blue-100\">\r\n                                <h3 className=\"text-xl font-bold text-blue-900 mb-6\">Course-wise Attendance</h3>\r\n                                <div className=\"space-y-6\">\r\n                                    {attendanceData.courses.map((course, index) => (\r\n                                        <div key={index} className=\"flex items-center justify-between p-5 border border-blue-200 rounded-xl bg-blue-50 hover:bg-blue-100 transition-colors shadow\">\r\n                                            <div className=\"flex items-center gap-4\">\r\n                                                <div className=\"p-3 bg-blue-700 rounded-lg\">\r\n                                                    <GraduationCap className=\"w-6 h-6 text-white\" />\r\n                                                </div>\r\n                                                <div>\r\n                                                    <h4 className=\"font-semibold text-blue-900\">{course.name}</h4>\r\n                                                    <p className=\"text-sm text-blue-700\">{course.students} students enrolled</p>\r\n                                                </div>\r\n                                            </div>\r\n                                            <div>\r\n                                                <div className={`text-lg font-bold px-4 py-1 rounded-full border ${getAttendanceColor(course.avgAttendance)}`}>\r\n                                                    {course.avgAttendance}%\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                    ))}\r\n                                </div>\r\n                            </div>\r\n                        )}\r\n\r\n                        {activeView === 'calendar' && (\r\n                            <div className=\"space-y-8\">\r\n                                {/* Enhanced Filtered Statistics Summary */}\r\n                                {(selectedCourse !== 'all' || selectedBatch !== 'all' || selectedSubject !== 'all') && (() => {\r\n                                    const filteredData = getFilteredAttendanceData();\r\n                                    const classRecords = filteredData.filter(record => record.type === 'class');\r\n                                    const presentCount = classRecords.filter(record => record.status === 'Present').length;\r\n                                    const absentCount = classRecords.filter(record => record.status === 'Absent').length;\r\n                                    const totalClasses = classRecords.length;\r\n                                    const attendancePercentage = totalClasses > 0 ? Math.round((presentCount / totalClasses) * 100) : 0;\r\n\r\n                                    return (\r\n                                        <div className=\"bg-yellow-50 rounded-3xl p-8 shadow-xl border border-yellow-200 backdrop-blur-sm\" style={{backgroundColor: '#fcc250', borderColor: '#fcc250'}}>\r\n                                            <div className=\"flex items-center justify-between mb-6\">\r\n                                                <div>\r\n                                                    <h4 className=\"text-2xl font-bold mb-2\" style={{color: '#29354d'}}>Filtered Attendance Summary</h4>\r\n                                                    <div className=\"flex flex-wrap gap-2 text-sm\">\r\n                                                        {selectedCourse !== 'all' && (\r\n                                                            <span className=\"px-3 py-1 rounded-full border\" style={{backgroundColor: '#29354d', color: '#fcc250', borderColor: '#29354d'}}>\r\n                                                                Course: {selectedCourse}\r\n                                                            </span>\r\n                                                        )}\r\n                                                        {selectedBatch !== 'all' && (\r\n                                                            <span className=\"px-3 py-1 rounded-full border\" style={{backgroundColor: '#29354d', color: '#fcc250', borderColor: '#29354d'}}>\r\n                                                                Batch: {selectedBatch}\r\n                                                            </span>\r\n                                                        )}\r\n                                                        {selectedSubject !== 'all' && (\r\n                                                            <span className=\"px-3 py-1 rounded-full border\" style={{backgroundColor: '#29354d', color: '#fcc250', borderColor: '#29354d'}}>\r\n                                                                Subject: {selectedSubject}\r\n                                                            </span>\r\n                                                        )}\r\n                                                    </div>\r\n                                                </div>\r\n                                                <div className=\"hidden md:block\">\r\n                                                    <div className=\"w-16 h-16 rounded-2xl flex items-center justify-center shadow-lg\" style={{backgroundColor: '#29354d'}}>\r\n                                                        <TrendingUp className=\"w-8 h-8\" style={{color: '#fcc250'}} />\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n\r\n                                            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\r\n                                                <div className=\"group relative overflow-hidden bg-white rounded-2xl p-6 shadow-lg border hover:shadow-xl transition-all duration-300 hover:-translate-y-1\" style={{borderColor: '#29354d'}}>\r\n                                                    <div className=\"absolute top-0 right-0 w-20 h-20 rounded-bl-3xl opacity-10\" style={{backgroundColor: '#29354d'}}></div>\r\n                                                    <div className=\"relative\">\r\n                                                        <div className=\"flex items-center justify-between mb-3\">\r\n                                                            <div className=\"p-2 rounded-xl\" style={{backgroundColor: '#fcc250'}}>\r\n                                                                <TrendingUp className=\"w-5 h-5\" style={{color: '#29354d'}} />\r\n                                                            </div>\r\n                                                            <div className={`text-xs px-2 py-1 rounded-full ${\r\n                                                                attendancePercentage >= 90 ? 'bg-green-100 text-green-700' :\r\n                                                                attendancePercentage >= 75 ? 'text-white' :\r\n                                                                'bg-red-100 text-red-700'\r\n                                                            }`} style={attendancePercentage >= 75 && attendancePercentage < 90 ? {backgroundColor: '#fcc250', color: '#29354d'} : {}}>\r\n                                                                {attendancePercentage >= 90 ? 'Excellent' : attendancePercentage >= 75 ? 'Good' : 'Needs Improvement'}\r\n                                                            </div>\r\n                                                        </div>\r\n                                                        <div className=\"text-3xl font-bold mb-1\" style={{color: '#29354d'}}>{attendancePercentage}%</div>\r\n                                                        <div className=\"text-sm font-medium\" style={{color: '#29354d'}}>Attendance Rate</div>\r\n                                                    </div>\r\n                                                </div>\r\n\r\n                                                <div className=\"group relative overflow-hidden bg-white rounded-2xl p-6 shadow-lg border hover:shadow-xl transition-all duration-300 hover:-translate-y-1\" style={{borderColor: '#29354d'}}>\r\n                                                    <div className=\"absolute top-0 right-0 w-20 h-20 rounded-bl-3xl opacity-10\" style={{backgroundColor: '#fcc250'}}></div>\r\n                                                    <div className=\"relative\">\r\n                                                        <div className=\"flex items-center justify-between mb-3\">\r\n                                                            <div className=\"p-2 rounded-xl\" style={{backgroundColor: '#29354d'}}>\r\n                                                                <CheckCircle className=\"w-5 h-5\" style={{color: '#fcc250'}} />\r\n                                                            </div>\r\n                                                            <div className=\"text-xs px-2 py-1 rounded-full\" style={{backgroundColor: '#fcc250', color: '#29354d'}}>\r\n                                                                {totalClasses > 0 ? Math.round((presentCount / totalClasses) * 100) : 0}%\r\n                                                            </div>\r\n                                                        </div>\r\n                                                        <div className=\"text-3xl font-bold mb-1\" style={{color: '#29354d'}}>{presentCount}</div>\r\n                                                        <div className=\"text-sm font-medium\" style={{color: '#29354d'}}>Present Days</div>\r\n                                                    </div>\r\n                                                </div>\r\n\r\n                                                <div className=\"group relative overflow-hidden bg-white rounded-2xl p-6 shadow-lg border hover:shadow-xl transition-all duration-300 hover:-translate-y-1\" style={{borderColor: '#29354d'}}>\r\n                                                    <div className=\"absolute top-0 right-0 w-20 h-20 rounded-bl-3xl opacity-10\" style={{backgroundColor: '#29354d'}}></div>\r\n                                                    <div className=\"relative\">\r\n                                                        <div className=\"flex items-center justify-between mb-3\">\r\n                                                            <div className=\"p-2 rounded-xl\" style={{backgroundColor: '#fcc250'}}>\r\n                                                                <XCircle className=\"w-5 h-5\" style={{color: '#29354d'}} />\r\n                                                            </div>\r\n                                                            <div className=\"text-xs px-2 py-1 rounded-full\" style={{backgroundColor: '#29354d', color: '#fcc250'}}>\r\n                                                                {totalClasses > 0 ? Math.round((absentCount / totalClasses) * 100) : 0}%\r\n                                                            </div>\r\n                                                        </div>\r\n                                                        <div className=\"text-3xl font-bold mb-1\" style={{color: '#29354d'}}>{absentCount}</div>\r\n                                                        <div className=\"text-sm font-medium\" style={{color: '#29354d'}}>Absent Days</div>\r\n                                                    </div>\r\n                                                </div>\r\n\r\n                                                <div className=\"group relative overflow-hidden bg-white rounded-2xl p-6 shadow-lg border hover:shadow-xl transition-all duration-300 hover:-translate-y-1\" style={{borderColor: '#29354d'}}>\r\n                                                    <div className=\"absolute top-0 right-0 w-20 h-20 rounded-bl-3xl opacity-10\" style={{backgroundColor: '#fcc250'}}></div>\r\n                                                    <div className=\"relative\">\r\n                                                        <div className=\"flex items-center justify-between mb-3\">\r\n                                                            <div className=\"p-2 rounded-xl\" style={{backgroundColor: '#29354d'}}>\r\n                                                                <Calendar className=\"w-5 h-5\" style={{color: '#fcc250'}} />\r\n                                                            </div>\r\n                                                            <div className=\"text-xs px-2 py-1 rounded-full\" style={{backgroundColor: '#fcc250', color: '#29354d'}}>\r\n                                                                100%\r\n                                                            </div>\r\n                                                        </div>\r\n                                                        <div className=\"text-3xl font-bold mb-1\" style={{color: '#29354d'}}>{totalClasses}</div>\r\n                                                        <div className=\"text-sm font-medium\" style={{color: '#29354d'}}>Total Classes</div>\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                    );\r\n                                })()}\r\n\r\n                                <div className=\"bg-gradient-to-br from-white via-blue-50/30 to-indigo-50/50 rounded-3xl p-8 shadow-2xl border border-blue-100/50 backdrop-blur-sm\">\r\n                                    {/* Header Section */}\r\n                                    <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8\">\r\n                                        <div className=\"flex items-center gap-4\">\r\n                                            <div className=\"p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl shadow-lg\">\r\n                                                <Calendar className=\"w-7 h-7 text-white\" />\r\n                                            </div>\r\n                                            <div>\r\n                                                <h3 className=\"text-2xl font-bold text-blue-900\">Interactive Calendar</h3>\r\n                                                <p className=\"text-blue-600 text-sm mt-1\">\r\n                                                    {(selectedCourse === 'all' && selectedBatch === 'all' && selectedSubject === 'all')\r\n                                                        ? 'Showing all attendance records'\r\n                                                        : 'Filtered attendance view'}\r\n                                                </p>\r\n                                            </div>\r\n                                        </div>\r\n\r\n                                        {/* Quick Stats */}\r\n                                        <div className=\"flex items-center gap-4 mt-4 lg:mt-0\">\r\n                                            <div className=\"text-center\">\r\n                                                <div className=\"text-lg font-bold text-blue-700\">{new Date().toLocaleDateString('en-US', { month: 'short' })}</div>\r\n                                                <div className=\"text-xs text-blue-600\">Current Month</div>\r\n                                            </div>\r\n                                            <div className=\"w-px h-8 bg-blue-200\"></div>\r\n                                            <div className=\"text-center\">\r\n                                                <div className=\"text-lg font-bold text-blue-700\">{selectedDate.getDate()}</div>\r\n                                                <div className=\"text-xs text-blue-600\">Selected</div>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                    {/* Enhanced Calendar Legend */}\r\n                                    <div className=\"mb-8 p-6 bg-gradient-to-r from-slate-50 to-blue-50 rounded-2xl border border-slate-200 shadow-inner\">\r\n                                        <div className=\"flex items-center justify-between mb-4\">\r\n                                            <h4 className=\"text-lg font-bold text-slate-700 flex items-center gap-2\">\r\n                                                <Info className=\"w-5 h-5 text-blue-500\" />\r\n                                                Calendar Legend\r\n                                            </h4>\r\n                                            <div className=\"text-xs text-slate-500 bg-white px-3 py-1 rounded-full border\">\r\n                                                Click dates for details\r\n                                            </div>\r\n                                        </div>\r\n                                        <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4\">\r\n                                            <div className=\"flex items-center gap-3 p-3 bg-white rounded-xl shadow-sm border border-green-100 hover:shadow-md transition-shadow\">\r\n                                                <div className=\"w-6 h-6 bg-gradient-to-br from-green-200 to-green-300 border-2 border-green-400 rounded-lg shadow-sm\"></div>\r\n                                                <div>\r\n                                                    <span className=\"font-semibold text-green-700\">Present</span>\r\n                                                    <div className=\"text-xs text-green-600\">Attended class</div>\r\n                                                </div>\r\n                                            </div>\r\n                                            <div className=\"flex items-center gap-3 p-3 bg-white rounded-xl shadow-sm border border-red-100 hover:shadow-md transition-shadow\">\r\n                                                <div className=\"w-6 h-6 bg-gradient-to-br from-red-200 to-red-300 border-2 border-red-400 rounded-lg shadow-sm\"></div>\r\n                                                <div>\r\n                                                    <span className=\"font-semibold text-red-700\">Absent</span>\r\n                                                    <div className=\"text-xs text-red-600\">Missed class</div>\r\n                                                </div>\r\n                                            </div>\r\n                                            <div className=\"flex items-center gap-3 p-3 bg-white rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow\">\r\n                                                <div className=\"w-6 h-6 bg-gradient-to-br from-gray-200 to-gray-300 border-2 border-gray-400 rounded-lg shadow-sm\"></div>\r\n                                                <div>\r\n                                                    <span className=\"font-semibold text-gray-700\">Holiday</span>\r\n                                                    <div className=\"text-xs text-gray-600\">No classes</div>\r\n                                                </div>\r\n                                            </div>\r\n                                            <div className=\"flex items-center gap-3 p-3 bg-white rounded-xl shadow-sm border border-yellow-100 hover:shadow-md transition-shadow\">\r\n                                                <div className=\"w-6 h-6 bg-gradient-to-br from-yellow-200 to-yellow-300 border-2 border-yellow-400 rounded-lg shadow-sm\"></div>\r\n                                                <div>\r\n                                                    <span className=\"font-semibold text-yellow-700\">Mixed</span>\r\n                                                    <div className=\"text-xs text-yellow-600\">Partial attendance</div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                    {/* Revolutionary Calendar Container */}\r\n                                    <div className=\"calendar-container relative\">\r\n                                        {/* Floating Background Elements */}\r\n                                        <div className=\"absolute -top-4 -left-4 w-32 h-32 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-full blur-2xl\"></div>\r\n                                        <div className=\"absolute -bottom-4 -right-4 w-40 h-40 bg-gradient-to-br from-blue-400/20 to-cyan-400/20 rounded-full blur-2xl\"></div>\r\n                                        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-br from-indigo-300/10 to-purple-300/10 rounded-full blur-3xl -z-10\"></div>\r\n\r\n                                        {/* Main Calendar Card */}\r\n                                        <div className=\"relative bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 overflow-hidden\">\r\n                                            {/* Decorative Header Strip */}\r\n                                            <div className=\"h-2 bg-blue-600\"></div>\r\n\r\n                                            {/* Calendar Content */}\r\n                                            <div className=\"p-6\">\r\n                                                <CalendarView\r\n                                                    value={selectedDate}\r\n                                                    onChange={handleDateChange}\r\n                                                    tileContent={getTileContent}\r\n                                                    tileClassName={getTileClassName}\r\n                                                    className=\"futuristic-calendar w-full\"\r\n                                                />\r\n                                            </div>\r\n\r\n                                            {/* Bottom Accent */}\r\n                                            <div className=\"h-1 bg-gradient-to-r from-transparent via-blue-400/50 to-transparent\"></div>\r\n                                        </div>\r\n\r\n                                        {/* Floating Action Buttons */}\r\n                                        <div className=\"absolute -right-4 top-1/2 transform -translate-y-1/2 space-y-3\">\r\n                                            <button className=\"group w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 flex items-center justify-center\">\r\n                                                <Calendar className=\"w-5 h-5 text-white group-hover:rotate-12 transition-transform\" />\r\n                                            </button>\r\n                                            <button className=\"group w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 flex items-center justify-center\">\r\n                                                <TrendingUp className=\"w-5 h-5 text-white group-hover:rotate-12 transition-transform\" />\r\n                                            </button>\r\n                                            <button className=\"group w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 flex items-center justify-center\">\r\n                                                <Download className=\"w-5 h-5 text-white group-hover:rotate-12 transition-transform\" />\r\n                                            </button>\r\n                                        </div>\r\n\r\n                                        {/* Interactive Status Indicators */}\r\n                                        <div className=\"mt-8 grid grid-cols-2 lg:grid-cols-4 gap-4\">\r\n                                            <div className=\"group relative overflow-hidden bg-gradient-to-br from-green-50 to-emerald-100 rounded-2xl p-4 border border-green-200 hover:shadow-lg transition-all duration-300 cursor-pointer\">\r\n                                                <div className=\"absolute top-0 right-0 w-8 h-8 bg-green-400/20 rounded-bl-2xl\"></div>\r\n                                                <div className=\"flex items-center gap-3\">\r\n                                                    <div className=\"w-3 h-3 bg-green-500 rounded-full animate-pulse\"></div>\r\n                                                    <span className=\"text-green-700 font-medium\">Present Days</span>\r\n                                                </div>\r\n                                                <div className=\"mt-2 text-xs text-green-600\">Click to highlight</div>\r\n                                            </div>\r\n\r\n                                            <div className=\"group relative overflow-hidden bg-gradient-to-br from-red-50 to-rose-100 rounded-2xl p-4 border border-red-200 hover:shadow-lg transition-all duration-300 cursor-pointer\">\r\n                                                <div className=\"absolute top-0 right-0 w-8 h-8 bg-red-400/20 rounded-bl-2xl\"></div>\r\n                                                <div className=\"flex items-center gap-3\">\r\n                                                    <div className=\"w-3 h-3 bg-red-500 rounded-full animate-pulse\"></div>\r\n                                                    <span className=\"text-red-700 font-medium\">Absent Days</span>\r\n                                                </div>\r\n                                                <div className=\"mt-2 text-xs text-red-600\">Click to highlight</div>\r\n                                            </div>\r\n\r\n                                            <div className=\"group relative overflow-hidden bg-gradient-to-br from-gray-50 to-slate-100 rounded-2xl p-4 border border-gray-200 hover:shadow-lg transition-all duration-300 cursor-pointer\">\r\n                                                <div className=\"absolute top-0 right-0 w-8 h-8 bg-gray-400/20 rounded-bl-2xl\"></div>\r\n                                                <div className=\"flex items-center gap-3\">\r\n                                                    <div className=\"w-3 h-3 bg-gray-500 rounded-full animate-pulse\"></div>\r\n                                                    <span className=\"text-gray-700 font-medium\">Holidays</span>\r\n                                                </div>\r\n                                                <div className=\"mt-2 text-xs text-gray-600\">Click to highlight</div>\r\n                                            </div>\r\n\r\n                                            <div className=\"group relative overflow-hidden bg-gradient-to-br from-yellow-50 to-amber-100 rounded-2xl p-4 border border-yellow-200 hover:shadow-lg transition-all duration-300 cursor-pointer\">\r\n                                                <div className=\"absolute top-0 right-0 w-8 h-8 bg-yellow-400/20 rounded-bl-2xl\"></div>\r\n                                                <div className=\"flex items-center gap-3\">\r\n                                                    <div className=\"w-3 h-3 bg-yellow-500 rounded-full animate-pulse\"></div>\r\n                                                    <span className=\"text-yellow-700 font-medium\">Mixed Days</span>\r\n                                                </div>\r\n                                                <div className=\"mt-2 text-xs text-yellow-600\">Click to highlight</div>\r\n                                            </div>\r\n                                        </div>\r\n\r\n                                        {/* Animated Progress Ring */}\r\n                                        <div className=\"absolute -left-8 top-8 w-16 h-16\">\r\n                                            <svg className=\"w-16 h-16 transform -rotate-90\" viewBox=\"0 0 64 64\">\r\n                                                <circle cx=\"32\" cy=\"32\" r=\"28\" fill=\"none\" stroke=\"#e5e7eb\" strokeWidth=\"4\"/>\r\n                                                <circle\r\n                                                    cx=\"32\"\r\n                                                    cy=\"32\"\r\n                                                    r=\"28\"\r\n                                                    fill=\"none\"\r\n                                                    stroke=\"url(#gradient)\"\r\n                                                    strokeWidth=\"4\"\r\n                                                    strokeDasharray=\"175.93\"\r\n                                                    strokeDashoffset=\"35\"\r\n                                                    className=\"animate-pulse\"\r\n                                                />\r\n                                                <defs>\r\n                                                    <linearGradient id=\"gradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\r\n                                                        <stop offset=\"0%\" stopColor=\"#3b82f6\"/>\r\n                                                        <stop offset=\"100%\" stopColor=\"#8b5cf6\"/>\r\n                                                    </linearGradient>\r\n                                                </defs>\r\n                                            </svg>\r\n                                            <div className=\"absolute inset-0 flex items-center justify-center\">\r\n                                                <span className=\"text-xs font-bold text-blue-600\">80%</span>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                    {/* Enhanced Selected Date Details */}\r\n                                    {selectedDate && (() => {\r\n                                        const attendance = getAttendanceForDate(selectedDate);\r\n                                        const today = new Date();\r\n                                        const isToday = selectedDate.toDateString() === today.toDateString();\r\n                                        const isFuture = selectedDate > today;\r\n\r\n                                        return (\r\n                                            <div className=\"mt-8 space-y-4\">\r\n                                                {/* Date Header */}\r\n                                                <div className=\"bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl p-6 text-white shadow-xl\">\r\n                                                    <div className=\"flex items-center justify-between\">\r\n                                                        <div>\r\n                                                            <h4 className=\"text-2xl font-bold mb-1\">\r\n                                                                {selectedDate.toLocaleDateString('en-US', {\r\n                                                                    weekday: 'long',\r\n                                                                    year: 'numeric',\r\n                                                                    month: 'long',\r\n                                                                    day: 'numeric'\r\n                                                                })}\r\n                                                            </h4>\r\n                                                            <div className=\"flex items-center gap-2 text-indigo-100\">\r\n                                                                {isToday && (\r\n                                                                    <span className=\"px-2 py-1 bg-white/20 rounded-full text-xs font-medium\">\r\n                                                                        Today\r\n                                                                    </span>\r\n                                                                )}\r\n                                                                {isFuture && (\r\n                                                                    <span className=\"px-2 py-1 bg-white/20 rounded-full text-xs font-medium\">\r\n                                                                        Future Date\r\n                                                                    </span>\r\n                                                                )}\r\n                                                                {attendance && (\r\n                                                                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${\r\n                                                                        attendance.status === 'present' ? 'bg-green-400/30 text-green-100' :\r\n                                                                        attendance.status === 'absent' ? 'bg-red-400/30 text-red-100' :\r\n                                                                        attendance.status === 'holiday' ? 'bg-gray-400/30 text-gray-100' :\r\n                                                                        'bg-yellow-400/30 text-yellow-100'\r\n                                                                    }`}>\r\n                                                                        {attendance.status === 'present' ? 'Present Day' :\r\n                                                                         attendance.status === 'absent' ? 'Absent Day' :\r\n                                                                         attendance.status === 'holiday' ? 'Holiday' :\r\n                                                                         'Mixed Attendance'}\r\n                                                                    </span>\r\n                                                                )}\r\n                                                            </div>\r\n                                                        </div>\r\n                                                        <div className=\"text-right\">\r\n                                                            <div className=\"text-3xl font-bold\">{selectedDate.getDate()}</div>\r\n                                                            <div className=\"text-sm text-indigo-200\">\r\n                                                                {selectedDate.toLocaleDateString('en-US', { month: 'short' })}\r\n                                                            </div>\r\n                                                        </div>\r\n                                                    </div>\r\n                                                </div>\r\n\r\n                                                {/* Attendance Details */}\r\n                                                {attendance && attendance.records.length > 0 ? (\r\n                                                    <div className=\"bg-white rounded-2xl p-6 shadow-lg border border-gray-100\">\r\n                                                        <div className=\"flex items-center justify-between mb-6\">\r\n                                                            <h5 className=\"text-lg font-bold text-gray-900\">Class Details</h5>\r\n                                                            <div className=\"text-sm text-gray-500\">\r\n                                                                {attendance.records.length} {attendance.records.length === 1 ? 'class' : 'classes'}\r\n                                                            </div>\r\n                                                        </div>\r\n                                                        <div className=\"space-y-3\">\r\n                                                            {attendance.records.map((record, index) => (\r\n                                                                <div key={index} className={`group relative overflow-hidden rounded-xl border-2 transition-all duration-300 hover:shadow-lg ${\r\n                                                                    record.status === 'Present' ? 'border-green-200 bg-gradient-to-r from-green-50 to-green-100/50 hover:border-green-300' :\r\n                                                                    record.status === 'Absent' ? 'border-red-200 bg-gradient-to-r from-red-50 to-red-100/50 hover:border-red-300' :\r\n                                                                    'border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100/50 hover:border-gray-300'\r\n                                                                }`}>\r\n                                                                    <div className=\"p-4\">\r\n                                                                        <div className=\"flex items-center justify-between\">\r\n                                                                            <div className=\"flex items-center gap-4\">\r\n                                                                                <div className={`p-3 rounded-xl shadow-sm ${\r\n                                                                                    record.status === 'Present' ? 'bg-green-100' :\r\n                                                                                    record.status === 'Absent' ? 'bg-red-100' :\r\n                                                                                    'bg-gray-100'\r\n                                                                                }`}>\r\n                                                                                    {getStatusIcon(record.status.toLowerCase())}\r\n                                                                                </div>\r\n                                                                                <div>\r\n                                                                                    <h6 className=\"font-bold text-gray-900 text-lg\">{record.subject}</h6>\r\n                                                                                    <div className=\"flex items-center gap-2 text-sm text-gray-600 mt-1\">\r\n                                                                                        <span className=\"px-2 py-1 bg-white/70 rounded-lg border\">\r\n                                                                                            {record.course}\r\n                                                                                        </span>\r\n                                                                                        <span className=\"px-2 py-1 bg-white/70 rounded-lg border\">\r\n                                                                                            {record.batch}\r\n                                                                                        </span>\r\n                                                                                        {record.type === 'holiday' && (\r\n                                                                                            <span className=\"px-2 py-1 bg-blue-100 text-blue-700 rounded-lg border border-blue-200\">\r\n                                                                                                Holiday\r\n                                                                                            </span>\r\n                                                                                        )}\r\n                                                                                    </div>\r\n                                                                                </div>\r\n                                                                            </div>\r\n                                                                            <div className=\"text-right\">\r\n                                                                                <span className={`inline-flex items-center px-4 py-2 rounded-xl font-semibold text-sm border-2 ${\r\n                                                                                    record.status === 'Present' ? 'bg-green-100 text-green-800 border-green-300' :\r\n                                                                                    record.status === 'Absent' ? 'bg-red-100 text-red-800 border-red-300' :\r\n                                                                                    'bg-gray-100 text-gray-800 border-gray-300'\r\n                                                                                }`}>\r\n                                                                                    {record.status}\r\n                                                                                </span>\r\n                                                                            </div>\r\n                                                                        </div>\r\n                                                                    </div>\r\n                                                                    {/* Decorative gradient overlay */}\r\n                                                                    <div className={`absolute top-0 right-0 w-1 h-full ${\r\n                                                                        record.status === 'Present' ? 'bg-gradient-to-b from-green-400 to-green-600' :\r\n                                                                        record.status === 'Absent' ? 'bg-gradient-to-b from-red-400 to-red-600' :\r\n                                                                        'bg-gradient-to-b from-gray-400 to-gray-600'\r\n                                                                    }`}></div>\r\n                                                                </div>\r\n                                                            ))}\r\n                                                        </div>\r\n                                                    </div>\r\n                                                ) : isFuture ? (\r\n                                                    <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8 text-center border border-blue-100\">\r\n                                                        <div className=\"w-16 h-16 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-2xl mx-auto mb-4 flex items-center justify-center\">\r\n                                                            <Calendar className=\"w-8 h-8 text-white\" />\r\n                                                        </div>\r\n                                                        <h5 className=\"text-lg font-bold text-blue-900 mb-2\">Future Date</h5>\r\n                                                        <p className=\"text-blue-600\">No attendance data available for future dates.</p>\r\n                                                    </div>\r\n                                                ) : (\r\n                                                    <div className=\"bg-gradient-to-r from-gray-50 to-slate-50 rounded-2xl p-8 text-center border border-gray-200\">\r\n                                                        <div className=\"w-16 h-16 bg-gradient-to-br from-gray-400 to-slate-500 rounded-2xl mx-auto mb-4 flex items-center justify-center\">\r\n                                                            <Calendar className=\"w-8 h-8 text-white\" />\r\n                                                        </div>\r\n                                                        <h5 className=\"text-lg font-bold text-gray-700 mb-2\">No Classes</h5>\r\n                                                        <p className=\"text-gray-600\">No classes were scheduled for this date.</p>\r\n                                                    </div>\r\n                                                )}\r\n                                            </div>\r\n                                        );\r\n                                    })()}\r\n                                </div>\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n\r\n                    {/* Right Column - Recent Activity */}\r\n                    <div className=\"bg-white rounded-2xl p-8 shadow-md border border-blue-100\">\r\n                        <h3 className=\"text-xl font-bold text-blue-900 mb-6 flex items-center gap-2\">\r\n                            <Clock className=\"w-5 h-5 text-blue-700\" />\r\n                            <span>Recent Activity</span>\r\n                        </h3>\r\n                        <div className=\"space-y-5\">\r\n                            {attendanceData.recentActivity.map((activity, index) => (\r\n                                <div key={index} className=\"flex items-center gap-3 p-4 bg-blue-50 rounded-xl border hover:bg-blue-100 transition-colors\">\r\n                                    {getStatusIcon(activity.status)}\r\n                                    <div className=\"flex-1\">\r\n                                        <div className=\"font-semibold text-blue-900\">{activity.subject}</div>\r\n                                        <div className=\"text-sm text-blue-700\">{activity.date} • {activity.time}</div>\r\n                                    </div>\r\n                                    <div className={`text-xs px-3 py-1 rounded-full capitalize border\r\n                    ${activity.status === 'present' ? 'bg-green-100 text-green-800 border-green-300' :\r\n                                            activity.status === 'absent' ? 'bg-red-100 text-red-800 border-red-300' :\r\n                                                'bg-yellow-100 text-yellow-800 border-yellow-300'\r\n                                        }`}>\r\n                                        {activity.status}\r\n                                    </div>\r\n                                </div>\r\n                            ))}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default AttendancePage;\r\n\r\n// Futuristic CSS styles for revolutionary calendar design\r\nconst calendarStyles = `\r\n.futuristic-calendar {\r\n    width: 100%;\r\n    border: none;\r\n    font-family: inherit;\r\n    background: transparent;\r\n}\r\n\r\n.enhanced-calendar {\r\n    width: 100%;\r\n    border: none;\r\n    font-family: inherit;\r\n    background: transparent;\r\n}\r\n\r\n.enhanced-calendar .react-calendar__navigation {\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    border-radius: 1rem 1rem 0 0;\r\n    padding: 1rem;\r\n    margin-bottom: 0;\r\n}\r\n\r\n.enhanced-calendar .react-calendar__navigation button {\r\n    background: rgba(255, 255, 255, 0.2);\r\n    color: white;\r\n    border: 2px solid rgba(255, 255, 255, 0.3);\r\n    padding: 0.75rem 1.5rem;\r\n    border-radius: 0.75rem;\r\n    font-weight: 600;\r\n    font-size: 0.9rem;\r\n    transition: all 0.3s ease;\r\n    backdrop-filter: blur(10px);\r\n}\r\n\r\n.enhanced-calendar .react-calendar__navigation button:hover {\r\n    background: rgba(255, 255, 255, 0.3);\r\n    border-color: rgba(255, 255, 255, 0.5);\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.enhanced-calendar .react-calendar__navigation button:disabled {\r\n    background: rgba(255, 255, 255, 0.1);\r\n    border-color: rgba(255, 255, 255, 0.2);\r\n    color: rgba(255, 255, 255, 0.5);\r\n    transform: none;\r\n    box-shadow: none;\r\n}\r\n\r\n.enhanced-calendar .react-calendar__navigation__label {\r\n    font-size: 1.1rem;\r\n    font-weight: 700;\r\n}\r\n\r\n.enhanced-calendar .react-calendar__month-view__weekdays {\r\n    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\r\n    font-weight: 700;\r\n    color: #475569;\r\n    padding: 1rem 0;\r\n    border-bottom: 2px solid #e2e8f0;\r\n}\r\n\r\n.enhanced-calendar .react-calendar__month-view__weekdays__weekday {\r\n    padding: 0.75rem;\r\n    text-align: center;\r\n    font-size: 0.85rem;\r\n    text-transform: uppercase;\r\n    letter-spacing: 0.5px;\r\n}\r\n\r\n.enhanced-calendar .react-calendar__month-view__days {\r\n    background: white;\r\n}\r\n\r\n.enhanced-calendar .react-calendar__tile {\r\n    position: relative;\r\n    padding: 1rem 0.5rem;\r\n    background: white;\r\n    border: 1px solid #f1f5f9;\r\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n    font-weight: 500;\r\n    min-height: 60px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    cursor: pointer;\r\n}\r\n\r\n.enhanced-calendar .react-calendar__tile:hover {\r\n    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n    border-color: #cbd5e1;\r\n}\r\n\r\n.enhanced-calendar .react-calendar__tile--active {\r\n    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;\r\n    color: white !important;\r\n    border-color: #1d4ed8 !important;\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);\r\n    font-weight: 700;\r\n}\r\n\r\n.enhanced-calendar .react-calendar__tile.present-day {\r\n    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);\r\n    border: 2px solid #16a34a;\r\n    color: #15803d;\r\n    font-weight: 600;\r\n}\r\n\r\n.enhanced-calendar .react-calendar__tile.present-day:hover {\r\n    background: linear-gradient(135deg, #bbf7d0 0%, #86efac 100%);\r\n    transform: translateY(-3px);\r\n    box-shadow: 0 6px 20px rgba(34, 197, 94, 0.3);\r\n}\r\n\r\n.enhanced-calendar .react-calendar__tile.absent-day {\r\n    background: linear-gradient(135deg, #fecaca 0%, #fca5a5 100%);\r\n    border: 2px solid #dc2626;\r\n    color: #b91c1c;\r\n    font-weight: 600;\r\n}\r\n\r\n.enhanced-calendar .react-calendar__tile.absent-day:hover {\r\n    background: linear-gradient(135deg, #fca5a5 0%, #f87171 100%);\r\n    transform: translateY(-3px);\r\n    box-shadow: 0 6px 20px rgba(220, 38, 38, 0.3);\r\n}\r\n\r\n.enhanced-calendar .react-calendar__tile.holiday-day {\r\n    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);\r\n    border: 2px solid #6b7280;\r\n    color: #4b5563;\r\n    font-weight: 600;\r\n}\r\n\r\n.enhanced-calendar .react-calendar__tile.holiday-day:hover {\r\n    background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%);\r\n    transform: translateY(-3px);\r\n    box-shadow: 0 6px 20px rgba(107, 114, 128, 0.3);\r\n}\r\n\r\n.enhanced-calendar .react-calendar__tile.mixed-day {\r\n    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);\r\n    border: 2px solid #d97706;\r\n    color: #92400e;\r\n    font-weight: 600;\r\n}\r\n\r\n.enhanced-calendar .react-calendar__tile.mixed-day:hover {\r\n    background: linear-gradient(135deg, #fde68a 0%, #fcd34d 100%);\r\n    transform: translateY(-3px);\r\n    box-shadow: 0 6px 20px rgba(217, 119, 6, 0.3);\r\n}\r\n\r\n.enhanced-calendar .react-calendar__tile.future-date {\r\n    background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);\r\n    color: #9ca3af;\r\n    border-color: #e5e7eb;\r\n    cursor: not-allowed;\r\n}\r\n\r\n.enhanced-calendar .react-calendar__tile.future-date:hover {\r\n    transform: none;\r\n    box-shadow: none;\r\n    background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);\r\n}\r\n\r\n.enhanced-calendar .react-calendar__tile.no-class {\r\n    background: white;\r\n    color: #9ca3af;\r\n    border-color: #f1f5f9;\r\n}\r\n\r\n.enhanced-calendar .react-calendar__tile.no-class:hover {\r\n    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\r\n}\r\n\r\n/* Add subtle animations */\r\n@keyframes pulse-dot {\r\n    0%, 100% { opacity: 1; transform: scale(1); }\r\n    50% { opacity: 0.7; transform: scale(1.1); }\r\n}\r\n\r\n.enhanced-calendar .react-calendar__tile div div {\r\n    animation: pulse-dot 2s infinite;\r\n}\r\n\r\n/* Responsive adjustments */\r\n@media (max-width: 768px) {\r\n    .enhanced-calendar .react-calendar__tile {\r\n        min-height: 50px;\r\n        padding: 0.75rem 0.25rem;\r\n        font-size: 0.9rem;\r\n    }\r\n\r\n    .enhanced-calendar .react-calendar__navigation button {\r\n        padding: 0.5rem 1rem;\r\n        font-size: 0.8rem;\r\n    }\r\n}\r\n`;\r\n\r\n// Inject styles into the document\r\nif (typeof document !== 'undefined') {\r\n    const existingStyle = document.getElementById('calendar-styles');\r\n    if (!existingStyle) {\r\n        const styleElement = document.createElement('style');\r\n        styleElement.id = 'calendar-styles';\r\n        styleElement.textContent = calendarStyles;\r\n        document.head.appendChild(styleElement);\r\n    }\r\n}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACIC,QAAQ,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,MAAM,EAAEC,QAAQ,EAC1DC,UAAU,EAAEC,KAAK,EAAEC,WAAW,EAAEC,OAAO,EAAEC,WAAW,EAAEC,IAAI,QACvD,cAAc;AACrB,OAAO,kCAAkC;AACzC,OAAOC,YAAY,MAAM,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,SAAS,CAAC;EACvD,MAAM,CAACqB,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACuB,cAAc,EAAEC,iBAAiB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACyB,eAAe,EAAEC,kBAAkB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,WAAW,CAAC;EACvD,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC,IAAI+B,IAAI,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACC,cAAc,CAAC,GAAGhC,QAAQ,CAAC;IAC9BiC,OAAO,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAEC,MAAM,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAI,CAAC;IAChDC,QAAQ,EAAE,CACN;MAAEC,IAAI,EAAE,aAAa;MAAEJ,OAAO,EAAE,EAAE;MAAEE,KAAK,EAAE,EAAE;MAAEG,UAAU,EAAE;IAAG,CAAC,EAC/D;MAAED,IAAI,EAAE,SAAS;MAAEJ,OAAO,EAAE,EAAE;MAAEE,KAAK,EAAE,EAAE;MAAEG,UAAU,EAAE;IAAG,CAAC,EAC3D;MAAED,IAAI,EAAE,WAAW;MAAEJ,OAAO,EAAE,EAAE;MAAEE,KAAK,EAAE,EAAE;MAAEG,UAAU,EAAE;IAAG,CAAC,EAC7D;MAAED,IAAI,EAAE,kBAAkB;MAAEJ,OAAO,EAAE,EAAE;MAAEE,KAAK,EAAE,EAAE;MAAEG,UAAU,EAAE;IAAG,CAAC,EACpE;MAAED,IAAI,EAAE,SAAS;MAAEJ,OAAO,EAAE,EAAE;MAAEE,KAAK,EAAE,EAAE;MAAEG,UAAU,EAAE;IAAG,CAAC,CAC9D;IACDC,OAAO,EAAE,CACL;MAAEF,IAAI,EAAE,SAAS;MAAEG,QAAQ,EAAE,EAAE;MAAEC,aAAa,EAAE;IAAG,CAAC,EACpD;MAAEJ,IAAI,EAAE,SAAS;MAAEG,QAAQ,EAAE,EAAE;MAAEC,aAAa,EAAE;IAAG,CAAC,EACpD;MAAEJ,IAAI,EAAE,SAAS;MAAEG,QAAQ,EAAE,EAAE;MAAEC,aAAa,EAAE;IAAG,CAAC,CACvD;IACDC,OAAO,EAAE,CACL;MAAEL,IAAI,EAAE,aAAa;MAAEG,QAAQ,EAAE,GAAG;MAAEC,aAAa,EAAE;IAAG,CAAC,EACzD;MAAEJ,IAAI,EAAE,SAAS;MAAEG,QAAQ,EAAE,EAAE;MAAEC,aAAa,EAAE;IAAG,CAAC,EACpD;MAAEJ,IAAI,EAAE,UAAU;MAAEG,QAAQ,EAAE,EAAE;MAAEC,aAAa,EAAE;IAAG,CAAC,CACxD;IACDE,cAAc,EAAE,CACZ;MAAEC,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,aAAa;MAAEC,MAAM,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAW,CAAC,EACnF;MAAEH,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAW,CAAC,EAC/E;MAAEH,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,WAAW;MAAEC,MAAM,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAW,CAAC,EAChF;MAAEH,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,kBAAkB;MAAEC,MAAM,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAW,CAAC,EACxF;MAAEH,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAW,CAAC,CAClF;IACD;IACAC,YAAY,EAAE;IACV;IACA;MAAEJ,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,aAAa;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACzH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACrH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,WAAW;MAAEC,MAAM,EAAE,QAAQ;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACtH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,aAAa;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACzH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACrH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,kBAAkB;MAAEC,MAAM,EAAE,QAAQ;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EAC7H;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACrH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,KAAK;MAAEC,KAAK,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAU,CAAC,EAC3G;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,KAAK;MAAEC,KAAK,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAU,CAAC,EAC3G;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,aAAa;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACzH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,WAAW;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACvH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,QAAQ;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACpH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,kBAAkB;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EAC9H;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACrH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,aAAa;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACzH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,WAAW;MAAEC,MAAM,EAAE,QAAQ;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACtH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC;IAErH;IACA;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,SAAS;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACjH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,WAAW;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,SAAS;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACnH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,QAAQ;MAAEG,MAAM,EAAE,SAAS;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EAChH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,aAAa;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,SAAS;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACrH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,SAAS;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC;IAEjH;IACA;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,YAAY;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,UAAU;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACrH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,WAAW;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,UAAU;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACpH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,kBAAkB;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,UAAU;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EAC3H;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,YAAY;MAAEC,MAAM,EAAE,QAAQ;MAAEG,MAAM,EAAE,UAAU;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACpH;MAAEP,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,WAAW;MAAEC,MAAM,EAAE,SAAS;MAAEG,MAAM,EAAE,UAAU;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAQ,CAAC;EAE5H,CAAC,CAAC;EAEF,MAAMC,aAAa,GAAIN,MAAM,IAAK;IAC9B,QAAQA,MAAM;MACV,KAAK,SAAS;QAAE,oBAAO/B,OAAA,CAACN,WAAW;UAAC4C,SAAS,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzE,KAAK,QAAQ;QAAE,oBAAO1C,OAAA,CAACL,OAAO;UAAC2C,SAAS,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAClE,KAAK,MAAM;QAAE,oBAAO1C,OAAA,CAACJ,WAAW;UAAC0C,SAAS,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvE;QAAS,OAAO,IAAI;IACxB;EACJ,CAAC;EAED,MAAMC,kBAAkB,GAAIpB,UAAU,IAAK;IACvC,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO,6CAA6C;IAC1E,IAAIA,UAAU,IAAI,EAAE,EAAE,OAAO,gDAAgD;IAC7E,OAAO,uCAAuC;EAClD,CAAC;EAED,MAAMqB,QAAQ,GAAGA,CAAC;IAAEC,KAAK;IAAEC,KAAK;IAAEC,QAAQ;IAAEC,IAAI,EAAEC,IAAI;IAAEC;EAAM,CAAC,kBAC3DlD,OAAA;IAAKsC,SAAS,EAAC,wIAAwI;IAAAa,QAAA,gBACnJnD,OAAA;MAAKsC,SAAS,EAAC,wCAAwC;MAAAa,QAAA,gBACnDnD,OAAA;QAAKsC,SAAS,EAAC,6BAA6B;QAAAa,QAAA,gBACxCnD,OAAA;UAAKsC,SAAS,EAAC,4BAA4B;UAAAa,QAAA,eACvCnD,OAAA,CAACiD,IAAI;YAACX,SAAS,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eACN1C,OAAA;UAAIsC,SAAS,EAAC,6BAA6B;UAAAa,QAAA,EAAEN;QAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,EACLQ,KAAK,iBAAIlD,OAAA,CAACR,UAAU;QAAC8C,SAAS,EAAC;MAAwB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CAAC,eACN1C,OAAA;MAAKsC,SAAS,EAAC,WAAW;MAAAa,QAAA,gBACtBnD,OAAA;QAAGsC,SAAS,EAAC,uCAAuC;QAAAa,QAAA,EAAEL;MAAK;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChE1C,OAAA;QAAGsC,SAAS,EAAC,uBAAuB;QAAAa,QAAA,EAAEJ;MAAQ;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACR;EAED,MAAMU,WAAW,GAAGA,CAAC;IAAE7B,UAAU;IAAE8B,KAAK;IAAEjC,KAAK;IAAEF;EAAQ,CAAC,kBACtDlB,OAAA;IAAKsC,SAAS,EAAC,WAAW;IAAAa,QAAA,gBACtBnD,OAAA;MAAKsC,SAAS,EAAC,mCAAmC;MAAAa,QAAA,gBAC9CnD,OAAA;QAAMsC,SAAS,EAAC,qCAAqC;QAAAa,QAAA,EAAEE;MAAK;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACpE1C,OAAA;QAAMsC,SAAS,EAAG,yCAAwCK,kBAAkB,CAACpB,UAAU,CAAE,EAAE;QAAA4B,QAAA,GACtF5B,UAAU,EAAC,GAChB;MAAA;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eACN1C,OAAA;MAAKsC,SAAS,EAAC,qCAAqC;MAAAa,QAAA,eAChDnD,OAAA;QACIsC,SAAS,EAAG,gDAA+Cf,UAAU,IAAI,EAAE,GAAG,cAAc,GAAGA,UAAU,IAAI,EAAE,GAAG,eAAe,GAAG,YAAa,EAAE;QACnJ+B,KAAK,EAAE;UAAEC,KAAK,EAAG,GAAEhC,UAAW;QAAG;MAAE;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eACN1C,OAAA;MAAKsC,SAAS,EAAC,4CAA4C;MAAAa,QAAA,gBACvDnD,OAAA;QAAAmD,QAAA,GAAM,WAAS,EAACjC,OAAO;MAAA;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC/B1C,OAAA;QAAAmD,QAAA,GAAM,SAAO,EAAC/B,KAAK;MAAA;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACR;;EAED;EACAzD,SAAS,CAAC,MAAM;IACZ,IAAIsB,cAAc,KAAK,KAAK,IAAIF,aAAa,KAAK,KAAK,IAAII,eAAe,KAAK,KAAK,EAAE;MAClFL,aAAa,CAAC,UAAU,CAAC;IAC7B;EACJ,CAAC,EAAE,CAACG,cAAc,EAAEF,aAAa,EAAEI,eAAe,CAAC,CAAC;;EAEpD;EACA,MAAM+C,yBAAyB,GAAGA,CAAA,KAAM;IACpC,OAAOxC,cAAc,CAACiB,YAAY,CAACwB,MAAM,CAACC,MAAM,IAAI;MAChD,MAAMC,WAAW,GAAGpD,cAAc,KAAK,KAAK,IAAImD,MAAM,CAACxB,MAAM,CAAC0B,WAAW,CAAC,CAAC,KAAKrD,cAAc,CAACqD,WAAW,CAAC,CAAC;MAC5G,MAAMC,UAAU,GAAGxD,aAAa,KAAK,KAAK,IAAIqD,MAAM,CAACvB,KAAK,CAACyB,WAAW,CAAC,CAAC,KAAKvD,aAAa,CAACuD,WAAW,CAAC,CAAC;MACxG,MAAME,YAAY,GAAGrD,eAAe,KAAK,KAAK,IAAIiD,MAAM,CAAC5B,OAAO,CAAC8B,WAAW,CAAC,CAAC,KAAKnD,eAAe,CAACmD,WAAW,CAAC,CAAC;MAEhH,OAAOD,WAAW,IAAIE,UAAU,IAAIC,YAAY;IACpD,CAAC,CAAC;EACN,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAIlC,IAAI,IAAK;IACnC,MAAMmC,OAAO,GAAGnC,IAAI,CAACoC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAChD,MAAMC,YAAY,GAAGX,yBAAyB,CAAC,CAAC;IAChD,MAAMY,UAAU,GAAGD,YAAY,CAACV,MAAM,CAACC,MAAM,IAAIA,MAAM,CAAC7B,IAAI,KAAKmC,OAAO,CAAC;IAEzE,IAAII,UAAU,CAACC,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;;IAExC;IACA,IAAID,UAAU,CAACE,IAAI,CAACZ,MAAM,IAAIA,MAAM,CAACtB,IAAI,KAAK,SAAS,CAAC,EAAE;MACtD,OAAO;QAAEL,MAAM,EAAE,SAAS;QAAEwC,OAAO,EAAEH;MAAW,CAAC;IACrD;;IAEA;IACA,MAAMI,YAAY,GAAGJ,UAAU,CAACX,MAAM,CAACC,MAAM,IAAIA,MAAM,CAAC3B,MAAM,KAAK,SAAS,CAAC,CAACsC,MAAM;IACpF,MAAMI,WAAW,GAAGL,UAAU,CAACX,MAAM,CAACC,MAAM,IAAIA,MAAM,CAAC3B,MAAM,KAAK,QAAQ,CAAC,CAACsC,MAAM;IAElF,IAAIG,YAAY,GAAG,CAAC,IAAIC,WAAW,KAAK,CAAC,EAAE,OAAO;MAAE1C,MAAM,EAAE,SAAS;MAAEwC,OAAO,EAAEH;IAAW,CAAC;IAC5F,IAAIK,WAAW,GAAG,CAAC,IAAID,YAAY,KAAK,CAAC,EAAE,OAAO;MAAEzC,MAAM,EAAE,QAAQ;MAAEwC,OAAO,EAAEH;IAAW,CAAC;IAC3F,IAAII,YAAY,GAAG,CAAC,IAAIC,WAAW,GAAG,CAAC,EAAE,OAAO;MAAE1C,MAAM,EAAE,OAAO;MAAEwC,OAAO,EAAEH;IAAW,CAAC;IAExF,OAAO;MAAErC,MAAM,EAAE,SAAS;MAAEwC,OAAO,EAAEH;IAAW,CAAC;EACrD,CAAC;;EAED;EACA,MAAMM,cAAc,GAAGA,CAAC;IAAE7C,IAAI;IAAE8C;EAAK,CAAC,KAAK;IACvC,IAAIA,IAAI,KAAK,OAAO,EAAE,OAAO,IAAI;IAEjC,MAAMC,UAAU,GAAGb,oBAAoB,CAAClC,IAAI,CAAC;IAC7C,IAAI,CAAC+C,UAAU,EAAE,OAAO,IAAI;IAE5B,MAAMC,KAAK,GAAG,IAAI9D,IAAI,CAAC,CAAC;IACxB,MAAM+D,QAAQ,GAAGjD,IAAI,GAAGgD,KAAK;IAE7B,IAAIC,QAAQ,EAAE,OAAO,IAAI;IAEzB,oBACI9E,OAAA;MAAKsC,SAAS,EAAC,0BAA0B;MAAAa,QAAA,eACrCnD,OAAA;QAAKsC,SAAS,EAAG,wBACbsC,UAAU,CAAC7C,MAAM,KAAK,SAAS,GAAG,cAAc,GAChD6C,UAAU,CAAC7C,MAAM,KAAK,QAAQ,GAAG,YAAY,GAC7C6C,UAAU,CAAC7C,MAAM,KAAK,SAAS,GAAG,aAAa,GAC/C6C,UAAU,CAAC7C,MAAM,KAAK,OAAO,GAAG,eAAe,GAC/C,aACH;MAAE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd,CAAC;;EAED;EACA,MAAMqC,gBAAgB,GAAGA,CAAC;IAAElD,IAAI;IAAE8C;EAAK,CAAC,KAAK;IACzC,IAAIA,IAAI,KAAK,OAAO,EAAE,OAAO,IAAI;IAEjC,MAAMC,UAAU,GAAGb,oBAAoB,CAAClC,IAAI,CAAC;IAC7C,MAAMgD,KAAK,GAAG,IAAI9D,IAAI,CAAC,CAAC;IACxB,MAAM+D,QAAQ,GAAGjD,IAAI,GAAGgD,KAAK;IAE7B,IAAIC,QAAQ,EAAE;MACV,OAAO,aAAa;IACxB;IAEA,IAAI,CAACF,UAAU,EAAE,OAAO,UAAU;IAElC,QAAQA,UAAU,CAAC7C,MAAM;MACrB,KAAK,SAAS;QACV,OAAO,aAAa;MACxB,KAAK,QAAQ;QACT,OAAO,YAAY;MACvB,KAAK,SAAS;QACV,OAAO,aAAa;MACxB,KAAK,OAAO;QACR,OAAO,WAAW;MACtB;QACI,OAAO,UAAU;IACzB;EACJ,CAAC;EAED,MAAMiD,gBAAgB,GAAInD,IAAI,IAAK;IAC/Bf,eAAe,CAACe,IAAI,CAAC;;IAErB;IACA,MAAMoD,eAAe,GAAGC,QAAQ,CAACC,aAAa,CAAC,oBAAoB,CAAC;IACpE,IAAIF,eAAe,EAAE;MACjBA,eAAe,CAAC3B,KAAK,CAAC8B,SAAS,GAAG,aAAa;MAC/CC,UAAU,CAAC,MAAM;QACbJ,eAAe,CAAC3B,KAAK,CAAC8B,SAAS,GAAG,UAAU;MAChD,CAAC,EAAE,GAAG,CAAC;IACX;EACJ,CAAC;EAED,oBACIpF,OAAA;IAAKsC,SAAS,EAAC,sEAAsE;IAAAa,QAAA,eACjFnD,OAAA;MAAKsC,SAAS,EAAC,6BAA6B;MAAAa,QAAA,gBAExCnD,OAAA;QAAKsC,SAAS,EAAC,2DAA2D;QAAAa,QAAA,eACtEnD,OAAA;UAAKsC,SAAS,EAAC,oEAAoE;UAAAa,QAAA,gBAC/EnD,OAAA;YAAAmD,QAAA,gBACInD,OAAA;cAAIsC,SAAS,EAAC,qDAAqD;cAAAa,QAAA,EAAC;YAAoB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7F1C,OAAA;cAAGsC,SAAS,EAAC,4BAA4B;cAAAa,QAAA,EAAC;YAAiD;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9F,CAAC,eACN1C,OAAA;YAAKsC,SAAS,EAAC,yBAAyB;YAAAa,QAAA,gBACpCnD,OAAA;cAAQsC,SAAS,EAAC,gHAAgH;cAAAa,QAAA,gBAC9HnD,OAAA,CAACT,QAAQ;gBAAC+C,SAAS,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChC1C,OAAA;gBAAAmD,QAAA,EAAM;cAAM;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACT1C,OAAA;cAAQsC,SAAS,EAAC,sHAAsH;cAAAa,QAAA,gBACpInD,OAAA,CAACV,MAAM;gBAACgD,SAAS,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9B1C,OAAA;gBAAAmD,QAAA,EAAM;cAAM;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGN1C,OAAA;QAAKsC,SAAS,EAAC,2DAA2D;QAAAa,QAAA,eACtEnD,OAAA;UAAKsC,SAAS,EAAC,qCAAqC;UAAAa,QAAA,EAC/C,CACG;YAAEmC,EAAE,EAAE,SAAS;YAAEjC,KAAK,EAAE,SAAS;YAAEL,IAAI,EAAExD;UAAW,CAAC,EACrD;YAAE8F,EAAE,EAAE,UAAU;YAAEjC,KAAK,EAAE,YAAY;YAAEL,IAAI,EAAE5D;UAAS,CAAC,EACvD;YAAEkG,EAAE,EAAE,SAAS;YAAEjC,KAAK,EAAE,UAAU;YAAEL,IAAI,EAAE7D;UAAM,CAAC,EACjD;YAAEmG,EAAE,EAAE,SAAS;YAAEjC,KAAK,EAAE,WAAW;YAAEL,IAAI,EAAE3D;UAAc,CAAC,EAC1D;YAAEiG,EAAE,EAAE,UAAU;YAAEjC,KAAK,EAAE,eAAe;YAAEL,IAAI,EAAE9D;UAAS,CAAC,CAC7D,CAACqG,GAAG,CAAC,CAAC;YAAED,EAAE;YAAEjC,KAAK;YAAEL,IAAI,EAAEC;UAAK,CAAC,kBAC5BjD,OAAA;YAEIwF,OAAO,EAAEA,CAAA,KAAMpF,aAAa,CAACkF,EAAE,CAAE;YACjChD,SAAS,EAAG;AAC5C,oBAAoBnC,UAAU,KAAKmF,EAAE,GACK,+BAA+B,GAC/B,iCACL,EAAE;YAAAnC,QAAA,gBAEPnD,OAAA,CAACiD,IAAI;cAACX,SAAS,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5B1C,OAAA;cAAAmD,QAAA,EAAOE;YAAK;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GATf4C,EAAE;YAAA/C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUH,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGN1C,OAAA;QAAKsC,SAAS,EAAC,2DAA2D;QAAAa,QAAA,eACtEnD,OAAA;UAAKsC,SAAS,EAAC,uCAAuC;UAAAa,QAAA,gBAClDnD,OAAA;YAAAmD,QAAA,gBACInD,OAAA;cAAOsC,SAAS,EAAC,gDAAgD;cAAAa,QAAA,EAAC;YAAU;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpF1C,OAAA;cACI8C,KAAK,EAAEnC,SAAU;cACjB8E,QAAQ,EAAGC,CAAC,IAAK9E,YAAY,CAAC8E,CAAC,CAACC,MAAM,CAAC7C,KAAK,CAAE;cAC9CR,SAAS,EAAC,8GAA8G;cAAAa,QAAA,gBAExHnD,OAAA;gBAAQ8C,KAAK,EAAC,OAAO;gBAAAK,QAAA,EAAC;cAAK;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpC1C,OAAA;gBAAQ8C,KAAK,EAAC,UAAU;gBAAAK,QAAA,EAAC;cAAS;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC3C1C,OAAA;gBAAQ8C,KAAK,EAAC,WAAW;gBAAAK,QAAA,EAAC;cAAU;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC7C1C,OAAA;gBAAQ8C,KAAK,EAAC,WAAW;gBAAAK,QAAA,EAAC;cAAU;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC7C1C,OAAA;gBAAQ8C,KAAK,EAAC,UAAU;gBAAAK,QAAA,EAAC;cAAS;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACN1C,OAAA;YAAAmD,QAAA,gBACInD,OAAA;cAAOsC,SAAS,EAAC,gDAAgD;cAAAa,QAAA,EAAC;YAAK;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/E1C,OAAA;cACI8C,KAAK,EAAEzC,aAAc;cACrBoF,QAAQ,EAAGC,CAAC,IAAK;gBACbpF,gBAAgB,CAACoF,CAAC,CAACC,MAAM,CAAC7C,KAAK,CAAC;gBAChC;gBACA,IAAI4C,CAAC,CAACC,MAAM,CAAC7C,KAAK,KAAK,KAAK,EAAE;kBAC1BuC,UAAU,CAAC,MAAMjF,aAAa,CAAC,UAAU,CAAC,EAAE,GAAG,CAAC;gBACpD;cACJ,CAAE;cACFkC,SAAS,EAAC,0IAA0I;cAAAa,QAAA,gBAEpJnD,OAAA;gBAAQ8C,KAAK,EAAC,KAAK;gBAAAK,QAAA,EAAC;cAAW;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC1C,OAAA;gBAAQ8C,KAAK,EAAC,SAAS;gBAAAK,QAAA,EAAC;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC1C,OAAA;gBAAQ8C,KAAK,EAAC,SAAS;gBAAAK,QAAA,EAAC;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC1C,OAAA;gBAAQ8C,KAAK,EAAC,SAAS;gBAAAK,QAAA,EAAC;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACN1C,OAAA;YAAAmD,QAAA,gBACInD,OAAA;cAAOsC,SAAS,EAAC,gDAAgD;cAAAa,QAAA,EAAC;YAAM;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChF1C,OAAA;cACI8C,KAAK,EAAEvC,cAAe;cACtBkF,QAAQ,EAAGC,CAAC,IAAK;gBACblF,iBAAiB,CAACkF,CAAC,CAACC,MAAM,CAAC7C,KAAK,CAAC;gBACjC;gBACA,IAAI4C,CAAC,CAACC,MAAM,CAAC7C,KAAK,KAAK,KAAK,EAAE;kBAC1BuC,UAAU,CAAC,MAAMjF,aAAa,CAAC,UAAU,CAAC,EAAE,GAAG,CAAC;gBACpD;cACJ,CAAE;cACFkC,SAAS,EAAC,0IAA0I;cAAAa,QAAA,gBAEpJnD,OAAA;gBAAQ8C,KAAK,EAAC,KAAK;gBAAAK,QAAA,EAAC;cAAW;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC1C,OAAA;gBAAQ8C,KAAK,EAAC,aAAa;gBAAAK,QAAA,EAAC;cAAW;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChD1C,OAAA;gBAAQ8C,KAAK,EAAC,SAAS;gBAAAK,QAAA,EAAC;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC1C,OAAA;gBAAQ8C,KAAK,EAAC,UAAU;gBAAAK,QAAA,EAAC;cAAQ;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACN1C,OAAA;YAAAmD,QAAA,gBACInD,OAAA;cAAOsC,SAAS,EAAC,gDAAgD;cAAAa,QAAA,EAAC;YAAO;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjF1C,OAAA;cACI8C,KAAK,EAAErC,eAAgB;cACvBgF,QAAQ,EAAGC,CAAC,IAAK;gBACbhF,kBAAkB,CAACgF,CAAC,CAACC,MAAM,CAAC7C,KAAK,CAAC;gBAClC;gBACA,IAAI4C,CAAC,CAACC,MAAM,CAAC7C,KAAK,KAAK,KAAK,EAAE;kBAC1BuC,UAAU,CAAC,MAAMjF,aAAa,CAAC,UAAU,CAAC,EAAE,GAAG,CAAC;gBACpD;cACJ,CAAE;cACFkC,SAAS,EAAC,0IAA0I;cAAAa,QAAA,gBAEpJnD,OAAA;gBAAQ8C,KAAK,EAAC,KAAK;gBAAAK,QAAA,EAAC;cAAY;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACzC1C,OAAA;gBAAQ8C,KAAK,EAAC,aAAa;gBAAAK,QAAA,EAAC;cAAW;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChD1C,OAAA;gBAAQ8C,KAAK,EAAC,SAAS;gBAAAK,QAAA,EAAC;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC1C,OAAA;gBAAQ8C,KAAK,EAAC,WAAW;gBAAAK,QAAA,EAAC;cAAS;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5C1C,OAAA;gBAAQ8C,KAAK,EAAC,kBAAkB;gBAAAK,QAAA,EAAC;cAAgB;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1D1C,OAAA;gBAAQ8C,KAAK,EAAC,SAAS;gBAAAK,QAAA,EAAC;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC1C,OAAA;gBAAQ8C,KAAK,EAAC,SAAS;gBAAAK,QAAA,EAAC;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC1C,OAAA;gBAAQ8C,KAAK,EAAC,YAAY;gBAAAK,QAAA,EAAC;cAAU;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9C1C,OAAA;gBAAQ8C,KAAK,EAAC,WAAW;gBAAAK,QAAA,EAAC;cAAS;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5C1C,OAAA;gBAAQ8C,KAAK,EAAC,kBAAkB;gBAAAK,QAAA,EAAC;cAAgB;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGN1C,OAAA;QAAKsC,SAAS,EAAC,uCAAuC;QAAAa,QAAA,gBAClDnD,OAAA,CAAC4C,QAAQ;UACLC,KAAK,EAAC,oBAAoB;UAC1BC,KAAK,EAAC,KAAK;UACXC,QAAQ,EAAC,oBAAoB;UAC7BC,IAAI,EAAExD,UAAW;UACjB0D,KAAK,EAAE;QAAK;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACF1C,OAAA,CAAC4C,QAAQ;UACLC,KAAK,EAAC,eAAe;UACrBC,KAAK,EAAC,KAAK;UACXC,QAAQ,EAAC,mBAAmB;UAC5BC,IAAI,EAAE9D;QAAS;UAAAqD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACF1C,OAAA,CAAC4C,QAAQ;UACLC,KAAK,EAAC,cAAc;UACpBC,KAAK,EAAC,KAAK;UACXC,QAAQ,EAAC,oBAAoB;UAC7BC,IAAI,EAAEtD;QAAY;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGN1C,OAAA;QAAKsC,SAAS,EAAC,uCAAuC;QAAAa,QAAA,gBAElDnD,OAAA;UAAKsC,SAAS,EAAC,yBAAyB;UAAAa,QAAA,GACnChD,UAAU,KAAK,SAAS,iBACrBH,OAAA;YAAKsC,SAAS,EAAC,2DAA2D;YAAAa,QAAA,gBACtEnD,OAAA;cAAIsC,SAAS,EAAC,sCAAsC;cAAAa,QAAA,EAAC;YAA0B;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpF1C,OAAA;cAAKsC,SAAS,EAAC,uCAAuC;cAAAa,QAAA,gBAClDnD,OAAA;gBAAKsC,SAAS,EAAC,wFAAwF;gBAAAa,QAAA,gBACnGnD,OAAA;kBAAKsC,SAAS,EAAC,wDAAwD;kBAAAa,QAAA,EAAC;gBAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjF1C,OAAA;kBAAKsC,SAAS,EAAC,6BAA6B;kBAAAa,QAAA,EAAC;gBAAkB;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC,eACN1C,OAAA;gBAAKsC,SAAS,EAAC,WAAW;gBAAAa,QAAA,gBACtBnD,OAAA;kBAAKsC,SAAS,EAAC,gGAAgG;kBAAAa,QAAA,gBAC3GnD,OAAA;oBAAKsC,SAAS,EAAC,yBAAyB;oBAAAa,QAAA,gBACpCnD,OAAA,CAACN,WAAW;sBAAC4C,SAAS,EAAC;oBAAwB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAClD1C,OAAA;sBAAAmD,QAAA,gBACInD,OAAA;wBAAKsC,SAAS,EAAC,8BAA8B;wBAAAa,QAAA,EAAC;sBAAO;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC3D1C,OAAA;wBAAKsC,SAAS,EAAC,wBAAwB;wBAAAa,QAAA,EAAC;sBAAa;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACN1C,OAAA;oBAAKsC,SAAS,EAAC,wCAAwC;oBAAAa,QAAA,EAAC;kBAAG;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC,eACN1C,OAAA;kBAAKsC,SAAS,EAAC,4FAA4F;kBAAAa,QAAA,gBACvGnD,OAAA;oBAAKsC,SAAS,EAAC,yBAAyB;oBAAAa,QAAA,gBACpCnD,OAAA,CAACL,OAAO;sBAAC2C,SAAS,EAAC;oBAAsB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC5C1C,OAAA;sBAAAmD,QAAA,gBACInD,OAAA;wBAAKsC,SAAS,EAAC,4BAA4B;wBAAAa,QAAA,EAAC;sBAAM;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACxD1C,OAAA;wBAAKsC,SAAS,EAAC,sBAAsB;wBAAAa,QAAA,EAAC;sBAAW;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACN1C,OAAA;oBAAKsC,SAAS,EAAC,sCAAsC;oBAAAa,QAAA,EAAC;kBAAE;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACR,EAEAvC,UAAU,KAAK,UAAU,iBACtBH,OAAA;YAAKsC,SAAS,EAAC,2DAA2D;YAAAa,QAAA,gBACtEnD,OAAA;cAAIsC,SAAS,EAAC,sCAAsC;cAAAa,QAAA,EAAC;YAAuB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjF1C,OAAA;cAAKsC,SAAS,EAAC,WAAW;cAAAa,QAAA,EACrBnC,cAAc,CAACK,QAAQ,CAACkE,GAAG,CAAC,CAACzD,OAAO,EAAE8D,KAAK,kBACxC5F,OAAA,CAACoD,WAAW;gBAER7B,UAAU,EAAEO,OAAO,CAACP,UAAW;gBAC/B8B,KAAK,EAAEvB,OAAO,CAACR,IAAK;gBACpBF,KAAK,EAAEU,OAAO,CAACV,KAAM;gBACrBF,OAAO,EAAEY,OAAO,CAACZ;cAAQ,GAJpB0E,KAAK;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKb,CACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACR,EAEAvC,UAAU,KAAK,SAAS,iBACrBH,OAAA;YAAKsC,SAAS,EAAC,2DAA2D;YAAAa,QAAA,gBACtEnD,OAAA;cAAIsC,SAAS,EAAC,sCAAsC;cAAAa,QAAA,EAAC;YAAqB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/E1C,OAAA;cAAKsC,SAAS,EAAC,uCAAuC;cAAAa,QAAA,EACjDnC,cAAc,CAACQ,OAAO,CAAC+D,GAAG,CAAC,CAACpD,KAAK,EAAEyD,KAAK,kBACrC5F,OAAA;gBAAiBsC,SAAS,EAAC,8FAA8F;gBAAAa,QAAA,gBACrHnD,OAAA;kBAAKsC,SAAS,EAAC,wCAAwC;kBAAAa,QAAA,gBACnDnD,OAAA;oBAAIsC,SAAS,EAAC,6BAA6B;oBAAAa,QAAA,EAAEhB,KAAK,CAACb;kBAAI;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC7D1C,OAAA,CAACb,KAAK;oBAACmD,SAAS,EAAC;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC,eACN1C,OAAA;kBAAKsC,SAAS,EAAC,WAAW;kBAAAa,QAAA,gBACtBnD,OAAA;oBAAKsC,SAAS,EAAC,kCAAkC;oBAAAa,QAAA,GAAEhB,KAAK,CAACT,aAAa,EAAC,GAAC;kBAAA;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC9E1C,OAAA;oBAAKsC,SAAS,EAAC,uBAAuB;oBAAAa,QAAA,GAAEhB,KAAK,CAACV,QAAQ,EAAC,WAAS;kBAAA;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACtE1C,OAAA;oBAAKsC,SAAS,EAAC,qCAAqC;oBAAAa,QAAA,eAChDnD,OAAA;sBACIsC,SAAS,EAAC,0DAA0D;sBACpEgB,KAAK,EAAE;wBAAEC,KAAK,EAAG,GAAEpB,KAAK,CAACT,aAAc;sBAAG;oBAAE;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1C;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA,GAdAkD,KAAK;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAeV,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACR,EAEAvC,UAAU,KAAK,SAAS,iBACrBH,OAAA;YAAKsC,SAAS,EAAC,2DAA2D;YAAAa,QAAA,gBACtEnD,OAAA;cAAIsC,SAAS,EAAC,sCAAsC;cAAAa,QAAA,EAAC;YAAsB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChF1C,OAAA;cAAKsC,SAAS,EAAC,WAAW;cAAAa,QAAA,EACrBnC,cAAc,CAACW,OAAO,CAAC4D,GAAG,CAAC,CAACrD,MAAM,EAAE0D,KAAK,kBACtC5F,OAAA;gBAAiBsC,SAAS,EAAC,+HAA+H;gBAAAa,QAAA,gBACtJnD,OAAA;kBAAKsC,SAAS,EAAC,yBAAyB;kBAAAa,QAAA,gBACpCnD,OAAA;oBAAKsC,SAAS,EAAC,4BAA4B;oBAAAa,QAAA,eACvCnD,OAAA,CAACX,aAAa;sBAACiD,SAAS,EAAC;oBAAoB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CAAC,eACN1C,OAAA;oBAAAmD,QAAA,gBACInD,OAAA;sBAAIsC,SAAS,EAAC,6BAA6B;sBAAAa,QAAA,EAAEjB,MAAM,CAACZ;oBAAI;sBAAAiB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC9D1C,OAAA;sBAAGsC,SAAS,EAAC,uBAAuB;sBAAAa,QAAA,GAAEjB,MAAM,CAACT,QAAQ,EAAC,oBAAkB;oBAAA;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACN1C,OAAA;kBAAAmD,QAAA,eACInD,OAAA;oBAAKsC,SAAS,EAAG,mDAAkDK,kBAAkB,CAACT,MAAM,CAACR,aAAa,CAAE,EAAE;oBAAAyB,QAAA,GACzGjB,MAAM,CAACR,aAAa,EAAC,GAC1B;kBAAA;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA,GAdAkD,KAAK;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAeV,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACR,EAEAvC,UAAU,KAAK,UAAU,iBACtBH,OAAA;YAAKsC,SAAS,EAAC,WAAW;YAAAa,QAAA,GAErB,CAAC5C,cAAc,KAAK,KAAK,IAAIF,aAAa,KAAK,KAAK,IAAII,eAAe,KAAK,KAAK,KAAK,CAAC,MAAM;cAC1F,MAAM0D,YAAY,GAAGX,yBAAyB,CAAC,CAAC;cAChD,MAAMqC,YAAY,GAAG1B,YAAY,CAACV,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACtB,IAAI,KAAK,OAAO,CAAC;cAC3E,MAAMoC,YAAY,GAAGqB,YAAY,CAACpC,MAAM,CAACC,MAAM,IAAIA,MAAM,CAAC3B,MAAM,KAAK,SAAS,CAAC,CAACsC,MAAM;cACtF,MAAMI,WAAW,GAAGoB,YAAY,CAACpC,MAAM,CAACC,MAAM,IAAIA,MAAM,CAAC3B,MAAM,KAAK,QAAQ,CAAC,CAACsC,MAAM;cACpF,MAAMyB,YAAY,GAAGD,YAAY,CAACxB,MAAM;cACxC,MAAM0B,oBAAoB,GAAGD,YAAY,GAAG,CAAC,GAAGE,IAAI,CAACC,KAAK,CAAEzB,YAAY,GAAGsB,YAAY,GAAI,GAAG,CAAC,GAAG,CAAC;cAEnG,oBACI9F,OAAA;gBAAKsC,SAAS,EAAC,kFAAkF;gBAACgB,KAAK,EAAE;kBAAC4C,eAAe,EAAE,SAAS;kBAAEC,WAAW,EAAE;gBAAS,CAAE;gBAAAhD,QAAA,gBAC1JnD,OAAA;kBAAKsC,SAAS,EAAC,wCAAwC;kBAAAa,QAAA,gBACnDnD,OAAA;oBAAAmD,QAAA,gBACInD,OAAA;sBAAIsC,SAAS,EAAC,yBAAyB;sBAACgB,KAAK,EAAE;wBAAC8C,KAAK,EAAE;sBAAS,CAAE;sBAAAjD,QAAA,EAAC;oBAA2B;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnG1C,OAAA;sBAAKsC,SAAS,EAAC,8BAA8B;sBAAAa,QAAA,GACxC5C,cAAc,KAAK,KAAK,iBACrBP,OAAA;wBAAMsC,SAAS,EAAC,+BAA+B;wBAACgB,KAAK,EAAE;0BAAC4C,eAAe,EAAE,SAAS;0BAAEE,KAAK,EAAE,SAAS;0BAAED,WAAW,EAAE;wBAAS,CAAE;wBAAAhD,QAAA,GAAC,UACnH,EAAC5C,cAAc;sBAAA;wBAAAgC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrB,CACT,EACArC,aAAa,KAAK,KAAK,iBACpBL,OAAA;wBAAMsC,SAAS,EAAC,+BAA+B;wBAACgB,KAAK,EAAE;0BAAC4C,eAAe,EAAE,SAAS;0BAAEE,KAAK,EAAE,SAAS;0BAAED,WAAW,EAAE;wBAAS,CAAE;wBAAAhD,QAAA,GAAC,SACpH,EAAC9C,aAAa;sBAAA;wBAAAkC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CACT,EACAjC,eAAe,KAAK,KAAK,iBACtBT,OAAA;wBAAMsC,SAAS,EAAC,+BAA+B;wBAACgB,KAAK,EAAE;0BAAC4C,eAAe,EAAE,SAAS;0BAAEE,KAAK,EAAE,SAAS;0BAAED,WAAW,EAAE;wBAAS,CAAE;wBAAAhD,QAAA,GAAC,WAClH,EAAC1C,eAAe;sBAAA;wBAAA8B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvB,CACT;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACN1C,OAAA;oBAAKsC,SAAS,EAAC,iBAAiB;oBAAAa,QAAA,eAC5BnD,OAAA;sBAAKsC,SAAS,EAAC,kEAAkE;sBAACgB,KAAK,EAAE;wBAAC4C,eAAe,EAAE;sBAAS,CAAE;sBAAA/C,QAAA,eAClHnD,OAAA,CAACR,UAAU;wBAAC8C,SAAS,EAAC,SAAS;wBAACgB,KAAK,EAAE;0BAAC8C,KAAK,EAAE;wBAAS;sBAAE;wBAAA7D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5D;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eAEN1C,OAAA;kBAAKsC,SAAS,EAAC,uCAAuC;kBAAAa,QAAA,gBAClDnD,OAAA;oBAAKsC,SAAS,EAAC,2IAA2I;oBAACgB,KAAK,EAAE;sBAAC6C,WAAW,EAAE;oBAAS,CAAE;oBAAAhD,QAAA,gBACvLnD,OAAA;sBAAKsC,SAAS,EAAC,4DAA4D;sBAACgB,KAAK,EAAE;wBAAC4C,eAAe,EAAE;sBAAS;oBAAE;sBAAA3D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACvH1C,OAAA;sBAAKsC,SAAS,EAAC,UAAU;sBAAAa,QAAA,gBACrBnD,OAAA;wBAAKsC,SAAS,EAAC,wCAAwC;wBAAAa,QAAA,gBACnDnD,OAAA;0BAAKsC,SAAS,EAAC,gBAAgB;0BAACgB,KAAK,EAAE;4BAAC4C,eAAe,EAAE;0BAAS,CAAE;0BAAA/C,QAAA,eAChEnD,OAAA,CAACR,UAAU;4BAAC8C,SAAS,EAAC,SAAS;4BAACgB,KAAK,EAAE;8BAAC8C,KAAK,EAAE;4BAAS;0BAAE;4BAAA7D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5D,CAAC,eACN1C,OAAA;0BAAKsC,SAAS,EAAG,kCACbyD,oBAAoB,IAAI,EAAE,GAAG,6BAA6B,GAC1DA,oBAAoB,IAAI,EAAE,GAAG,YAAY,GACzC,yBACH,EAAE;0BAACzC,KAAK,EAAEyC,oBAAoB,IAAI,EAAE,IAAIA,oBAAoB,GAAG,EAAE,GAAG;4BAACG,eAAe,EAAE,SAAS;4BAAEE,KAAK,EAAE;0BAAS,CAAC,GAAG,CAAC,CAAE;0BAAAjD,QAAA,EACpH4C,oBAAoB,IAAI,EAAE,GAAG,WAAW,GAAGA,oBAAoB,IAAI,EAAE,GAAG,MAAM,GAAG;wBAAmB;0BAAAxD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpG,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACN1C,OAAA;wBAAKsC,SAAS,EAAC,yBAAyB;wBAACgB,KAAK,EAAE;0BAAC8C,KAAK,EAAE;wBAAS,CAAE;wBAAAjD,QAAA,GAAE4C,oBAAoB,EAAC,GAAC;sBAAA;wBAAAxD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACjG1C,OAAA;wBAAKsC,SAAS,EAAC,qBAAqB;wBAACgB,KAAK,EAAE;0BAAC8C,KAAK,EAAE;wBAAS,CAAE;wBAAAjD,QAAA,EAAC;sBAAe;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eAEN1C,OAAA;oBAAKsC,SAAS,EAAC,2IAA2I;oBAACgB,KAAK,EAAE;sBAAC6C,WAAW,EAAE;oBAAS,CAAE;oBAAAhD,QAAA,gBACvLnD,OAAA;sBAAKsC,SAAS,EAAC,4DAA4D;sBAACgB,KAAK,EAAE;wBAAC4C,eAAe,EAAE;sBAAS;oBAAE;sBAAA3D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACvH1C,OAAA;sBAAKsC,SAAS,EAAC,UAAU;sBAAAa,QAAA,gBACrBnD,OAAA;wBAAKsC,SAAS,EAAC,wCAAwC;wBAAAa,QAAA,gBACnDnD,OAAA;0BAAKsC,SAAS,EAAC,gBAAgB;0BAACgB,KAAK,EAAE;4BAAC4C,eAAe,EAAE;0BAAS,CAAE;0BAAA/C,QAAA,eAChEnD,OAAA,CAACN,WAAW;4BAAC4C,SAAS,EAAC,SAAS;4BAACgB,KAAK,EAAE;8BAAC8C,KAAK,EAAE;4BAAS;0BAAE;4BAAA7D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC7D,CAAC,eACN1C,OAAA;0BAAKsC,SAAS,EAAC,gCAAgC;0BAACgB,KAAK,EAAE;4BAAC4C,eAAe,EAAE,SAAS;4BAAEE,KAAK,EAAE;0BAAS,CAAE;0BAAAjD,QAAA,GACjG2C,YAAY,GAAG,CAAC,GAAGE,IAAI,CAACC,KAAK,CAAEzB,YAAY,GAAGsB,YAAY,GAAI,GAAG,CAAC,GAAG,CAAC,EAAC,GAC5E;wBAAA;0BAAAvD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACN1C,OAAA;wBAAKsC,SAAS,EAAC,yBAAyB;wBAACgB,KAAK,EAAE;0BAAC8C,KAAK,EAAE;wBAAS,CAAE;wBAAAjD,QAAA,EAAEqB;sBAAY;wBAAAjC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACxF1C,OAAA;wBAAKsC,SAAS,EAAC,qBAAqB;wBAACgB,KAAK,EAAE;0BAAC8C,KAAK,EAAE;wBAAS,CAAE;wBAAAjD,QAAA,EAAC;sBAAY;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eAEN1C,OAAA;oBAAKsC,SAAS,EAAC,2IAA2I;oBAACgB,KAAK,EAAE;sBAAC6C,WAAW,EAAE;oBAAS,CAAE;oBAAAhD,QAAA,gBACvLnD,OAAA;sBAAKsC,SAAS,EAAC,4DAA4D;sBAACgB,KAAK,EAAE;wBAAC4C,eAAe,EAAE;sBAAS;oBAAE;sBAAA3D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACvH1C,OAAA;sBAAKsC,SAAS,EAAC,UAAU;sBAAAa,QAAA,gBACrBnD,OAAA;wBAAKsC,SAAS,EAAC,wCAAwC;wBAAAa,QAAA,gBACnDnD,OAAA;0BAAKsC,SAAS,EAAC,gBAAgB;0BAACgB,KAAK,EAAE;4BAAC4C,eAAe,EAAE;0BAAS,CAAE;0BAAA/C,QAAA,eAChEnD,OAAA,CAACL,OAAO;4BAAC2C,SAAS,EAAC,SAAS;4BAACgB,KAAK,EAAE;8BAAC8C,KAAK,EAAE;4BAAS;0BAAE;4BAAA7D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzD,CAAC,eACN1C,OAAA;0BAAKsC,SAAS,EAAC,gCAAgC;0BAACgB,KAAK,EAAE;4BAAC4C,eAAe,EAAE,SAAS;4BAAEE,KAAK,EAAE;0BAAS,CAAE;0BAAAjD,QAAA,GACjG2C,YAAY,GAAG,CAAC,GAAGE,IAAI,CAACC,KAAK,CAAExB,WAAW,GAAGqB,YAAY,GAAI,GAAG,CAAC,GAAG,CAAC,EAAC,GAC3E;wBAAA;0BAAAvD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACN1C,OAAA;wBAAKsC,SAAS,EAAC,yBAAyB;wBAACgB,KAAK,EAAE;0BAAC8C,KAAK,EAAE;wBAAS,CAAE;wBAAAjD,QAAA,EAAEsB;sBAAW;wBAAAlC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACvF1C,OAAA;wBAAKsC,SAAS,EAAC,qBAAqB;wBAACgB,KAAK,EAAE;0BAAC8C,KAAK,EAAE;wBAAS,CAAE;wBAAAjD,QAAA,EAAC;sBAAW;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eAEN1C,OAAA;oBAAKsC,SAAS,EAAC,2IAA2I;oBAACgB,KAAK,EAAE;sBAAC6C,WAAW,EAAE;oBAAS,CAAE;oBAAAhD,QAAA,gBACvLnD,OAAA;sBAAKsC,SAAS,EAAC,4DAA4D;sBAACgB,KAAK,EAAE;wBAAC4C,eAAe,EAAE;sBAAS;oBAAE;sBAAA3D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACvH1C,OAAA;sBAAKsC,SAAS,EAAC,UAAU;sBAAAa,QAAA,gBACrBnD,OAAA;wBAAKsC,SAAS,EAAC,wCAAwC;wBAAAa,QAAA,gBACnDnD,OAAA;0BAAKsC,SAAS,EAAC,gBAAgB;0BAACgB,KAAK,EAAE;4BAAC4C,eAAe,EAAE;0BAAS,CAAE;0BAAA/C,QAAA,eAChEnD,OAAA,CAACd,QAAQ;4BAACoD,SAAS,EAAC,SAAS;4BAACgB,KAAK,EAAE;8BAAC8C,KAAK,EAAE;4BAAS;0BAAE;4BAAA7D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1D,CAAC,eACN1C,OAAA;0BAAKsC,SAAS,EAAC,gCAAgC;0BAACgB,KAAK,EAAE;4BAAC4C,eAAe,EAAE,SAAS;4BAAEE,KAAK,EAAE;0BAAS,CAAE;0BAAAjD,QAAA,EAAC;wBAEvG;0BAAAZ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACN1C,OAAA;wBAAKsC,SAAS,EAAC,yBAAyB;wBAACgB,KAAK,EAAE;0BAAC8C,KAAK,EAAE;wBAAS,CAAE;wBAAAjD,QAAA,EAAE2C;sBAAY;wBAAAvD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACxF1C,OAAA;wBAAKsC,SAAS,EAAC,qBAAqB;wBAACgB,KAAK,EAAE;0BAAC8C,KAAK,EAAE;wBAAS,CAAE;wBAAAjD,QAAA,EAAC;sBAAa;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAEd,CAAC,EAAE,CAAC,eAEJ1C,OAAA;cAAKsC,SAAS,EAAC,mIAAmI;cAAAa,QAAA,gBAE9InD,OAAA;gBAAKsC,SAAS,EAAC,mEAAmE;gBAAAa,QAAA,gBAC9EnD,OAAA;kBAAKsC,SAAS,EAAC,yBAAyB;kBAAAa,QAAA,gBACpCnD,OAAA;oBAAKsC,SAAS,EAAC,yEAAyE;oBAAAa,QAAA,eACpFnD,OAAA,CAACd,QAAQ;sBAACoD,SAAS,EAAC;oBAAoB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC,eACN1C,OAAA;oBAAAmD,QAAA,gBACInD,OAAA;sBAAIsC,SAAS,EAAC,kCAAkC;sBAAAa,QAAA,EAAC;oBAAoB;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC1E1C,OAAA;sBAAGsC,SAAS,EAAC,4BAA4B;sBAAAa,QAAA,EACnC5C,cAAc,KAAK,KAAK,IAAIF,aAAa,KAAK,KAAK,IAAII,eAAe,KAAK,KAAK,GAC5E,gCAAgC,GAChC;oBAA0B;sBAAA8B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eAGN1C,OAAA;kBAAKsC,SAAS,EAAC,sCAAsC;kBAAAa,QAAA,gBACjDnD,OAAA;oBAAKsC,SAAS,EAAC,aAAa;oBAAAa,QAAA,gBACxBnD,OAAA;sBAAKsC,SAAS,EAAC,iCAAiC;sBAAAa,QAAA,EAAE,IAAIpC,IAAI,CAAC,CAAC,CAACsF,kBAAkB,CAAC,OAAO,EAAE;wBAAEC,KAAK,EAAE;sBAAQ,CAAC;oBAAC;sBAAA/D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACnH1C,OAAA;sBAAKsC,SAAS,EAAC,uBAAuB;sBAAAa,QAAA,EAAC;oBAAa;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC,eACN1C,OAAA;oBAAKsC,SAAS,EAAC;kBAAsB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5C1C,OAAA;oBAAKsC,SAAS,EAAC,aAAa;oBAAAa,QAAA,gBACxBnD,OAAA;sBAAKsC,SAAS,EAAC,iCAAiC;sBAAAa,QAAA,EAAEtC,YAAY,CAAC0F,OAAO,CAAC;oBAAC;sBAAAhE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC/E1C,OAAA;sBAAKsC,SAAS,EAAC,uBAAuB;sBAAAa,QAAA,EAAC;oBAAQ;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAGN1C,OAAA;gBAAKsC,SAAS,EAAC,qGAAqG;gBAAAa,QAAA,gBAChHnD,OAAA;kBAAKsC,SAAS,EAAC,wCAAwC;kBAAAa,QAAA,gBACnDnD,OAAA;oBAAIsC,SAAS,EAAC,0DAA0D;oBAAAa,QAAA,gBACpEnD,OAAA,CAACH,IAAI;sBAACyC,SAAS,EAAC;oBAAuB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,mBAE9C;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL1C,OAAA;oBAAKsC,SAAS,EAAC,+DAA+D;oBAAAa,QAAA,EAAC;kBAE/E;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACN1C,OAAA;kBAAKsC,SAAS,EAAC,uCAAuC;kBAAAa,QAAA,gBAClDnD,OAAA;oBAAKsC,SAAS,EAAC,qHAAqH;oBAAAa,QAAA,gBAChInD,OAAA;sBAAKsC,SAAS,EAAC;oBAAsG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC5H1C,OAAA;sBAAAmD,QAAA,gBACInD,OAAA;wBAAMsC,SAAS,EAAC,8BAA8B;wBAAAa,QAAA,EAAC;sBAAO;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC7D1C,OAAA;wBAAKsC,SAAS,EAAC,wBAAwB;wBAAAa,QAAA,EAAC;sBAAc;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACN1C,OAAA;oBAAKsC,SAAS,EAAC,mHAAmH;oBAAAa,QAAA,gBAC9HnD,OAAA;sBAAKsC,SAAS,EAAC;oBAAgG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACtH1C,OAAA;sBAAAmD,QAAA,gBACInD,OAAA;wBAAMsC,SAAS,EAAC,4BAA4B;wBAAAa,QAAA,EAAC;sBAAM;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC1D1C,OAAA;wBAAKsC,SAAS,EAAC,sBAAsB;wBAAAa,QAAA,EAAC;sBAAY;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACN1C,OAAA;oBAAKsC,SAAS,EAAC,oHAAoH;oBAAAa,QAAA,gBAC/HnD,OAAA;sBAAKsC,SAAS,EAAC;oBAAmG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACzH1C,OAAA;sBAAAmD,QAAA,gBACInD,OAAA;wBAAMsC,SAAS,EAAC,6BAA6B;wBAAAa,QAAA,EAAC;sBAAO;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC5D1C,OAAA;wBAAKsC,SAAS,EAAC,uBAAuB;wBAAAa,QAAA,EAAC;sBAAU;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACN1C,OAAA;oBAAKsC,SAAS,EAAC,sHAAsH;oBAAAa,QAAA,gBACjInD,OAAA;sBAAKsC,SAAS,EAAC;oBAAyG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC/H1C,OAAA;sBAAAmD,QAAA,gBACInD,OAAA;wBAAMsC,SAAS,EAAC,+BAA+B;wBAAAa,QAAA,EAAC;sBAAK;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC5D1C,OAAA;wBAAKsC,SAAS,EAAC,yBAAyB;wBAAAa,QAAA,EAAC;sBAAkB;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAGN1C,OAAA;gBAAKsC,SAAS,EAAC,6BAA6B;gBAAAa,QAAA,gBAExCnD,OAAA;kBAAKsC,SAAS,EAAC;gBAA6G;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnI1C,OAAA;kBAAKsC,SAAS,EAAC;gBAA+G;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrI1C,OAAA;kBAAKsC,SAAS,EAAC;gBAAmK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAGzL1C,OAAA;kBAAKsC,SAAS,EAAC,qGAAqG;kBAAAa,QAAA,gBAEhHnD,OAAA;oBAAKsC,SAAS,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAGvC1C,OAAA;oBAAKsC,SAAS,EAAC,KAAK;oBAAAa,QAAA,eAChBnD,OAAA,CAACF,YAAY;sBACTgD,KAAK,EAAEjC,YAAa;sBACpB4E,QAAQ,EAAET,gBAAiB;sBAC3BwB,WAAW,EAAE9B,cAAe;sBAC5B+B,aAAa,EAAE1B,gBAAiB;sBAChCzC,SAAS,EAAC;oBAA4B;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eAGN1C,OAAA;oBAAKsC,SAAS,EAAC;kBAAsE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3F,CAAC,eAGN1C,OAAA;kBAAKsC,SAAS,EAAC,gEAAgE;kBAAAa,QAAA,gBAC3EnD,OAAA;oBAAQsC,SAAS,EAAC,kLAAkL;oBAAAa,QAAA,eAChMnD,OAAA,CAACd,QAAQ;sBAACoD,SAAS,EAAC;oBAA+D;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClF,CAAC,eACT1C,OAAA;oBAAQsC,SAAS,EAAC,oLAAoL;oBAAAa,QAAA,eAClMnD,OAAA,CAACR,UAAU;sBAAC8C,SAAS,EAAC;oBAA+D;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpF,CAAC,eACT1C,OAAA;oBAAQsC,SAAS,EAAC,iLAAiL;oBAAAa,QAAA,eAC/LnD,OAAA,CAACT,QAAQ;sBAAC+C,SAAS,EAAC;oBAA+D;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eAGN1C,OAAA;kBAAKsC,SAAS,EAAC,4CAA4C;kBAAAa,QAAA,gBACvDnD,OAAA;oBAAKsC,SAAS,EAAC,kLAAkL;oBAAAa,QAAA,gBAC7LnD,OAAA;sBAAKsC,SAAS,EAAC;oBAA+D;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACrF1C,OAAA;sBAAKsC,SAAS,EAAC,yBAAyB;sBAAAa,QAAA,gBACpCnD,OAAA;wBAAKsC,SAAS,EAAC;sBAAiD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACvE1C,OAAA;wBAAMsC,SAAS,EAAC,4BAA4B;wBAAAa,QAAA,EAAC;sBAAY;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/D,CAAC,eACN1C,OAAA;sBAAKsC,SAAS,EAAC,6BAA6B;sBAAAa,QAAA,EAAC;oBAAkB;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE,CAAC,eAEN1C,OAAA;oBAAKsC,SAAS,EAAC,2KAA2K;oBAAAa,QAAA,gBACtLnD,OAAA;sBAAKsC,SAAS,EAAC;oBAA6D;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACnF1C,OAAA;sBAAKsC,SAAS,EAAC,yBAAyB;sBAAAa,QAAA,gBACpCnD,OAAA;wBAAKsC,SAAS,EAAC;sBAA+C;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACrE1C,OAAA;wBAAMsC,SAAS,EAAC,0BAA0B;wBAAAa,QAAA,EAAC;sBAAW;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5D,CAAC,eACN1C,OAAA;sBAAKsC,SAAS,EAAC,2BAA2B;sBAAAa,QAAA,EAAC;oBAAkB;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CAAC,eAEN1C,OAAA;oBAAKsC,SAAS,EAAC,8KAA8K;oBAAAa,QAAA,gBACzLnD,OAAA;sBAAKsC,SAAS,EAAC;oBAA8D;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACpF1C,OAAA;sBAAKsC,SAAS,EAAC,yBAAyB;sBAAAa,QAAA,gBACpCnD,OAAA;wBAAKsC,SAAS,EAAC;sBAAgD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACtE1C,OAAA;wBAAMsC,SAAS,EAAC,2BAA2B;wBAAAa,QAAA,EAAC;sBAAQ;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1D,CAAC,eACN1C,OAAA;sBAAKsC,SAAS,EAAC,4BAA4B;sBAAAa,QAAA,EAAC;oBAAkB;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnE,CAAC,eAEN1C,OAAA;oBAAKsC,SAAS,EAAC,kLAAkL;oBAAAa,QAAA,gBAC7LnD,OAAA;sBAAKsC,SAAS,EAAC;oBAAgE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACtF1C,OAAA;sBAAKsC,SAAS,EAAC,yBAAyB;sBAAAa,QAAA,gBACpCnD,OAAA;wBAAKsC,SAAS,EAAC;sBAAkD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACxE1C,OAAA;wBAAMsC,SAAS,EAAC,6BAA6B;wBAAAa,QAAA,EAAC;sBAAU;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9D,CAAC,eACN1C,OAAA;sBAAKsC,SAAS,EAAC,8BAA8B;sBAAAa,QAAA,EAAC;oBAAkB;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eAGN1C,OAAA;kBAAKsC,SAAS,EAAC,kCAAkC;kBAAAa,QAAA,gBAC7CnD,OAAA;oBAAKsC,SAAS,EAAC,gCAAgC;oBAACoE,OAAO,EAAC,WAAW;oBAAAvD,QAAA,gBAC/DnD,OAAA;sBAAQ2G,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,IAAI;sBAACC,IAAI,EAAC,MAAM;sBAACC,MAAM,EAAC,SAAS;sBAACC,WAAW,EAAC;oBAAG;sBAAAzE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eAC7E1C,OAAA;sBACI2G,EAAE,EAAC,IAAI;sBACPC,EAAE,EAAC,IAAI;sBACPC,CAAC,EAAC,IAAI;sBACNC,IAAI,EAAC,MAAM;sBACXC,MAAM,EAAC,gBAAgB;sBACvBC,WAAW,EAAC,GAAG;sBACfC,eAAe,EAAC,QAAQ;sBACxBC,gBAAgB,EAAC,IAAI;sBACrB5E,SAAS,EAAC;oBAAe;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC,eACF1C,OAAA;sBAAAmD,QAAA,eACInD,OAAA;wBAAgBsF,EAAE,EAAC,UAAU;wBAAC6B,EAAE,EAAC,IAAI;wBAACC,EAAE,EAAC,IAAI;wBAACC,EAAE,EAAC,MAAM;wBAACC,EAAE,EAAC,MAAM;wBAAAnE,QAAA,gBAC7DnD,OAAA;0BAAMuH,MAAM,EAAC,IAAI;0BAACC,SAAS,EAAC;wBAAS;0BAAAjF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC,eACvC1C,OAAA;0BAAMuH,MAAM,EAAC,MAAM;0BAACC,SAAS,EAAC;wBAAS;0BAAAjF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7B;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACN1C,OAAA;oBAAKsC,SAAS,EAAC,mDAAmD;oBAAAa,QAAA,eAC9DnD,OAAA;sBAAMsC,SAAS,EAAC,iCAAiC;sBAAAa,QAAA,EAAC;oBAAG;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,EAGL7B,YAAY,IAAI,CAAC,MAAM;gBACpB,MAAM+D,UAAU,GAAGb,oBAAoB,CAAClD,YAAY,CAAC;gBACrD,MAAMgE,KAAK,GAAG,IAAI9D,IAAI,CAAC,CAAC;gBACxB,MAAM0G,OAAO,GAAG5G,YAAY,CAAC6G,YAAY,CAAC,CAAC,KAAK7C,KAAK,CAAC6C,YAAY,CAAC,CAAC;gBACpE,MAAM5C,QAAQ,GAAGjE,YAAY,GAAGgE,KAAK;gBAErC,oBACI7E,OAAA;kBAAKsC,SAAS,EAAC,gBAAgB;kBAAAa,QAAA,gBAE3BnD,OAAA;oBAAKsC,SAAS,EAAC,qFAAqF;oBAAAa,QAAA,eAChGnD,OAAA;sBAAKsC,SAAS,EAAC,mCAAmC;sBAAAa,QAAA,gBAC9CnD,OAAA;wBAAAmD,QAAA,gBACInD,OAAA;0BAAIsC,SAAS,EAAC,yBAAyB;0BAAAa,QAAA,EAClCtC,YAAY,CAACwF,kBAAkB,CAAC,OAAO,EAAE;4BACtCsB,OAAO,EAAE,MAAM;4BACfC,IAAI,EAAE,SAAS;4BACftB,KAAK,EAAE,MAAM;4BACbuB,GAAG,EAAE;0BACT,CAAC;wBAAC;0BAAAtF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACL1C,OAAA;0BAAKsC,SAAS,EAAC,yCAAyC;0BAAAa,QAAA,GACnDsE,OAAO,iBACJzH,OAAA;4BAAMsC,SAAS,EAAC,wDAAwD;4BAAAa,QAAA,EAAC;0BAEzE;4BAAAZ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CACT,EACAoC,QAAQ,iBACL9E,OAAA;4BAAMsC,SAAS,EAAC,wDAAwD;4BAAAa,QAAA,EAAC;0BAEzE;4BAAAZ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CACT,EACAkC,UAAU,iBACP5E,OAAA;4BAAMsC,SAAS,EAAG,8CACdsC,UAAU,CAAC7C,MAAM,KAAK,SAAS,GAAG,gCAAgC,GAClE6C,UAAU,CAAC7C,MAAM,KAAK,QAAQ,GAAG,4BAA4B,GAC7D6C,UAAU,CAAC7C,MAAM,KAAK,SAAS,GAAG,8BAA8B,GAChE,kCACH,EAAE;4BAAAoB,QAAA,EACEyB,UAAU,CAAC7C,MAAM,KAAK,SAAS,GAAG,aAAa,GAC/C6C,UAAU,CAAC7C,MAAM,KAAK,QAAQ,GAAG,YAAY,GAC7C6C,UAAU,CAAC7C,MAAM,KAAK,SAAS,GAAG,SAAS,GAC3C;0BAAkB;4BAAAQ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CACT;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACA,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACN1C,OAAA;wBAAKsC,SAAS,EAAC,YAAY;wBAAAa,QAAA,gBACvBnD,OAAA;0BAAKsC,SAAS,EAAC,oBAAoB;0BAAAa,QAAA,EAAEtC,YAAY,CAAC0F,OAAO,CAAC;wBAAC;0BAAAhE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAClE1C,OAAA;0BAAKsC,SAAS,EAAC,yBAAyB;0BAAAa,QAAA,EACnCtC,YAAY,CAACwF,kBAAkB,CAAC,OAAO,EAAE;4BAAEC,KAAK,EAAE;0BAAQ,CAAC;wBAAC;0BAAA/D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5D,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,EAGLkC,UAAU,IAAIA,UAAU,CAACL,OAAO,CAACF,MAAM,GAAG,CAAC,gBACxCrE,OAAA;oBAAKsC,SAAS,EAAC,2DAA2D;oBAAAa,QAAA,gBACtEnD,OAAA;sBAAKsC,SAAS,EAAC,wCAAwC;sBAAAa,QAAA,gBACnDnD,OAAA;wBAAIsC,SAAS,EAAC,iCAAiC;wBAAAa,QAAA,EAAC;sBAAa;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAClE1C,OAAA;wBAAKsC,SAAS,EAAC,uBAAuB;wBAAAa,QAAA,GACjCyB,UAAU,CAACL,OAAO,CAACF,MAAM,EAAC,GAAC,EAACO,UAAU,CAACL,OAAO,CAACF,MAAM,KAAK,CAAC,GAAG,OAAO,GAAG,SAAS;sBAAA;wBAAA9B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACN1C,OAAA;sBAAKsC,SAAS,EAAC,WAAW;sBAAAa,QAAA,EACrByB,UAAU,CAACL,OAAO,CAACgB,GAAG,CAAC,CAAC7B,MAAM,EAAEkC,KAAK,kBAClC5F,OAAA;wBAAiBsC,SAAS,EAAG,kGACzBoB,MAAM,CAAC3B,MAAM,KAAK,SAAS,GAAG,wFAAwF,GACtH2B,MAAM,CAAC3B,MAAM,KAAK,QAAQ,GAAG,gFAAgF,GAC7G,oFACH,EAAE;wBAAAoB,QAAA,gBACCnD,OAAA;0BAAKsC,SAAS,EAAC,KAAK;0BAAAa,QAAA,eAChBnD,OAAA;4BAAKsC,SAAS,EAAC,mCAAmC;4BAAAa,QAAA,gBAC9CnD,OAAA;8BAAKsC,SAAS,EAAC,yBAAyB;8BAAAa,QAAA,gBACpCnD,OAAA;gCAAKsC,SAAS,EAAG,4BACboB,MAAM,CAAC3B,MAAM,KAAK,SAAS,GAAG,cAAc,GAC5C2B,MAAM,CAAC3B,MAAM,KAAK,QAAQ,GAAG,YAAY,GACzC,aACH,EAAE;gCAAAoB,QAAA,EACEd,aAAa,CAACqB,MAAM,CAAC3B,MAAM,CAAC6B,WAAW,CAAC,CAAC;8BAAC;gCAAArB,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC1C,CAAC,eACN1C,OAAA;gCAAAmD,QAAA,gBACInD,OAAA;kCAAIsC,SAAS,EAAC,iCAAiC;kCAAAa,QAAA,EAAEO,MAAM,CAAC5B;gCAAO;kCAAAS,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAK,CAAC,eACrE1C,OAAA;kCAAKsC,SAAS,EAAC,oDAAoD;kCAAAa,QAAA,gBAC/DnD,OAAA;oCAAMsC,SAAS,EAAC,yCAAyC;oCAAAa,QAAA,EACpDO,MAAM,CAACxB;kCAAM;oCAAAK,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACZ,CAAC,eACP1C,OAAA;oCAAMsC,SAAS,EAAC,yCAAyC;oCAAAa,QAAA,EACpDO,MAAM,CAACvB;kCAAK;oCAAAI,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACX,CAAC,EACNgB,MAAM,CAACtB,IAAI,KAAK,SAAS,iBACtBpC,OAAA;oCAAMsC,SAAS,EAAC,uEAAuE;oCAAAa,QAAA,EAAC;kCAExF;oCAAAZ,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAM,CACT;gCAAA;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACA,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACL,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACL,CAAC,eACN1C,OAAA;8BAAKsC,SAAS,EAAC,YAAY;8BAAAa,QAAA,eACvBnD,OAAA;gCAAMsC,SAAS,EAAG,gFACdoB,MAAM,CAAC3B,MAAM,KAAK,SAAS,GAAG,8CAA8C,GAC5E2B,MAAM,CAAC3B,MAAM,KAAK,QAAQ,GAAG,wCAAwC,GACrE,2CACH,EAAE;gCAAAoB,QAAA,EACEO,MAAM,CAAC3B;8BAAM;gCAAAQ,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACZ;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACN,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACL;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eAEN1C,OAAA;0BAAKsC,SAAS,EAAG,qCACboB,MAAM,CAAC3B,MAAM,KAAK,SAAS,GAAG,8CAA8C,GAC5E2B,MAAM,CAAC3B,MAAM,KAAK,QAAQ,GAAG,0CAA0C,GACvE,4CACH;wBAAE;0BAAAQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA,GAhDJkD,KAAK;wBAAArD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAiDV,CACR;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,GACNoC,QAAQ,gBACR9E,OAAA;oBAAKsC,SAAS,EAAC,+FAA+F;oBAAAa,QAAA,gBAC1GnD,OAAA;sBAAKsC,SAAS,EAAC,mHAAmH;sBAAAa,QAAA,eAC9HnD,OAAA,CAACd,QAAQ;wBAACoD,SAAS,EAAC;sBAAoB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1C,CAAC,eACN1C,OAAA;sBAAIsC,SAAS,EAAC,sCAAsC;sBAAAa,QAAA,EAAC;oBAAW;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACrE1C,OAAA;sBAAGsC,SAAS,EAAC,eAAe;sBAAAa,QAAA,EAAC;oBAA8C;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9E,CAAC,gBAEN1C,OAAA;oBAAKsC,SAAS,EAAC,8FAA8F;oBAAAa,QAAA,gBACzGnD,OAAA;sBAAKsC,SAAS,EAAC,kHAAkH;sBAAAa,QAAA,eAC7HnD,OAAA,CAACd,QAAQ;wBAACoD,SAAS,EAAC;sBAAoB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1C,CAAC,eACN1C,OAAA;sBAAIsC,SAAS,EAAC,sCAAsC;sBAAAa,QAAA,EAAC;oBAAU;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACpE1C,OAAA;sBAAGsC,SAAS,EAAC,eAAe;sBAAAa,QAAA,EAAC;oBAAwC;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxE,CACR;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAEd,CAAC,EAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAGN1C,OAAA;UAAKsC,SAAS,EAAC,2DAA2D;UAAAa,QAAA,gBACtEnD,OAAA;YAAIsC,SAAS,EAAC,8DAA8D;YAAAa,QAAA,gBACxEnD,OAAA,CAACP,KAAK;cAAC6C,SAAS,EAAC;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3C1C,OAAA;cAAAmD,QAAA,EAAM;YAAe;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACL1C,OAAA;YAAKsC,SAAS,EAAC,WAAW;YAAAa,QAAA,EACrBnC,cAAc,CAACY,cAAc,CAAC2D,GAAG,CAAC,CAACuC,QAAQ,EAAElC,KAAK,kBAC/C5F,OAAA;cAAiBsC,SAAS,EAAC,8FAA8F;cAAAa,QAAA,GACpHd,aAAa,CAACyF,QAAQ,CAAC/F,MAAM,CAAC,eAC/B/B,OAAA;gBAAKsC,SAAS,EAAC,QAAQ;gBAAAa,QAAA,gBACnBnD,OAAA;kBAAKsC,SAAS,EAAC,6BAA6B;kBAAAa,QAAA,EAAE2E,QAAQ,CAAChG;gBAAO;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrE1C,OAAA;kBAAKsC,SAAS,EAAC,uBAAuB;kBAAAa,QAAA,GAAE2E,QAAQ,CAACjG,IAAI,EAAC,UAAG,EAACiG,QAAQ,CAAC9F,IAAI;gBAAA;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC,eACN1C,OAAA;gBAAKsC,SAAS,EAAG;AACrD,sBAAsBwF,QAAQ,CAAC/F,MAAM,KAAK,SAAS,GAAG,8CAA8C,GACxD+F,QAAQ,CAAC/F,MAAM,KAAK,QAAQ,GAAG,wCAAwC,GACnE,iDACP,EAAE;gBAAAoB,QAAA,EACF2E,QAAQ,CAAC/F;cAAM;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA,GAZAkD,KAAK;cAAArD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaV,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACxC,EAAA,CAn8BID,cAAc;AAAA8H,EAAA,GAAd9H,cAAc;AAq8BpB,eAAeA,cAAc;;AAE7B;AACA,MAAM+H,cAAc,GAAI;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,IAAI,OAAO9C,QAAQ,KAAK,WAAW,EAAE;EACjC,MAAM+C,aAAa,GAAG/C,QAAQ,CAACgD,cAAc,CAAC,iBAAiB,CAAC;EAChE,IAAI,CAACD,aAAa,EAAE;IAChB,MAAME,YAAY,GAAGjD,QAAQ,CAACkD,aAAa,CAAC,OAAO,CAAC;IACpDD,YAAY,CAAC7C,EAAE,GAAG,iBAAiB;IACnC6C,YAAY,CAACE,WAAW,GAAGL,cAAc;IACzC9C,QAAQ,CAACoD,IAAI,CAACC,WAAW,CAACJ,YAAY,CAAC;EAC3C;AACJ;AAAC,IAAAJ,EAAA;AAAAS,YAAA,CAAAT,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
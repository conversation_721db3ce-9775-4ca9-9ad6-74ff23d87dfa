{"ast": null, "code": "/**\n * @license lucide-react v0.539.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M22 17a10 10 0 0 0-20 0\",\n  key: \"ozegv\"\n}], [\"path\", {\n  d: \"M6 17a6 6 0 0 1 12 0\",\n  key: \"5giftw\"\n}], [\"path\", {\n  d: \"M10 17a2 2 0 0 1 4 0\",\n  key: \"gnsikk\"\n}]];\nconst Rainbow = createLucideIcon(\"rainbow\", __iconNode);\nexport { __iconNode, Rainbow as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Rainbow", "createLucideIcon"], "sources": ["D:\\techvritti\\Collegemanagement\\frontend\\node_modules\\lucide-react\\src\\icons\\rainbow.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M22 17a10 10 0 0 0-20 0', key: 'ozegv' }],\n  ['path', { d: 'M6 17a6 6 0 0 1 12 0', key: '5giftw' }],\n  ['path', { d: 'M10 17a2 2 0 0 1 4 0', key: 'gnsikk' }],\n];\n\n/**\n * @component @name Rainbow\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjIgMTdhMTAgMTAgMCAwIDAtMjAgMCIgLz4KICA8cGF0aCBkPSJNNiAxN2E2IDYgMCAwIDEgMTIgMCIgLz4KICA8cGF0aCBkPSJNMTAgMTdhMiAyIDAgMCAxIDQgMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/rainbow\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Rainbow = createLucideIcon('rainbow', __iconNode);\n\nexport default Rainbow;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAA2BC,GAAA,EAAK;AAAA,CAAS,GACvD,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAwBC,GAAA,EAAK;AAAA,CAAU,GACrD,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAwBC,GAAA,EAAK;AAAA,CAAU,EACvD;AAaA,MAAMC,OAAA,GAAUC,gBAAA,CAAiB,WAAWJ,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
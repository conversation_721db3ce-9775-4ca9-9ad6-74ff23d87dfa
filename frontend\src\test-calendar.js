// Test script to verify calendar functionality
// This can be run in the browser console to test the calendar features

// Test data filtering
const testAttendanceData = [
    { date: '2024-01-15', subject: 'Mathematics', status: 'Present', course: 'Engineering', batch: 'Batch A', type: 'class' },
    { date: '2024-01-16', subject: 'Physics', status: 'Absent', course: 'Engineering', batch: 'Batch A', type: 'class' },
    { date: '2024-01-17', subject: 'Chemistry', status: 'Present', course: 'Science', batch: 'Batch A', type: 'class' },
    { date: '2024-01-20', subject: 'Holiday', status: 'Holiday', course: 'all', batch: 'all', type: 'holiday' },
];

// Test filter function
function testFilterFunction(records, course, batch, subject) {
    return records.filter(record => {
        const courseMatch = course === 'all' || record.course.toLowerCase() === course.toLowerCase();
        const batchMatch = batch === 'all' || record.batch.toLowerCase() === batch.toLowerCase();
        const subjectMatch = subject === 'all' || record.subject.toLowerCase() === subject.toLowerCase();
        
        return courseMatch && batchMatch && subjectMatch;
    });
}

// Test cases
console.log('Testing calendar filter functionality:');

// Test 1: Filter by Engineering course
const engineeringRecords = testFilterFunction(testAttendanceData, 'Engineering', 'all', 'all');
console.log('Engineering records:', engineeringRecords.length); // Should be 2

// Test 2: Filter by Science course
const scienceRecords = testFilterFunction(testAttendanceData, 'Science', 'all', 'all');
console.log('Science records:', scienceRecords.length); // Should be 1

// Test 3: Filter by Mathematics subject
const mathRecords = testFilterFunction(testAttendanceData, 'all', 'all', 'Mathematics');
console.log('Mathematics records:', mathRecords.length); // Should be 1

// Test 4: Filter by Batch A
const batchARecords = testFilterFunction(testAttendanceData, 'all', 'Batch A', 'all');
console.log('Batch A records:', batchARecords.length); // Should be 3

console.log('All tests completed!');

// Test attendance status calculation
function testAttendanceStatus(records) {
    const classRecords = records.filter(record => record.type === 'class');
    const presentCount = classRecords.filter(record => record.status === 'Present').length;
    const absentCount = classRecords.filter(record => record.status === 'Absent').length;
    const totalClasses = classRecords.length;
    const attendancePercentage = totalClasses > 0 ? Math.round((presentCount / totalClasses) * 100) : 0;
    
    return {
        present: presentCount,
        absent: absentCount,
        total: totalClasses,
        percentage: attendancePercentage
    };
}

// Test attendance calculation
const engineeringStats = testAttendanceStatus(engineeringRecords);
console.log('Engineering attendance stats:', engineeringStats);
// Should show: present: 1, absent: 1, total: 2, percentage: 50
